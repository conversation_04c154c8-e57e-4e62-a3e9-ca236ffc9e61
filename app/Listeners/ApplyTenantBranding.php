<?php

namespace App\Listeners;

use Illuminate\Support\Facades\Config;
use Stancl\Tenancy\Events\TenancyInitialized;

class ApplyTenantBranding
{
	public function handle(TenancyInitialized $event)
	{
		$tenant = $event->tenancy->tenant;
		$attributes = $tenant->getAttributes();

		// Set application name
		if (!empty($attributes['app_name'])) {
			Config::set('app.name', $attributes['app_name']);
		}

		// Set mail from address and name
		if (!empty($attributes['email_from_address'])) {
			Config::set('mail.from.address', $attributes['email_from_address']);
		}

		if (!empty($attributes['email_from_name'])) {
			Config::set('mail.from.name', $attributes['email_from_name']);
		}

		// Set tenant-specific favicon path
		if (!empty($attributes['favicon_path'])) {
			Config::set('app.tenant_favicon', $attributes['favicon_path']);
		} else {
			Config::set('app.tenant_favicon', null); // fallback or reset
		}
	}
}
