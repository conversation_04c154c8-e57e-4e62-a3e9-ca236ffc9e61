<?php

namespace App\Console\Commands;

use App\Models\Invoice;
use Illuminate\Console\Command;

class ExpireInvoices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'invoices:expire';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Expires invoices which have their due date in the past.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
		$invoices = Invoice::where('status_id', '=', Invoice::$statuses['sent'])
		                   ->where('due_date', '<', now())
		                   ->get();

		foreach ($invoices as $invoice) {
			$invoice->markAsExpired();
		}
    }
}
