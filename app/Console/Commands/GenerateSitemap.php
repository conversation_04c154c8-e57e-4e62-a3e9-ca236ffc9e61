<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Psr\Http\Message\UriInterface;
use Spatie\Sitemap\SitemapGenerator;
use Spatie\Sitemap\Tags\Url;

class GenerateSitemap extends Command
{
	/**
	 * The console command name.
	 *
	 * @var string
	 */
	protected $signature = 'sitemap:generate';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Generate the sitemap.';

	/**
	 * Execute the console command.
	 */
	public function handle()
	{
		SitemapGenerator::create(config('app.url'))
		                ->shouldCrawl(function (UriInterface $url) {
			                $excluded_paths = [
				                'document/create',
				                '/export',
				                '/redirect/to'
			                ];

			                foreach ($excluded_paths as $path) {
				                if (str_contains($url->getPath(), $path)) {
					                return false;
				                }
			                }

			                return true;
		                })
		                ->hasCrawled(function (Url $url) {
			                // remove trailing slash
			                $canonical_url = rtrim($url->url, '/');
			                // remove query parameters
			                $parsed_url = parse_url($canonical_url);
			                $clean_url = $parsed_url['scheme'] . '://' . $parsed_url['host'] . ($parsed_url['path'] ?? '');
			                return $url->setUrl($clean_url);
		                })
		                ->writeToFile(public_path('sitemap.xml'));

		return Command::SUCCESS;
	}
}
