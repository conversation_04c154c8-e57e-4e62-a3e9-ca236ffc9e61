<?php

namespace App\Console\Commands;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ClearUnverifiedUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clearUnverifiedUsers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clears unverified users.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
	public function handle()
	{
		// delete users who created their account more than 24 hours ago, but still haven't confirmed email address
		User::where('email_verified_at', null)
		    ->where('created_at', '<', Carbon::now()->subDay())
		    ->forceDelete();

		return Command::SUCCESS;
	}
}
