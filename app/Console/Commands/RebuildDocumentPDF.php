<?php

namespace App\Console\Commands;

use App\Models\Document;
use Exception;
use Illuminate\Console\Command;

class RebuildDocumentPDF extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'document:rebuild {id?*} {--all}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rebuilds and re-saves document PDF files.';

	/**
	 * If number of retrieved documents is larger than this number, prompt the user to confirm continuing
	 */
	const DOCUMENT_PROMPT_SIZE = 100;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
	    $ids = $this->argument('id');

		if($this->option('all')) {
			$documents = Document::where('is_visible', '=', 1)->get();
		} elseif(!empty($ids)) {
			$documents = Document::whereIn('id', $ids)->get();
		} else {
			$this->error('Argument "id" or "all" option is required.');
			return Command::FAILURE;
		}

		if($documents->count() > 0) {

			if(count($documents) > self::DOCUMENT_PROMPT_SIZE) {

				if (!$this->confirm("Document count is ". count($documents) . ", are you sure you want to continue?")) {
					$this->error('Aborted.');
				}
			}

			$this->withProgressBar($documents, function ($_document) {
				try{
					if(!$_document->saveHtml() || !$_document->savePdf()) {
						$this->error("Document $_document->id failed.");
					}
				} catch(Exception $e) {
					$this->error("Document $_document->id failed. Exception: ".$e->getTraceAsString());
				}
			});

			$this->newLine(2);
		} else {
			$this->error("Could not find any documents to process.");
		}

	    return Command::SUCCESS;

    }
}
