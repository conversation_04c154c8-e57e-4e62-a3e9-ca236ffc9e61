<?php

namespace App\Console\Commands;

use App\Models\FeedbackRequest;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Notification;
use Spatie\Activitylog\Models\Activity;

class SendFeedbackRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sendFeedbackRequests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Sends emails for feedback requests to users who haven't already received one.";

    /**
     * Execute the console command.
     *
     * @return int
     */
	public function handle(): int {

		// timeframe after document has been downloaded in which we solicit feedback from users
		$days_lower_threshold = 2;
		$days_upper_threshold = 4;

		// max emails per day
		$limit_emails_threshold = 50;

		// delay between each email
		$delay_in_seconds = 1;

		// get users who have downloaded a document recently
		$recent_active_users = Activity::select('causer_id')
		                               ->where('description', 'Download')
		                               ->whereBetween('created_at', [now()->subDays($days_upper_threshold), now()->subDays($days_lower_threshold)])
		                               ->whereNotNull('causer_id')
		                               ->get()->pluck('causer_id')->unique();

		// filter out users who already have a feedback/feedbackRequest
		$users = User::whereIn('id', $recent_active_users)
		             ->whereDoesntHave('feedback')
		             ->whereDoesntHave('feedbackRequest')
		             ->limit($limit_emails_threshold)
		             ->get();

		// dispatch emails
		foreach ($users as $_i => $_user) {

			// delay each email by $delay_in_seconds
			$delay = now()->addSeconds($_i * $delay_in_seconds);

			// send notification
			Notification::route('mail', $_user->email)->notify((new \App\Notifications\FeedbackRequest($_user))->delay($delay));

			// insert new FeedbackRequest
			FeedbackRequest::create([
				'user_id' => $_user->id
			]);

			// log the action
			$this->info("Sent feedback request to {$_user->email}.");
		}

		return Command::SUCCESS;
	}

}
