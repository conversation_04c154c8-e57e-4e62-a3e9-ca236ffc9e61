<?php
namespace App\Http\Requests;

use App\Rules\GoogleReCaptchaValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;
use Auth;
use <PERSON><PERSON><PERSON><PERSON><PERSON>ov\EmailSmtpValidation\Rules\EmailSmtpVerified;

class StoreContactRequest extends FormRequest
{
	public function authorize()
	{
		return true;
	}

	public function rules()
	{
		$rules = [
			'topic_id' => 'required',
			'email' => [
				'required',
				'string',
				'email:rfc,dns',
				'max:255'
			],
			'message' => 'required',
			'slike' => 'nullable|array|max:5',
			'slike.*' => 'file|mimes:jpeg,png,gif,webp,bmp|max:10240'
		];

		if (!\Auth::check()) {
			$rules['g-recaptcha-response'] = new GoogleReCaptchaValidationRule('contact');
		}

		if (Str::contains($this->get('email'), '@gmail.com')) {
			$rules['email'][] = app(EmailSmtpVerified::class);
		}

		if (Auth::guest()) {
			$rules['checkbox'] = 'required';
		}

		return $rules;
	}
}
