<?php

namespace App\Http\Middleware;

use Closure;

/**
 * Redirects all traffic to https on production and staging servers
 *
 * Class HttpsProtocol
 * @package App\Http\Middleware
 */
class HttpsProtocol {

	public function handle($request, Closure $next) {

		if (app()->environment(['production', 'staging']) && !$request->secure()) {
			return redirect()->secure($request->getRequestUri());
		}

		return $next($request);
	}
}
