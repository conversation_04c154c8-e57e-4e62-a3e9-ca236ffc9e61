<?php

namespace App\Http\Controllers\Auth;

use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\EmailSmtpValidation\Rules\EmailSmtpVerified;
use App\Http\Controllers\Controller;
use App\Models\EmailPreference;
use App\Models\User;
use App\Models\UserOptions;
use App\Rules\GoogleReCaptchaValidationRule;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Session;

class RegisterController extends Controller {

	/*
	|--------------------------------------------------------------------------
	| Register Controller
	|--------------------------------------------------------------------------
	|
	| This controller handles the registration of new users as well as their
	| validation and creation. By default this controller uses a trait to
	| provide this functionality without requiring any additional code.
	|
	*/

	use RegistersUsers;

	/**
	 * Where to redirect users after registration.
	 *
	 * @var string
	 */
	protected $redirectTo = '/';

	/**
	 * Create a new controller instance.
	 *
	 * @return void
	 */
	public function __construct() {

		$this->middleware('guest');
	}

	/**
	 * Get a validator for an incoming registration request.
	 *
	 * @param array $data
	 *
	 * @return \Illuminate\Contracts\Validation\Validator
	 */
	protected function validator(array $data) {

		$validate_params = [
			'name'     => 'required|string|max:255',
			'email' => [
				'required',
				'string',
				'email:rfc,dns',
				'max:255',
				'unique:users',
				'indisposable'
			],
			'password' => 'required|string|min:6|confirmed',
			'g-recaptcha-response' => [new GoogleReCaptchaValidationRule('register')]
		];

		// perform smtp validation on gmail address
		if(Str::contains(request()->get('email'), '@gmail.com')) {
			$validate_params['email'][] = app(EmailSmtpVerified::class);
		}

		return Validator::make($data, $validate_params);
	}

	/**
	 * Create a new user instance after a valid registration.
	 *
	 * @param array $data
	 *
	 * @return \App\Models\User
	 */
	protected function create(array $data) {

		if($user = User::create([
			'name'     => $data['name'],
			'email'    => $data['email'],
			'password' => Hash::make($data['password']),
		])) {
			EmailPreference::updateOrCreate(
				[
					'email' => $data['email']
				],
				[
					'general' => true,
					'newsletter' => isset($data['newsletter'])
				]
			);

			UserOptions::create([
				'user_id' => $user->id,
				'is_wizard_tutorial_shown' => \Cookie::has('wizardTutorialShown')
			]);

			return $user;
		}

		return null;
	}

	protected function redirectTo() {

		if(!Session::has('success')) {
			Session::flash('success', 'Uspješno ste izradili Pravomat račun.');
		}
		return route('documents');
	}
}
