<?php

namespace App\Http\Controllers;

use App\Models\Feedback;
use App\Models\User;
use App\Notifications\SendFeedback;
use App\Rules\GoogleReCaptchaValidationRule;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;

class FeedbackController extends Controller
{
	public function index() {

		return view('feedback.index');
	}

    public function store(Request $request) {

		$rules = [
			'message' => 'string|nullable|required_without:stars',
			'stars' => 'integer|nullable|required_without:message',
		];

	    if(!\Auth::check()) {
		    $rules['g-recaptcha-response'] = new GoogleReCaptchaValidationRule('feedback');
	    }

	    $request->validate($rules);

    	$feedback = Feedback::create([
			'view' => $request->get('view'),
		    'stars' => $request->get('stars'),
		    'message' => $request->get('message'),
		    'user_id' =>  Auth::check() ? Auth::id() : null,
		    'guest_id' => Auth::guest() ? User::guestID() : null,
	    ]);

    	// send notification
	    $notification = new SendFeedback($feedback);
	    $recipient = \App::isProduction() ? '<EMAIL>' : env('DEVELOPER_EMAIL');
	    Notification::route('mail', $recipient)->notify($notification);

        // log activity
        activity()->performedOn($feedback)->log('Feedback');

		return back()->with('success', 'Zahvaljujemo na povratnoj informaciji!');
    }

	public function facebook(Request $request) {

		$device = $this->detectDevice($request);

		activity()->withProperties(['device' => $device])->log('Facebook');

		if($device === 'iPhone') {
			return redirect()->away('https://m.facebook.com/pravomathr/reviews');
		} elseif($device === 'android') {
			return redirect()->away('https://m.facebook.com/pg/pravomathr');
		} else {
			return redirect()->away('https://www.facebook.com/pravomathr/reviews');
		}
	}

	private function detectDevice(Request $request)
	{
		$userAgent = $request->header('User-Agent');

		if (str_contains($userAgent, 'iPhone')) {
			return "iPhone";
		} elseif (str_contains($userAgent, 'Android')) {
			return "android";
		} else {
			return "other";
		}
	}
}
