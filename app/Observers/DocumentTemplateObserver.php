<?php

namespace App\Observers;

use App\Models\DocumentTemplate;

class DocumentTemplateObserver
{
    /**
     * Handle the DocumentTemplate "created" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
    public function created(DocumentTemplate $documentTemplate)
    {
        \Artisan::call('es:sync');
    }

    /**
     * Handle the DocumentTemplate "updated" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
    public function updated(DocumentTemplate $documentTemplate)
    {
        \Artisan::call('es:sync', [
            'index' => 'template',
            'id' => $documentTemplate->id
        ]);

        \Cache::tags(['template-search'])->flush();
    }

    /**
     * Handle the DocumentTemplate "saved" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
    public function saved(DocumentTemplate $documentTemplate)
    {
        \Artisan::call('es:sync');
    }

    /**
     * Handle the DocumentTemplate "deleted" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
	public function deleted(DocumentTemplate $documentTemplate)
    {
        \Artisan::call('es:sync');
    }

    /**
     * Handle the DocumentTemplate "restored" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
	public function restored(DocumentTemplate $documentTemplate)
    {
        \Artisan::call('es:sync');
    }

    /**
     * Handle the DocumentTemplate "force deleted" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
	public function forceDeleted(DocumentTemplate $documentTemplate)
    {
        \Artisan::call('es:sync');
    }
}
