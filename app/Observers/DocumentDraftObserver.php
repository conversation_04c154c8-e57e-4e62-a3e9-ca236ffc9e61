<?php

namespace App\Observers;

use App\Models\DocumentDraft;

class DocumentDraftObserver
{
    /**
     * Handle the DocumentDraft "created" event.
     *
     * @param DocumentDraft $draft
     *
     * @return void
     */
    public function created(DocumentDraft $draft)
    {
	    activity()->performedOn($draft)->withProperties([
		    'agent' => request()->userAgent()
	    ])->log('Create');

		// IMPORTANT - do not automatically create pdf because we might be mass creating draft models from documents!
    }

    /**
     * Handle the DocumentDraft "updated" event.
     *
     * @param DocumentDraft $draft
     *
     * @return void
     */
    public function updated(DocumentDraft $draft)
    {
	    activity()->performedOn($draft)->withProperties([
		    'agent' => request()->userAgent()
	    ])->log('Update');

		if($draft->isDirty('html')) {
			$draft->savePdf();
		}
    }

	public function updating(DocumentDraft $draft) {
		// set dirty flag if body html contains string "segment-dirty"
		if(!$draft->is_dirty && $draft->isDirty('html')) {
			$draft->is_dirty = $draft->hasDirtySegments();
		}
	}

    /**
     * Handle the DocumentDraft "deleted" event.
     *
     * @param DocumentDraft $draft
     *
     * @return void
     */
    public function deleted(DocumentDraft $draft)
    {
	    activity()->performedOn($draft)->log('Delete');
    }

    /**
     * Handle the DocumentDraft "restored" event.
     *
     * @param DocumentDraft $draft
     *
     * @return void
     */
	public function restored(DocumentDraft $draft)
    {
        //
    }

    /**
     * Handle the DocumentDraft "force deleted" event.
     *
     * @param DocumentDraft $draft
     *
     * @return void
     */
	public function forceDeleted(DocumentDraft $draft)
    {
        //
    }
}
