<?php

namespace App\Helpers\AmountToTextConverter\CurrencyTransformer;

use App\Helpers\AmountToTextConverter\Exception\NumberToWordsException;
use App\Helpers\AmountToTextConverter\Language\Croatian\CroatianDictionary;
use App\Helpers\AmountToTextConverter\Language\Croatian\CroatianExponentInflector;
use App\Helpers\AmountToTextConverter\Language\Croatian\CroatianNounGenderInflector;
use App\Helpers\AmountToTextConverter\Language\Croatian\CroatianTripletTransformer;
use App\Helpers\AmountToTextConverter\NumberTransformer\NumberTransformerBuilder;
use App\Helpers\AmountToTextConverter\Service\NumberToTripletsConverter;
use App\Helpers\AmountToTextConverter\TransformerOptions\CurrencyTransformerOptions;
use Illuminate\Support\Str;

class CroatianCurrencyTransformer implements CurrencyTransformer
{
    public function toWords(int $amount, string $currency, ?CurrencyTransformerOptions $options = null): string
    {
        $dictionary = new CroatianDictionary();
        $numberToTripletsConverter = new NumberToTripletsConverter();
        $tripletTransformer = new CroatianTripletTransformer($dictionary);
        $nounGenderInflector = new CroatianNounGenderInflector();
        $exponentInflector = new CroatianExponentInflector($nounGenderInflector);

        $numberTransformer = (new NumberTransformerBuilder())
            ->withDictionary($dictionary)
            ->withWordsSeparatedBy(' ')
            ->transformNumbersBySplittingIntoTriplets($numberToTripletsConverter, $tripletTransformer)
            ->inflectExponentByNumbers($exponentInflector)
            ->build();

        $decimal = (int) ($amount / 100);
        $fraction = abs($amount % 100);

        if ($fraction === 0) {
            $fraction = null;
        }

        $currency = strtoupper($currency);

        if (!array_key_exists($currency, CroatianDictionary::$currencyNames)) {
            throw new NumberToWordsException(
                sprintf('Currency "%s" is not available for "%s" language', $currency, get_class($this))
            );
        }

        $currencyNames = CroatianDictionary::$currencyNames[$currency];

        $words = [];

        $words[] = $numberTransformer->toWords($decimal);
        $words[] = $nounGenderInflector->inflectNounByNumber(
            $decimal,
            $currencyNames[0][0],
            $currencyNames[0][1],
            $currencyNames[0][2]
        );

	    $words[] = "i";

        if (null !== $fraction) {
            $words[] = $numberTransformer->toWords($fraction);
            $words[] = $nounGenderInflector->inflectNounByFractionNumber(
                $fraction,
                $currencyNames[1][0],
                $currencyNames[1][1],
                $currencyNames[1][2]
            );
        } else {
	        $words[] = "ništa centi";
        }


		$result = implode(' ', $words);

		// custom modifications
	    if(Str::startsWith($result, 'jedan ')) {
			// this must come before all other replacements
		    $result = Str::replaceFirst('jedan ', '', $result);
	    }

	    $result = str_replace('jedan tisuću', 'jedna tisuća', $result);
	    $result = str_replace('dva tisuće', 'dvije tisuće', $result);

	    $result = str_replace('jedan milijuna', 'jedan milijun', $result);

	    $result = str_replace('jedan miljarda', 'jedna miljarda', $result);
	    $result = str_replace('dva miljarde', 'dvije miljarde', $result);

	    $result = str_replace('jedan bilijuna', 'jedan bilijun', $result);

        return trim($result);
    }
}
