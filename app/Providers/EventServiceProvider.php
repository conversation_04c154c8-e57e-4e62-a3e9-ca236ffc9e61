<?php

namespace App\Providers;

use App\Listeners\EventListener;
use App\Listeners\SaveGuestID;
use App\Models\Document;
use App\Models\DocumentDraft;
use App\Models\DocumentTemplate;
use App\Models\DocumentTemplateSectionValue;
use App\Models\TranslationDocument;
use App\Models\Translation;
use App\Models\EmailPreference;
use App\Models\Invoice;
use App\Models\Receipt;
use App\Models\SignatureRequest;
use App\Models\User;
use App\Notifications\SendInvoice;
use App\Notifications\SendTranslationOrderConfirmation;
use App\Observers\DocumentDraftObserver;
use App\Observers\DocumentObserver;
use App\Observers\DocumentTemplateObserver;
use App\Observers\DocumentTemplateSectionValueObserver;
use App\Observers\TranslationDocumentObserver;
use App\Observers\TranslationObserver;
use App\Observers\EmailPreferenceObserver;
use App\Observers\InvoiceObserver;
use App\Observers\ReceiptObserver;
use App\Observers\SignatureRequestObserver;
use App\Observers\UserObserver;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Events\Verified;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use Illuminate\Notifications\Events\NotificationSent;
use App\Notifications\SendReceipt;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
	    Event::class => [
		    EventListener::class
	    ],
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        Verified::class => [
            SaveGuestID::class
        ],
        Login::class => [
            SaveGuestID::class
        ]
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

		User::observe(UserObserver::class);
	    Document::observe(DocumentObserver::class);
	    DocumentDraft::observe(DocumentDraftObserver::class);
		TranslationDocument::observe(TranslationDocumentObserver::class);
		Translation::observe(TranslationObserver::class);
        Receipt::observe(ReceiptObserver::class);
        Invoice::observe(InvoiceObserver::class);
		EmailPreference::observe(EmailPreferenceObserver::class);
        DocumentTemplate::observe(DocumentTemplateObserver::class);
		SignatureRequest::observe(SignatureRequestObserver::class);
		DocumentTemplateSectionValue::observe(DocumentTemplateSectionValueObserver::class);

		$this->registerEventLoggers();
    }

	/**
	 * Register event activity logging
	 * @return void
	 */
	private function registerEventLoggers(): void {

		Event::listen(Login::class, function($event) {
			activity()->causedBy($event->user)->withProperties([
				'ip' => request()->ip(),
				'agent' => request()->userAgent(),
				'guest_id' => User::guestID()
			])->log('Login');
		});

		Event::listen(Registered::class, function($event) {
			activity()->withProperties([
				'ip' => request()->ip(),
				'agent' => request()->userAgent(),
				'guest_id' => User::guestID()
			])->causedBy($event->user)->log('Registration');
		});

		Event::listen(Verified::class, function($event) {
			activity()->withProperties([
				'ip' => request()->ip(),
				'agent' => request()->userAgent(),
				'guest_id' => User::guestID()
			])->causedBy($event->user)->log('Verification');
		});

		Event::listen(NotificationSent::class, function (NotificationSent $event) {
			if ($event->notification instanceof SendReceipt) {
				// log order history
				activity()->performedOn($event->notification->receipt->receiptable)
				          ->withProperties(['type' => 'history'])
				          ->log(($event->notification->receipt->isStornoReceipt() ? 'Storno račun' : 'Račun') . ' poslan.');

				activity()->performedOn($event->notification->receipt)->log('Send');
				$event->notification->receipt->update([
					'status_id' => Receipt::$statuses['sent'],
                    'sent_at' => now()
				]);
			} elseif ($event->notification instanceof SendInvoice) {
				// log order history
				activity()->performedOn($event->notification->invoice->invoiceable)
				          ->withProperties(['type' => 'history'])
				          ->log('Ponuda poslana.');

				activity()->performedOn($event->notification->invoice)->log('Send');

                $event->notification->invoice->update([
                    'sent_at' => now()
                ]);
            } elseif($event->notification instanceof SendTranslationOrderConfirmation) {
				// log order history
				activity()->performedOn($event->notification->translation)
				          ->withProperties(['type' => 'history'])
				          ->log('Potvrda o narudžbi poslana.');
			}
		});

	}
}
