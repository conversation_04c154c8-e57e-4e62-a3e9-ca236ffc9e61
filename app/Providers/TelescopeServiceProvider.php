<?php

namespace App\Providers;

use <PERSON><PERSON>\Telescope\Telescope;
use Illuminate\Support\Facades\Gate;
use <PERSON><PERSON>\Telescope\IncomingEntry;
use <PERSON><PERSON>\Telescope\TelescopeApplicationServiceProvider;

class TelescopeServiceProvider extends TelescopeApplicationServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
	public function register()
	{
		Telescope::night();

		$this->hideSensitiveRequestDetails();

		Telescope::filter(function (IncomingEntry $entry) {
			if (config('app.env') === 'local') {
				return true; // Allow everything in the local environment
			}

			// record fiscalization logs only
			if ($entry->isLog() && isset($entry->content['message']) && $entry->content['message'] === 'Fiscalization') {
				return true;
			}

			return $entry->isReportableException() ||
			       $entry->isFailedJob() ||
			       $entry->isScheduledTask() ||
			       $entry->hasMonitoredTag();
		});
	}

    /**
     * Prevent sensitive request details from being logged by Telescope.
     *
     * @return void
     */
    protected function hideSensitiveRequestDetails()
    {
        if ($this->app->isLocal()) {
            return;
        }

        Telescope::hideRequestParameters(['_token']);

        Telescope::hideRequestHeaders([
            'cookie',
            'x-csrf-token',
            'x-xsrf-token',
        ]);
    }

    /**
     * Register the Telescope gate.
     *
     * This gate determines who can access Telescope in non-local environments.
     *
     * @return void
     */
    protected function gate()
    {
        Gate::define('viewTelescope', function ($user) {
            return $user->isAdmin();
        });
    }
}
