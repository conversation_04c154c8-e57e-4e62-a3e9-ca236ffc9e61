<?php

namespace App\Providers;

use App\Models\Document;
use App\Models\DocumentDraft;
use App\Policies\DocumentDraftPolicy;
use App\Policies\DocumentPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
	    Document::class      => DocumentPolicy::class,
	    DocumentDraft::class => DocumentDraftPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        //
    }
}
