<?php

namespace App\Models;

use App\Helpers\PriceCalculator;
use App\Helpers\StringHelper;
use App\Notifications\SendInvoice;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Notification;
use LaravelDaily\Invoices\Classes\Buyer;
use LaravelDaily\Invoices\Classes\InvoiceItem;
use Mtvs\EloquentHashids\HasHashid;
use Mtvs\EloquentHashids\HashidRouting;
use Spatie\Activitylog\Models\Activity;

class Invoice extends Model
{
    use HasFactory;

	// php artisan modelCache:clear
	use HasHashid, HashidRouting, SoftDeletes, Cachable;

	protected $fillable = [
		'status_id',
		'number',
		'title',
		'user_id',
		'invoiceable_id',
		'invoiceable_type',
		'customer_type',
		'name',
		'email',
		'address',
		'city',
		'postal_code',
		'country',
		'oib',
		'phone',
		'total',
		'vat',
		'shipping',
		'items',
		'sent_at',
		'due_date',
		'pdf'
	];

	public static $statuses = [
		'draft' => 0,
		'created' => 1,
		'sent' => 2,
		'paid' => 3,
		'expired' => 4
	];

	protected $casts = [
		'items' => 'array',
	];

	public function activities()
	{
		return $this->morphMany(Activity::class, 'subject');
	}

	public function user()
	{
		return $this->belongsTo(User::class);
	}

	public function invoiceable()
	{
		return $this->morphTo();
	}

	public static function getNextNumber() {

		$receipt_count = self::withTrashed()->whereYear('created_at', now()->year)
		                     ->count();

		$next_number = $receipt_count + 1;

		return $next_number . '/' . now()->year;
	}

	public function getPaymentLinkAttribute() {
		return route('translation.invoice.pay', $this);
	}

	public function saveOnDisk($data = []) {
		return $this->build($data)->save();
	}

	public function download() {
		return $this->build()->stream();
	}

	public function send() {
		if(!$this->sent_at) {
			$notification = new SendInvoice($this);
			Notification::route('mail', $this->user->email)->notify($notification);
		}

		return false;
	}

	private function build($data = []) {
		$buyer_data = [
			'name' => $this->name,
			'address' => $this->address . ", " . $this->postal_code . " " . $this->city . ", " . $this->country,
			'custom_fields' => [
				'E-pošta' => $this->email,
			],
		];

		if ($this->phone) {
			$buyer_data['phone'] = $this->phone;
		}

		if ($this->oib) {
			$buyer_data['custom_fields']['OIB'] = $this->oib;
		}

		$customer = new Buyer($buyer_data);

		$items = [];

		foreach ($this->items as $_item) {
			$items[] = InvoiceItem::make($_item['name'])
			                      ->units($_item['unit'])
			                      ->pricePerUnit($_item['unit_amount'])
			                      ->quantity($_item['quantity'])
			                      ->tax(PriceCalculator::calculateTax($_item['amount'], $_item['vat']));
		}

		$filename = str('invoices/' . $this->id . '/' . "Ponuda - " .
		                StringHelper::sanatizeStringForDownload($this->title))->beforeLast('.');

		$support_email = $this->invoiceable->getMorphClass() === Translation::class ? '<EMAIL>' : '<EMAIL>';

		$custom_data = [
			'invoice_title' => "PONUDA",
			'url' => $this->invoiceable->url(),
			'invoice_number' => $this->number,
			'invoice_time' => now()->parse($this->created_at)->format('d.m.Y. H:i'),
			'tax_rate' => (int)$this->vat . "%",
			'total_net' => PriceCalculator::calculateVPC($this->total, $this->vat),
			'place' => 'Rijeka',
			'invoice_due_date' => now()->parse($this->due_date)->format('d.m.Y.'),
			'support_email' => $support_email,
		];

		// add details for translation
		if($this->invoiceable instanceof Translation) {

			$custom_data['is_translation'] = true;

			$translation = $this->invoiceable;

			$details = [];

			if($translation->isCustom()) {
				if($translation->isCertified()) {
					if($translation->getBindingType() == 'scanned') {
						if($translation->getScannedDeliveryType() == 'post') {
							$details['Rok isporuke'] = $data['eta'];
							$details['Napomena'] = 'Navedeni rok isporuke odnosi se na predaju pošiljke dostavnoj službi. Točno vrijeme dostave ovisi isključivo o dostavnoj službi, za što ne preuzimamo odgovornost.';
							$details['Vrsta uvezivanja'] = 'Prijevod uvezan za skenirani dokument';
							$details['Način dostave'] = 'Dostava pošiljkom Paket24 na adresu u Hrvatskoj (besplatno)';
							$details['Adresa za dostavu'] = $translation->getFullShippingAddress();
						} else {
							$details['Rok isporuke'] = $data['eta'];
							$details['Vrsta uvezivanja'] = 'Prijevod uvezan za skenirani dokument';
							$details['Način dostave'] = $translation->getScannedDeliveryType() == 'zagreb' ?
								'Osobno preuzimanje u Zagrebu' :
								'Osobno preuzimanje u Rijeci';
						}
					} else {
						$details['Rok isporuke'] = $data['eta'];
						$details['Vrsta uvezivanja'] = 'Prijevod uvezan za izvornik dokumenta';

						$details['Način dostave'] = $translation->getOriginalDeliveryType() == 'zagreb' ?
							'Osobno preuzimanje u Zagrebu' :
							'Osobno preuzimanje u Rijeci';
					}

				} else {
					$details['Način dostave'] = 'E-pošta';
					$details['Rok isporuke'] = $data['eta'];
				}
			} else {
				$details['Način dostave'] = 'E-pošta';
				$details['Rok isporuke'] = $data['eta'];
			}

			$custom_data['details'] = $details;
		}

		$invoice = \LaravelDaily\Invoices\Invoice::make('Račun')
										 ->template('invoice')
						                 ->logo(public_path('favicon.png'))
						                 ->buyer($customer)
						                 ->addItems($items)
						                 ->filename($filename)
						                 ->name("Ponuda " . $this->title)
						                 ->setCustomData($custom_data);

		if($this->shipping !== null) {
			$invoice = $invoice->shipping($this->shipping);
		}

		return $invoice;
	}

	public function hasExpired() {
		return now()->greaterThan($this->due_date);
	}

	public function markAsExpired() {

		$this->update([
			'status_id' => self::$statuses['expired'],
		]);

		if($this->invoiceable instanceof Translation) {
			if($this->invoiceable->status_id === Translation::$statuses['invoiced']) {
				$this->invoiceable->update([
					'status_id' => Translation::$statuses['invoice_expired'],
				]);

				// log order history
				activity()->performedOn($this->invoiceable)->withProperties(['type' => 'history'])->log('Ponuda istekla.');
			}
		}
	}
}
