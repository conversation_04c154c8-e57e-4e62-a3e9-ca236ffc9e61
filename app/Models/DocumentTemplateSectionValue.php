<?php

namespace App\Models;

use App\Traits\Encryptable;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Model;

class DocumentTemplateSectionValue extends Model {

	use Encryptable, Cachable;

	protected $touches = ['document'];
	protected $fillable = ['document_template_section_id', 'document_id'];

	use Encryptable;

	protected $encryptable = [
		'data'
	];

	public function document() {

		return $this->belongsTo('App\Models\Document');
	}

	public function section() {

		return $this->belongsTo('App\Models\DocumentTemplateSection', 'document_template_section_id', 'id');
	}

}
