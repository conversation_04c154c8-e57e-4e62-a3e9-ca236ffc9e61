<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SearchLog extends Model {

	use HasFactory;

	const UPDATED_AT = null;    // disable auto filling updated_at field

	/**
	 * +     * Creates new record with given query
	 * +     * @param $query
	 * +     * @return bool
	 * +     */
	public static function create($query) {

		$log        = new self();
		$log->query = $query;

		return $log->save();
	}
}
