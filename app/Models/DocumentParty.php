<?php

namespace App\Models;

use App\Traits\Encryptable;
use Illuminate\Database\Eloquent\Model;
use Mtvs\EloquentHashids\HasHashid;
use Mtvs\EloquentHashids\HashidRouting;

class DocumentParty extends Model {

	use Encryptable, HasHashid, HashidRouting;

	protected $fillable = ['document_id', 'name', 'label', 'side', 'signature', 'is_signee'];
	protected $encryptable = [
		'name',
		'label'
	];

	public function document() {

		return $this->belongsTo('App\Models\Document');
	}

	public function signatureRequest() {

		return $this->hasOne('App\Models\SignatureRequest', 'party_id');
	}
}
