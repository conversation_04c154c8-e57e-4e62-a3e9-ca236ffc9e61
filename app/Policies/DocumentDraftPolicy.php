<?php

namespace App\Policies;

use App\Models\DocumentDraft;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class DocumentDraftPolicy
{
    use HandlesAuthorization;


	/**
	 * Determine whether the user can update the model.
	 *
	 * @param User $user
	 * @param DocumentDraft $documentDraft
	 *
	 * @return bool
	 */
    public function update(User $user, DocumentDraft $draft): bool {
        return $draft->user_id === $user->id;
    }

}
