<?php

namespace App\Notifications;

use App\Exceptions\SkipNotificationException;
use App\Models\EmailPreference;
use Illuminate\Auth\Notifications\ResetPassword as ResetPasswordNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class ResetPassword extends ResetPasswordNotification implements ShouldQueue
{
    use Queueable;

    /**
     * ResetPassword constructor.
     * @param $token
     */
    public function __construct($token)
    {
        $this->token = $token;
    }

	/**
	 * Get the notification's delivery channels.
	 *
	 * @param  mixed  $notifiable
	 * @return array
	 */
	public function via(mixed $notifiable): array
	{
		return ['mail'];
	}

	public function shouldSkip($notifiable)
	{
		return !EmailPreference::isAllowedGeneralEmails($notifiable->getEmailForPasswordReset());
	}

    /**
     * Build Mail Message
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
	    if($this->shouldSkip($notifiable)) {
		    throw new SkipNotificationException();
	    }

        return (new MailMessage)
            ->subject('Promjena lozinke - Pravomat.hr')
            ->line('Za promjenu lozinke, klikni na poveznicu ispod.')
            ->action('Promjena lozinke', url('password/reset', $this->token))
            ->line('Vrijeme za ponovno postavljanje lozinke ističe za 60 minuta i tada poveznica više neće biti aktivna.
            Ako niste zatražili ponovno postavljanje lozinke, slobodno ignorirajte ovaj e-mail.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
