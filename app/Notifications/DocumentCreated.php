<?php

namespace App\Notifications;

use App\Exceptions\SkipNotificationException;
use App\Models\Document;
use App\Models\EmailPreference;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\URL;

class DocumentCreated extends Notification implements ShouldQueue
{
    use Queueable;

	private Document $document;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Document $document)
    {
        $this->document = $document;
    }

	/**
	 * Get the notification's delivery channels.
	 *
	 * @param  mixed  $notifiable
	 * @return array
	 */
	public function via($notifiable): array
	{
		return ['mail'];
	}

	public function shouldSkip($notifiable)
	{
		return !$this->document || !$this->document->user || !EmailPreference::isAllowedGeneralEmails($this->document->user->email);
	}

    /**
     * Build Mail Message
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
	    if($this->shouldSkip($notifiable)) {
		    throw new SkipNotificationException();
	    }

		$content = "Uspješno ste izradili dokument: {$this->document->template->public_title}.";

		$message = (new MailMessage)
		    ->subject("Uspješno ste izradili dokument: ". $this->document->template->public_title)
			->greeting('Pozdrav '.$this->document->user->name.",")
		    ->line($content)
		    ->action('Preuzmi dokument', URL::signedRoute('document.download', $this->document));

	    $message->viewData = ['email_preferences_link' => URL::signedRoute('email.preferences.show', ['email' => $this->document->user->email])];

	    return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
