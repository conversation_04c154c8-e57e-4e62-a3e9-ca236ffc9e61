<?php

namespace App\Notifications;

use App\Exceptions\SkipNotificationException;
use App\Models\Translation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TranslationCancelled extends Notification implements ShouldQueue
{
    use Queueable;

	private Translation $translation;
	/**
	 * Create a new notification instance.
	 */
	public function __construct(Translation $translation)
	{
		$this->translation = $translation;
	}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
	public function via($notifiable): array
	{
		return ['mail'];
	}

    /**
     * Build Mail Message
     */
    public function toMail($notifiable)
    {
	    $message = (new MailMessage)
					->subject('Narud<PERSON><PERSON> otkazana')
                    ->line('Korisnik ' . $this->translation->user->name . ' je otkazao prijevod dokumenta')
                    ->action('Pogledaj narudžbu', route('translation.show.admin', $this->translation))
                    ->salutation('none');

		return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
