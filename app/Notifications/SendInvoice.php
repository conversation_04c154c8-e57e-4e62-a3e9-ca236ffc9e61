<?php

namespace App\Notifications;

use App\Exceptions\SkipNotificationException;
use App\Models\EmailPreference;
use App\Models\Invoice;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;

class SendInvoice extends Notification implements ShouldQueue
{
	use Queueable;

	public Invoice $invoice;

	/**
	 * Create a new notification instance.
	 */
	public function __construct(Invoice $invoice)
	{
		$this->invoice = $invoice;
	}

	/**
	 * Get the notification's delivery channels.
	 *
	 * @return array<int, string>
	 */
	public function via($notifiable): array
	{
		return ['mail'];
	}

	public function shouldSkip($notifiable)
	{
		return !$this->invoice->user || !EmailPreference::isAllowedGeneralEmails($this->invoice->user->email);
	}

	/**
	 * Build Mail Message
	 */
	public function toMail($notifiable)
	{
		if($this->shouldSkip($notifiable)) {
			throw new SkipNotificationException();
		}

		$file_path = ltrim(str_replace('/storage', '', $this->invoice->pdf), '/');

		if (Storage::exists($file_path)) {

			$message = (new MailMessage)
				->subject('Ponuda')
				->greeting('Pozdrav '.$this->invoice->user->name.",")
				->line("U privitku se nalazi ponuda za narudžbu:")
				->line("<i>" . $this->invoice->invoiceable->full_title . "</i>")
				->line('Za prihvat ove ponude i narudžbu prijevoda, molimo da najkasnije do ' . now()->parse($this->invoice->due_date)->format('d.m.Y.') . '  izvršite uplatu putem poveznice: ')
				->action('Online plaćanje', route('translation.show', $this->invoice->invoiceable));

			$message->attach(Storage::path($file_path), [
				'as' => basename($file_path),
				'mime' => Storage::mimeType($file_path),
			]);

			$message->viewData = ['email_preferences_link' => URL::signedRoute('email.preferences.show', ['email' => $this->invoice->user->email])];

		} else {
			throw new \Exception('File not found: ' . $file_path. ', invoice id: ' . $this->invoice->id);
		}

		return $message;
	}

	/**
	 * Get the array representation of the notification.
	 *
	 * @return array<string, mixed>
	 */
	public function toArray($notifiable): array
	{
		return [
			//
		];
	}
}
