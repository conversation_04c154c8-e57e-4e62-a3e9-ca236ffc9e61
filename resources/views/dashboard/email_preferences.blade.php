@extends('layouts.common.master')
@section('title', 'Postavke obavijesti')

@section('content')
    <h1 class="pb-4">Postavke obavijesti</h1>

    <div class="pb-4">
        Adresa e-pošte: <i> {{ $preferences->email }} </i> @auth <a href="{{ route('account.personal.data') }}"><small>(uredi)</small></a> @endauth
    </div>

    <form method="post" action="{{ URL::signedRoute('email.preferences.store', ['email' => $preferences->email]) }}">
        @csrf


        <div class="row">
            <div class="col-lg-12">
                <div class="form-check">
                    <input @if($preferences->general) checked @endif name="general" class="form-check-input" type="checkbox" value="1" id="general" @if(!$guest) disabled @endif>
                    <label class="form-check-label" for="general">
                        <PERSON><PERSON>m od Pravomata primati osnovne obavijesti @if($guest) <strong> - isključivanjem ove opcije nećete moći primati osnovnu e-poštu (dokumente, zahtjeve za e-potpisima i druge obavijesti) od Pravomata dok je ponovo ne uključite</strong> @endif
                    </label>
                </div>
            </div>
        </div>


        <div class="row mt-3">
            <div class="col-lg-12">
                <div class="form-check">
                    <input @if($preferences->newsletter) checked @endif name="newsletter" class="form-check-input" type="checkbox" value="1" id="newsletter">
                    <label class="form-check-label" for="newsletter">
                        Želim od Pravomata povremeno primati poruke s novostima
                    </label>
                </div>
            </div>
        </div>

        @if($guest)
            <div class="row mt-3">
                <div class="col-lg-12">
                    <div class="form-check">
                        <input @if(!$preferences->general && !$preferences->newsletter) checked @endif class="form-check-input" type="checkbox" value="1" id="none">
                        <label class="form-check-label" for="none">
                            Ne želim od Pravomata primati nikakve obavijesti
                        </label>
                    </div>
                </div>
            </div>
        @endif

        <div class="row mt-3">
            <div class="col-lg-12">
                <input type="submit" value="Spremi postavke" class="btn btn-info">
            </div>
        </div>

    </form>

@endsection

@push('scripts')
    <script type="text/javascript">
        $(function() {

            $('#general').on('click', function(e){
                $('#none').prop('checked', false);
            })

            $('#newsletter').on('click', function(e){
                $('#none').prop('checked', false);
            })

            $('#none').on('click', function(e){
                if(e.target.checked) {
                    $('#general').prop('checked', false);
                    $('#newsletter').prop('checked', false);
                }
            })
        });
    </script>
@endpush
