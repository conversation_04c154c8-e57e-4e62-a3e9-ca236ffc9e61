@extends('layouts.common.master')
@section('title', 'Potpisi')

@section('content')
    <h1>Potpisi - <small>{{ $document->title }} ({{ $document->signedParties->count() }}/{{ $document->parties->count() }})</small>
        <span class="float-lg-right">
            <a target="_blank" class="btn btn-info d-none d-lg-inline-block" href="{{ \Illuminate\Support\Facades\URL::signedRoute('document.preview.image', $document) }}">Pregledaj dokument</a>
        </span>

    </h1>

    @if($document->signeeParties->count())

        <div class="table-responsive">
            <table class="table table-bordered mt-3">
                <thead>
                <tr>
                    <td>#</td>
                    <td>Stranka</td>
                    <td class="text-center">Potpis</td>
                    <td>Potpisnik</td>
                    <td>Vrijeme potpisa</td>
                </tr>
                </thead>
                <tbody>
                @foreach($document->signeeParties as $_i => $_party)
                    <tr>
                        <td>{{$_i+1}}</td>
                        <td>
                            {!! $_party->label !!}

                        </td>
                        <td class="text-center">
                            @if(!empty($_party->signature))
                                <img alt="signature" src="{{ \Illuminate\Support\Facades\URL::signedRoute('signature.image', $_party) }}">
                                <hr/>
                            @elseif($_party->signatureRequest)
                                <div class="row">
                                    <div class="col-lg-12">
                                        Zahtjev za e-Potpisom poslan je na adresu e-pošte: {{ $_party->signatureRequest->email }}
                                    </div>
                                    @if($_party->signatureRequest->isExpired())
                                        <div class="col-lg-12">
                                            <strong>
                                                Vrijeme za potpis je isteklo.
                                            </strong>
                                        </div>
                                    @endif
                                    <div class="col-lg-12">
                                        <hr/>
                                        <form method="post" action="{{ \Illuminate\Support\Facades\URL::signedRoute('signature.request.revoke', [$document, $_party->signatureRequest]) }}">
                                            @csrf
                                            <input type="submit" value="Otkaži zahtjev" class="btn btn-secondary please-confirm" data-message="Jeste li sigurni da želite otkazati zahtjev za e-Potpisom?">
                                        </form>
                                    </div>
                                </div>
                            @else
                                <div class="dropdown">
                                    <button class="btn btn-info dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        Odaberi
                                    </button>
                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                        <a class="dropdown-item" href="{{ \Illuminate\Support\Facades\URL::signedRoute('signature.show', $_party) }}">Dodaj e-Potpis</a>
                                        <a class="dropdown-item" href="{{ \Illuminate\Support\Facades\URL::signedRoute('signature.request', [$document, $_party]) }}">Zatraži e-Potpis</a>
                                    </div>
                                </div>
                            @endif
                        </td>
                        <td>
                            @if(!empty($_party->signature) && !empty($_party->signed_at))
                                @if($_party->signatureRequest)
                                    {{ $_party->signatureRequest->email }}
                                @else
                                    {{ Auth::user()->email }}
                                @endif

                            @endif
                        </td>
                        <td>
                            @if(!empty($_party->signature) && !empty($_party->signed_at))
                                {{ date('d.m.Y, H:i', strtotime($_party->signed_at)) }}
                            @endif
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>

    @else
        <div class="alert alert-danger mt-3"> Nisu uneseni podaci o strankama. </div>
        <a href="{{ route('documents') }}" class="btn btn-info">Povratak</a>
    @endif

@endsection
