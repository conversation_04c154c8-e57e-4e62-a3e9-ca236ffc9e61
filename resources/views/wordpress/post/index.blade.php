@extends('layouts.common.master')
@section('title', 'Stručni članci')
@section('description', 'Saznaj sve informacije koje te zanimaju vezano za ugovore o radu, kupoprodajne ugovore, ugovore o najmu, itd.')

@section('content')
    <h1 class="pb-4">Struč<PERSON> članci</h1>
    @if(!empty($posts) && count($posts))
        <div class="container">
        @foreach($posts as $_post)
            <div class="article_item row mb-4 p-4">
                    @if(!empty($_post->image))
                        <div class="col-lg-8">
                            <h4 class="pt-2"><a style="text-decoration: none !important" class="text-dark"
                                                href="{{ route('wordpress.post', $_post->slug) }}">{{ $_post->title }}
                                </a></h4>
                            <p class="pb-3">
                                {!! StringHelper::generateExcerpt($_post->acf->excerpt) !!}
                            </p>
                            <p>
                                <a href="{{ route('wordpress.post', $_post->slug) }}" class="btn btn-info">Pročitaj
                                    više</a>
                            </p>
                        </div>
                        <div class="col-lg-4 pb-4 d-none d-lg-block d-lg-none">
                            <a href="{{ route('wordpress.post', $_post->slug) }}">
                                <img style="border-radius: 5px;" height="200" width="300" src="{{$_post->image}}">
                            </a>
                        </div>
                    @else
                        <div class="col-lg-12">
                            <h4 class="pt-2"><a style="text-decoration: none !important" class="text-dark"
                                                href="{{ route('wordpress.post', $_post->slug) }}">{{ $_post->title }}
                                </a></h4>
                            <p class="pb-3">
                                {!! StringHelper::generateExcerpt($_post->acf->excerpt) !!}
                            </p>
                            <p>
                                <a href="{{ route('wordpress.post', $_post->slug) }}" class="btn btn-info">Pročitaj
                                    više</a>
                            </p>
                        </div>
                    @endif
            </div>
        @endforeach
        </div>

        {!! $pagination !!}

    @else
        <p>
            Nema postova.
        </p>
    @endif
@endsection
