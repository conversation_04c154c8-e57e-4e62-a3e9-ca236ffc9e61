@extends('layouts.common.master')
@section('title', $document->post_title)
@section('description', $document->acf->excerpt)
@section('keywords', $document->acf->tags)

@section('content')
    <div class="row">
        <div class="col">
            <div class="row">
                <div class="col-lg-12">
                    <h1 class="pb-4">{{ $document->post_title }}</h1>
                    <div class="pb-4 d-xl-none d-lg-none">
                        @if(!empty($widget))
                            <div class="card">
                                <div class="card-body">
                                    <div class="nav flex-column">
                                        <div class="pb-2">
                                            <a class="nav-link btn btn-block btn-info" href="{{ route('document.create', ['template_id' => $widget['template_id']]) }}">Izradi dokument</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                    <div class="site-content">
                        {!! StringHelper::wordpressContent($document->post_content) !!}
                    </div>
                </div>
            </div>
        </div>
        <div class="bg-transparent col-lg-3 pl-lg-5">
            <div class="py-2 sticky-top sticky-offset">
                @if(!empty($widget))
                    <div class="card">
                        <div class="card-header">
                            {{ $widget['document_template_public_title'] }}
                        </div>
                        <div class="card-body">
                            <div class="nav flex-column">

                                <div class="pb-2">
                                    <a class="nav-link btn btn-block btn-info" href="{{ route('document.create', ['template_id' => $widget['template_id']]) }}">Izradi dokument</a>
                                </div>

                                <div class="document-details">
                                    <span class="bold">Zadnja izmjena:</span> {{ date('d.m.Y.', strtotime($widget['updated_at'])) }} <br/>
                                    <span class="bold">Vrijeme izrade:</span> < {{ $widget['time_to_fill'] }} minuta <br/>
                                    <span class="bold">Format:</span> {{ $widget['format'] }} <br/>
                                    <span class="bold">Autor:</span> <a href="{{ $widget['author_page'] }}">{{ $widget['author'] }}</a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection
