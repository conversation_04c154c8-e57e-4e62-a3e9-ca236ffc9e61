@extends('layouts.example.footerless')
@section('title', $example->post_title)
@section('description', $example->acf->excerpt)
@section('keywords', $example->acf->tags)

@push('styles')
    <style media="all">

        .article-header{
            font-weight: bold;
            margin-bottom: 10px!important;
            margin-top: 10px!important;
        }

        @media (max-width: 768px) {
            .signature-line {
                font-size: 0.3em!important;
            }

            .editable-segment{
                text-align: start;
                text-justify: auto;
            }
        }

        .signature-line {
            font-size: 0.85em;
        }

        .example-preview{
            border: 1px solid rgba(186, 186, 186, 0.32)
        }

        .editable-segment{
            padding: 20px;
        }

        #supplementaries-container{
            padding: 20px;
        }

    </style>
@endpush

@section('content')
    <div class="row">
        <div class="col">
            <div class="row">
                <div class="col-lg-12">
                    <h1 class="pb-4">{!! $example->post_title !!}</h1>
                    <div class="pb-4 d-xl-none d-lg-none">

                    </div>

                    <div class="alert alert-secondary d-flex align-items-center" role="alert">
                        {!! $example->acf->excerpt !!}
                    </div>

                    <div class="site-content example-preview mt-4 p-4">
                        <div id="full-content">
                            {!! $example->html !!}
                        </div>
                    </div>

                    <div class="site-content example-preview mt-4 p-4 fading-content">
                        <div id="fading-content">
                            {!! $example->html !!}
                        </div>
                    </div>
                    <div class="fadeout"></div>
                </div>
            </div>
        </div>
        <div class="bg-transparent col-lg-3 pl-lg-5">
            <div class="py-2 sticky-top sticky-offset">

            </div>
        </div>
    </div>
@endsection
