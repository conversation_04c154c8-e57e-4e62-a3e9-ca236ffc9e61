<!DOCTYPE html>

<html lang="hr">

<head>
<meta name="robots" content="noindex,nofollow">
@include('layouts.document.partials.head',
    ['public_title' => 'Izrada dokumenta: '.$model->document->template->public_title.' - Pravomat', 'title' => $model->document->template->title])
</head>

<body>
<div id="layout-container">

    @include('layouts.common.partials.nav')


    <!-- Intro modal -->
    @if($should_show_wizard_tutorial)
        <div class="modal fade" id="wizardTutorialModal" tabindex="-1" role="dialog" aria-labelledby="wizardTutorialModalTitle"
             aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="wizardTutorialLongTitle">Upute za ispunjavanje obrasca</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                       <p>
                           Odabrali ste obrazac za izradu dokumenta: <strong>{{ $model->document->template->public_title }}.</strong>
                       </p>
                        <p>
                            S lijeve strane nalazi se <strong>upitnik</strong> s raznim poljima za upis i odabir. Preporučamo da pažljivo pročitate pitanja i ispunite sve potrebne podatke kako bi Vaš dokument bio ispravan.
                        </p>
                        <p>
                            S desne strane nalazi se <strong>preglednik</strong> dokumenta koji se automatski ažurira dok unosite i mijenjate podatke.
                        </p>
                        <p>
                            Obrazac se sastoji od nekoliko koraka, a klikom na <strong>"Spremi i nastavi"</strong> pri dnu svakog koraka spremaju se uneseni podaci. Po završetku možete preuzeti svoj gotovi dokument u PDF formatu, te ga pregledati i naknadno urediti u Uređivaču.
                        </p>
                        <p>
                            Želimo Vam ugodan rad!
                        </p>
                    </div>
                    <div class="modal-footer">
                        <a href="" data-dismiss="modal" class="btn btn-info">Razumijem</a>
                    </div>
                </div>
            </div>
        </div>
    @endif


    <!-- Save modal -->
    <div class="modal fade" id="saveChangesModal" tabindex="-1" role="dialog" aria-labelledby="saveChangesModalTitle"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="saveChangesLongTitle">Spremi promjene</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>
                        Imate nespremljene promjene. Želite li spremiti promjene?
                    </p>
                    <input id="intended-exit-page" type="hidden" value="">
                </div>
                <div class="modal-footer">
                    <a href="" class="btn btn-secondary" id="continue-without-saving">Odbaci i nastavi</a>
                    <a href="" class="btn btn-info" id="save-and-continue">Spremi i nastavi</a>
                </div>
            </div>
        </div>
    </div>

    <div id="layout-content">
        <div class="container margin-top">
            @if (session('success'))
                <div class="alert alert-success">
                    {!! session('success') !!}
                </div>
            @endif
            @if (session('error'))
                <div class="alert alert-danger">
                    {!! session('error') !!}
                </div>
            @endif
            @if (isset($errors) && $errors->any())
                <div class="alert alert-danger">
                    @if($errors->has('override'))
                        {{ collect($errors->get('override'))->first() }}
                    @else
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    @endif

                </div>
            @endif
            <div class="row">
                <div class="col-lg-12">
                    <h1>
                        {{ $model->document->template->public_title }}
                        <span class="mobile-content"><small> - {{ $model->section->title }}</small></span>
                    </h1>
                </div>

                <div id="nav-container" class="@if($enable_document_preview) col-lg-12 section-navbar-sticky @else col-lg-9 @endif pt-3 pb-2">
                    <div class="card" style="border-radius:0;">
                        <div class="card-header p-0">
                            <nav class="p-0 navbar navbar-expand-sm navbar-light bg-light">
                                <div class="collapse navbar-collapse" id="sectionNavbar">
                                    <ul class="navbar-nav mr-auto text-center">
                                        @foreach($model->document->template->sections as $_section)
                                            <li style="width:{!! 100/count($model->document->template->sections) !!}%;" class="nav-item {!! $_section->id == $model->section->id ? 'active' : '' !!}">
                                                <a class="nav-link" href="{!! route('section.show', [$model->document, $_section]) !!}">
                                                    {!! $_section->nav_title !!}
                                                    @if($_section->showMissingDataWarning($model->document->sectionValues->where('document_template_section_id', $_section->id)->first(), $model->document->is_visible, $model->section->order_index))
                                                        <span data-toggle="tooltip" data-original-title="Provjerite i potvrdite unos svih obaveznih podataka.">
                                                            <i style="color: #ffac00" class="fa fa-exclamation-circle" aria-hidden="true"></i>
                                                        </span>
                                                    @endif
                                                </a>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </nav>

                            <nav class="navbar navbar-light bg-light visible-xs">
                                <a class="mobile-navbar-brand" href="#"><small>{{ $model->section->title }}</small></a>
                                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                                    <span class="navbar-toggler-icon"></span>
                                </button>
                                <div class="collapse navbar-collapse" id="navbarNav">
                                    <ul class="navbar-nav">
                                        @foreach($model->document->template->sections as $_section)
                                            <li class="nav-item {!! $_section->id == $model->section->id ? 'active' : '' !!}">
                                                <a class="nav-link" href="{!! route('section.show', [$model->document, $_section]) !!}">{!! $_section->nav_title !!}</a>
                                            </li>
                                        @endforeach

                                    </ul>
                                </div>
                            </nav>
                        </div>
                        <div class="progress" style="border-radius:0;">
                            <div class="progress-bar bg-info" role="progressbar" style="width: {{ (int)($model->section->order_index / count($model->document->template->sections) * 100) }}%;" aria-valuenow="{{ (int)($model->section->order_index / count($model->document->template->sections) * 100) }}" aria-valuemin="0" aria-valuemax="100">{{ (int)($model->section->order_index / count($model->document->template->sections) * 100) }}%</div>
                        </div>
                    </div>
                </div>
                <div id="javascript-error-container" class="@if($enable_document_preview) col-lg-12 @else col-lg-9 @endif pt-2 d-none">
                    <div id="javascript-error" class="alert alert-danger">

                    </div>
                </div>
                <div id="section-content" data-preview="{{ $enable_document_preview }}" class="col-lg-12" style="display: none;">
                    <div class="row">
                        <div id="form-container" data-missing="{{ !empty($model->missing_data) ? $model->missing_data : "[]" }}" class="@if($enable_document_preview) col-lg-4 @else col-lg-9 @endif">
                            @yield('content')
                            @include('documents.partials.form_footer')
                        </div>
                        <div id="preview-sidebar" class="d-none @if($enable_document_preview) d-lg-block @endif col-lg-8 pl-lg-2" style="margin-bottom: 1rem">
                            @include('documents.partials.preview_sidebar')
                        </div>
                        <div id="actions-sidebar" class="d-none @if(!$enable_document_preview) d-lg-block @endif col-lg-3 pl-lg-5">
                            @include('documents.partials.actions_sidebar')
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    @include('layouts.common.partials.footer')
</div>

@if(!empty($js))
    @push('scripts')
<script src="{{ mix($js) }}"></script>
    @endpush
@endif

@include('layouts.document.partials.footer-scripts')
</body>

</html>
