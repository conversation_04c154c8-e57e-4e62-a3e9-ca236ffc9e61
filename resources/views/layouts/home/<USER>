<!DOCTYPE html>

<html lang="hr">

<head>
@include('layouts.home.partials.head')
</head>

<body>
<div id="layout-container">

    @include('layouts.common.partials.nav')

    <div class="pt-5" style="padding-bottom: 15rem;">
        @if (session('success'))
            <div class="alert alert-success pt-5">
                {!! session('success') !!}
            </div>
        @endif
        @if (session('error'))
            <div class="alert alert-danger pt-5">
                {!! session('error') !!}
            </div>
        @endif
        @if (isset($errors) && $errors->any())
            <div class="alert alert-danger">
                @if($errors->has('override'))
                    {{ collect($errors->get('override'))->first() }}
                @else
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                @endif

            </div>
        @endif
        @yield('content')
    </div>

    @include('layouts.common.partials.footer')

</div>

@include('layouts.home.partials.footer-scripts')

</body>

</html>