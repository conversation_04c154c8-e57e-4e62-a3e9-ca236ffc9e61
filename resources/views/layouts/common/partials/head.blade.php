@if(App::environment('production'))
    @if (Cookie::get('ga_accepted'))
        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script class="ga-script" async src="https://www.googletagmanager.com/gtag/js?id=G-DJ9FX7J5K2"></script>
        <script class="ga-script">
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            gtag('config', 'AW-10849958068');
            gtag('config', 'G-DJ9FX7J5K2');
        </script>
    @endif


@endif

<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" shrink-to-fit=no">
<meta name="format-detection" content="telephone=no" />
<meta name="description" content="@yield('description')">
<meta name="keywords" content="@yield('keywords')">

<meta property="og:title" content="@yield('title') - Pravomat" />
<meta name="og:description" content="@yield('description')">
<meta property="og:type" content="website" />
<meta property="og:url" content="{{ url(Request::url()) }}" />
<meta property="og:image" content="{{url('/')}}/img/lawbot.png" />

<meta name="twitter:title" content="@yield('title') - Pravomat">
<meta name="twitter:description" content="@yield('description')">
<meta name="twitter:image" content="{{url('/')}}/img/lawbot.png">
<meta name="twitter:card" content="summary">

<meta name="csrf-token" content="{{ csrf_token() }}">

<title>@yield('title') - Pravomat</title>

<link rel="canonical" href="{{ url()->current() }}" />

<!-- Bootstrap core CSS -->
<link href="/vendor/bootstrap/css/bootstrap.min.css?v=4.3" rel="stylesheet">

<!-- DataTables CSS -->
<link rel="stylesheet" href="/vendor/datatables//datatables.min.css">

<!-- Custom fonts for this template -->
<link href="/vendor/fontawesome-free/css/all.min.css" rel="stylesheet">
<link href="/vendor/simple-line-icons/css/simple-line-icons.css" rel="stylesheet" type="text/css">

<link href="/css/select2.min.css" rel="stylesheet" />

<!-- Custom styles for this template -->
<link href="{{ mix('css/app.css') }}" rel="stylesheet">
@stack('styles')

<link rel="icon" type="image/png" href="/favicon.png">
