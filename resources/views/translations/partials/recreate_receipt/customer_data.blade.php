<div class="row mb-3">
    <div class="col-lg-12">
        <label>
            <strong>
                Po<PERSON><PERSON> o kupcu
            </strong>
        </label>
        <div class="custom-control custom-radio">
            <input value="individual" type="radio" id="person-type-individual" name="person_type"
                   class="custom-control-input"
                    {{ $translation->receipt->customer_type == 'individual' ? 'checked' : '' }}>
            <label class="custom-control-label" for="person-type-individual"><PERSON><PERSON><PERSON> je fizička osoba</label>
        </div>
        <div class="custom-control custom-radio">
            <input value="business" type="radio" id="person-type-business" name="person_type"
                   class="custom-control-input"
                    {{ $translation->receipt->customer_type == 'business' ? 'checked' : '' }}>
            <label class="custom-control-label" for="person-type-business">Ku<PERSON><PERSON> je pravna osoba</label>
        </div>
    </div>
</div>
<hr/>
<div class="row">
    <div class="col-lg-6 mb-3 mb-lg-0">
        <label for="name">
            <strong @if($translation->receipt->customer_type == 'business') style="display: none" @endif class="person-type-individual">Ime i prezime</strong>
            <strong @if($translation->receipt->customer_type == 'individual') style="display: none" @endif class="person-type-business">Naziv</strong>
        </label>

        <input
                name="name"
                value="{{ $translation->receipt->name }}"
                placeholder="@if($translation->receipt->customer_type == 'business') Unesi naziv... @else Unesi ime i prezime... @endif"
                type="text"
                id="name"
                class="form-control"
                required
        >
    </div>
    <div class="col-lg-6 mb-3 mb-lg-0">
        <label for="email">
            <strong>Adresa e-pošte</strong>
        </label>
        <input name="email"
               placeholder="Unesi adresu e-pošte..." type="email" id="email" class="form-control"
               value="{{ $translation->receipt->email }}" required>
    </div>
</div>
<div class="row mt-0 mt-lg-2">
    <div class="col-lg-6 mb-3 mb-lg-0">
        <label for="address">
            <strong>Adresa</strong>
        </label>
        <input name="address" placeholder="Unesi adresu..." type="text" id="address"
               class="form-control" value="{{ $translation->receipt->address }}" required>
    </div>
    <div class="col-lg-6 mb-3 mb-lg-0">
        <label for="city">
            <strong>Grad</strong>
        </label>
        <input name="city" placeholder="Unesi grad..." type="text" id="city" class="form-control"
               value="{{ $translation->receipt->city }}" required>
    </div>
</div>
<div class="row mt-0 mt-lg-2">
    <div class="col-lg-6 mb-3 mb-lg-0">
        <label for="postal-code">
            <strong>Poštanski broj</strong>
        </label>
        <input name="postal_code" placeholder="Unesi poštanski broj..." type="text" id="postal-code"
               class="form-control" value="{{ $translation->receipt->postal_code }}" required>
    </div>
    <div class="col-lg-6 mb-3 mb-lg-0">
        <label for="country">
            <strong>Država</strong>
        </label>
        <select name="country" id="country" class="form-control">
            @php
                $selected_country = $translation->receipt->country ?: 'Hrvatska';
            @endphp

            @foreach(\App\Helpers\ISOCountryCodes::get() as $iso => $country)
                <option value="{{ $iso }}" {{ $selected_country == $country ? 'selected' : '' }}>
                    {{ $country }}
                </option>
            @endforeach
        </select>
    </div>
</div>
<div class="row mt-0 mt-lg-2">
    <div class="col-lg-6 mb-3 mb-lg-0">
        <label for="oib">
            <strong>OIB <small @if($translation->receipt->customer_type == 'business') style="display: none" @endif class="person-type-individual">(opcionalno)</small></strong>
        </label>
        <input name="oib" placeholder="Unesi OIB..." type="number" id="oib"
               class="form-control" value="{{ $translation->receipt->oib }}">
    </div>
    <div class="col-lg-6">
        <label for="phone">
            <strong>Telefon/mobitel <small @if($translation->isCertified()) style="display: none" @endif class="normal-translation">(opcionalno)</small></strong>
        </label>
        <input name="phone" placeholder="Unesi broj telefona/mobitela..." type="text" id="phone"
               class="form-control" value="{{ $translation->receipt->phone }}" @if(old('type', $translation->getType()) == 'certified') required @endif>
    </div>
</div>

@push('scripts')
    <script>
        $('#person-type-individual').on('click', function () {
            $('.person-type-individual').show();
            $('.person-type-business').hide();

            $('#oib').prop('required', false);
            $('#name').attr('placeholder', 'Unesi ime i prezime...');
        });

        $('#person-type-business').on('click', function () {
            $('.person-type-individual').hide();
            $('.person-type-business').show();

            $('#oib').prop('required', true);
            $('#name').attr('placeholder', 'Unesi naziv...');
        });
    </script>
@endpush