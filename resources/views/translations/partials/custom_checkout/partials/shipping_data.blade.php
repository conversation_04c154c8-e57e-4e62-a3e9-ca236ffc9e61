<div id="shipping" style="display: none;">
    <hr/>
    <div class="row">
        <div class="col-lg-6 mb-3 mb-lg-0">
            <label for="name">
                <strong>Ime i prezime / naziv</strong>
            </label>
            <input name="shipping_name"
                   placeholder="Unesi ime i prezime / naziv..." type="text" id="shipping-name" class="form-control"
                   required>
        </div>
        <div class="col-lg-6 mb-3 mb-lg-0">
            <label for="address">
                <strong>Adresa</strong>
            </label>
            <input name="shipping_address" placeholder="Unesi adresu..." type="text" id="shipping-address"
                   class="form-control" required>
        </div>
    </div>
    <div class="row mt-0 mt-lg-2">

        <div class="col-lg-6 mb-3 mb-lg-0">
            <label for="city">
                <strong>Grad</strong>
            </label>
            <input name="shipping_city" placeholder="Unesi grad..." type="text" id="shipping-city"
                   class="form-control"
                   required>
        </div>
        <div class="col-lg-6 mb-3 mb-lg-0">
            <label for="postal-code">
                <strong>Poštanski broj</strong>
            </label>
            <input name="shipping_postal_code" placeholder="Unesi poštanski broj..." type="text"
                   id="shipping-postal-code"
                   class="form-control" required>
        </div>
    </div>
    <div class="row mt-0 mt-lg-2">
        <div class="col-lg-6 mb-3 mb-lg-0">
            <label for="country">
                <strong>Država</strong>
            </label>
            <select name="shipping_country" id="shipping-country" class="form-control">
                @foreach(\App\Helpers\ISOCountryCodes::get() as $iso => $country)
                    <option @if($iso === 'HR') selected @endif value="{{ $iso }}">{{ $country }}</option>
                @endforeach
            </select>
        </div>
        <div class="col-lg-6">
            <label for="country">
                <strong>Telefon/mobitel <small class="normal-translation"
                                               style="display: none">(opcionalno)</small></strong>
            </label>
            <input name="shipping_phone" placeholder="Unesi broj telefona/mobitela..." type="text"
                   id="shipping-phone"
                   class="form-control" required>
        </div>
    </div>
</div>