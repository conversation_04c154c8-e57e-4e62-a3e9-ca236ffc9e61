@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">
            <div class="card mb-4">
                <div class="card-header">
                    Vrsta i trajanje ugovora
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            <label>1. Sklapa li se ugovor na neodređeno ili na određeno vrijeme?
                            <span data-toggle='tooltip'
                                  data-original-title='Prema Zakonu o radu, ugovor o radu u pravilu se sklapa na neodređeno vrijeme. Ugovor o radu može se iznimno sklopiti na određeno vrijeme za zasnivanje radnog odnosa čiji je prestanak unaprijed utvrđen kada je zbog objektivnog razloga potreba za obavljanjem posla privremena.'>
                        <i class='fa fa-info-circle'></i></span>
                            </label>
                            {{ Form::fRadio($model, 'contract_type', 'indefinite', 'Ugovor o radu na neodređeno vrijeme', ['id' => 'indefinite_contract','checked' => !isset($model->contract_type)]) }}
                            {{ Form::fRadio($model, 'contract_type', 'definite', 'Ugovor o radu na određeno vrijeme', ['id' => 'definite_contract', ])}}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'work_start_date', '2. Upiši datum na koji radnik počinje raditi kod poslodavca', 'Dan početka rada odnosno dan stupanja radnika na rad ne mora biti istovjetan danu sklapanja ugovora o radu. Smatra se da je radni odnos zasnovan danom početka rada.') }}
                            {{ Form::fRadio($model, 'work_start_date', 'same_as_contract_date', 'Na datum sklapanja ugovora', ['id' => 'work_start_date_non_custom_radio', 'checked' => !isset($model->work_start_date)]) }}
                            <div class="form-check radio-input">
                                <label class="form-check-label">
                                    {{ Form::radio('work_start_date', 'custom', null, ['class' => 'form-check-input', 'id' => 'work-start-date-custom-radio']) }}
                                    {{ Form::fText($model, 'work_start_date_custom', null, null, ['class' => 'form-control', 'data-datepicker' => 1, 'autocomplete' => 'off', 'placeholder' => 'Npr: 31. 12. '.date('Y').'.']) }}
                                </label>
                            </div>
                        </div>
                    </div>
                    <div id="definite_contract_container"
                         style="{{ !isset($model->contract_type) ||  $model->contract_type == 'indefinite' ? "display:none" : ""}}">
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fLabel($model, 'expected_duration', '<span class="dot"></span> Upiši datum prestanka ili očekivano trajanje ugovora o radu na određeno vrijeme') }}
                                <div class="form-check radio-input mb-2 mt-1">
                                    <label class="form-check-label">
                                        {{ Form::radio('expected_duration', 'until_date', null, ['class' => 'form-check-input','checked' => !isset($model->expected_duration)]) }}
                                        {{ Form::fText($model, 'expected_duration_until_date', null, 'Radni odnos prestaje na dan', ['class' => 'form-control', 'data-datepicker' => 1, 'autocomplete' => 'off', 'placeholder' => 'Npr: 31. 12. '.date('Y').'.']) }}
                                    </label>
                                </div>
                                <div class="form-check radio-input mb-2 mt-1">
                                    <label class="form-check-label" style="width: 100%;">
                                        {{ Form::radio('expected_duration', 'custom', null, ['class' => 'form-check-input']) }}
                                        {{ Form::fText($model, 'expected_duration_custom', null, 'Očekivano trajanje ugovora je', ['class' => 'form-control', 'data-force-start-case' => 'lower', 'placeholder' => 'Npr: 6 mjeseci, do povratka radnice Ive Ivić s rodiljnog dopusta, i slično']) }}
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fTextArea($model, 'definite_contract_reason', null, '<span class="dot"></span> Koji je objektivni razlog koji opravdava sklapanje ugovora o radu na određeno vrijeme?', ['data-force-start-case' => 'lower', 'placeholder' => 'Npr: zamjena radnice Ive Ivić koja je na rodiljnom dopustu, implementacija novog računovodstvenog programa, privremeno povećanje opsega poslova zbog reorganizacije poslovanja poslodavca, itd.', 'rows' => 5], 'Prema Zakonu o radu, pod objektivnim razlogom koji opravdava sklapanje ugovora o radu na određeno vrijeme i koji se u tom ugovoru mora navesti smatra se zamjena privremeno nenazočnog radnika te obavljanje posla čije je trajanje zbog prirode njegova izvršenja ograničeno rokom ili nastupanjem određenog događaja.') }}
                                <div class="alert alert-warning mt-2 mb-0">
                                    Prema Zakonu o radu, <strong>probni rad (odnosno probni rok)
                                    nije objektivan razlog</strong> za sklapanje ugovora o radu na određeno vrijeme.
                                    Stoga nemojte u ovo polje kao objektivan razlog za sklapanje ugovora o radu upisivati "probni rad" ili "probni rok".
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    Primjena drugih propisa na radni odnos
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'has_work_regulations', '1. Primjenjuje li se kod poslodavca pravilnik o radu?', 'Prema Zakonu o radu, poslodavac koji zapošljava najmanje dvadeset radnika dužan je donijeti i objaviti pravilnik o radu kojim se uređuju plaće, organizacija rada, postupak i mjere za zaštitu dostojanstva radnika te mjere zaštite od diskriminacije i druga pitanja važna za radnike zaposlene kod toga poslodavca, ako ta pitanja nisu uređena kolektivnim ugovorom. Posebni pravilnici o radu mogu se donijeti i za pojedina poduzeća i dijelove poduzeća poslodavca, odnosno pojedine skupine radnika.<br><br>Poslodavac koji zapošljava od 1 do 19 radnika nema obvezu donijeti pravilnik o radu, ali to može učiniti.') }}
                            {{ Form::fRadio($model, 'has_work_regulations', 0, 'Ne', ['id' => 'has_not_work_regulations', 'checked' => !isset($model->can_be_cancelled)]) }}
                            {{ Form::fRadio($model, 'has_work_regulations', 1, 'Da', ['id' => 'has_work_regulations'])}}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'has_collective_agreement','2. Primjenjuje li se neki kolektivni ugovor na radni odnos koji se ugovara ovim ugovorom?', 'Prema članku 192. Zakona o radu, kolektivnim ugovorom uređuju se prava i obveze stranaka koje su sklopile taj ugovor, a može sadržavati i pravna pravila kojima se uređuje sklapanje, sadržaj i prestanak radnih odnosa, pitanja socijalnog osiguranja te druga pitanja iz radnog odnosa ili u vezi s radnim odnosom. Prema članku 25. Zakona o reprezentativnosti udruga poslodavaca i sindikata, stranke kolektivnog ugovora mogu biti, na strani poslodavca, jedan ili više poslodavaca ili njihove udruge, ili Vlada Republike Hrvatske u određenim slučajevima, a na strani sindikata, jedan ili više sindikata koji su reprezentativni u skladu sa Zakonom o reprezentativnosti udruga poslodavaca i sindikata.' ) }}
                            {{ Form::fRadio($model, 'has_collective_agreement', 0, 'Ne', ['id' => 'has_not_collective_agreement','checked' => !isset($model->has_collective_agreement)])}}
                            {{ Form::fRadio($model, 'has_collective_agreement', 1, 'Da', ['id' => 'has_collective_agreement', ])}}
                        </div>
                    </div>
                    <div id="has_collective_agreement_container"
                         style="{{ !isset($model->has_collective_agreement) || $model->has_collective_agreement == 0 ? "display:none" : ""}}">
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fText($model, 'collective_agreement_name', null, '<span class="dot"></span> Upiši naziv kolektivnog ugovora koji se primjenjuje', ['placeholder' => 'Npr: Kolektivni ugovor ugostiteljstva'], null) }}
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    Probni rad
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'probation_period', '1. Ugovara li se probni rad radnika?', 'Prilikom sklapanja ugovora o radu u pravilu se može ugovoriti probni rad. Međutim, nakon prestanka sklopljenog ugovora o radu u kojem je bio ugovoren probni rad radnik i poslodavac pri sklapanju novog ugovora o radu za obavljanje istih poslova ne mogu ponovno ugovoriti probni rad. Probni rad se ne može ugovoriti ni u slučaju sklapanja ugovora o radu pod izmijenjenim uvjetima iz članka 123. stavka (1) Zakona o radu tj. u slučaju kada poslodavac otkaže ugovor i istodobno predloži radniku sklapanje ugovora o radu pod izmijenjenim uvjetima.') }}
                            {{ Form::fRadio($model, 'probation_period', 0, 'Ne', ['id' => 'is_not_probation_period', 'checked' => !isset($model->probation_period)])}}
                            {{ Form::fRadio($model, 'probation_period', 1, 'Da', ['id' => 'is_probation_period']) }}
                        </div>
                    </div>
                    <div id="probation_period_container" style="{{ !isset($model->probation_period) || $model->probation_period == 0 ? "display:none" : "" }}">
                        @if(isset($model->probation_period_type))
                            <span id="probation_period_type_set"></span>
                        @endif
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fLabel($model, 'probation_period_type', '<span class="dot"></span> Odaberi trajanje probnog rada', 'Prema Zakonu o radu, probni rad ne može trajati duže od 6 mjeseci. Ako se radi o ugovoru o radu na određeno vrijeme, trajanje probnog rada mora biti razmjerno očekivanom trajanju ugovora i naravi posla koji radnik obavlja.') }}
                                <div id="probation_period_work_regulations_container" style="@if(!isset($model->has_work_regulations) || !$model->has_work_regulations) display:none; @endif">
                                    {{ Form::fRadio($model, 'probation_period_type', 'defined_in_work_regulations', 'Trajanje i uvjeti probnog rada već su uređeni Pravilnikom o radu poslodavca pa želim da ugovor o radu upućuje na Pravilnik o radu', ['id' => 'probation_period_defined_in_work_regulations_cb'])}}
                                </div>
                                <div id="probation_period_collective_agreement_container"  style="@if(!isset($model->has_collective_agreement) || !$model->has_collective_agreement) display:none; @endif">
                                    {{ Form::fRadio($model, 'probation_period_type', 'defined_in_collective_agreement', 'Trajanje i uvjeti probnog rada već su uređeni Kolektivnim ugovorom koji se primjenjuje na ovaj radni odnos pa želim da ugovor o radu upućuje na Kolektivni ugovor', ['id' => 'probation_period_defined_in_collective_agreement_cb'])}}
                                </div>
                                <div class="form-check">
                                    <label class="form-check-label radio-input" style="width: 100%;">
                                        {{ Form::radio('probation_period_type', 'custom', null, ['id' => 'probation_period_custom_cb', 'class' => 'form-check-input', 'checked' => !isset($model->probation_period_type)]) }}
                                        {{ Form::fDropdown($model, "probation_period_custom", !isset($model->probation_period_custom) ? '3 mjeseca' : $model->probation_period_custom, ['1 mjesec' => '1 mjesec', '2 mjeseca' => '2 mjeseca', '3 mjeseca' => '3 mjeseca', '4 mjeseca' => '4 mjeseca', '5 mjeseci' => '5 mjeseci', '6 mjeseci' => '6 mjeseci']) }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>


    {{ Form::close() }}
@endsection
