@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                <div class="card-header">
                    Do<PERSON><PERSON><PERSON> odredbe
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'is_confidential', '1. <PERSON><PERSON><PERSON> li ugovoriti obvezu radnika da čuva poslovnu tajnu?') }}
                            {{ Form::fRadio($model, 'is_confidential', 0, 'Ne', [ 'checked' => !isset($model->is_confidential)]) }}
                            {{ Form::fRadio($model, 'is_confidential', 1, 'Da') }}
                        </div>
                    </div>

                    <hr/>

                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'regulate_competition', '2. <PERSON><PERSON><PERSON> li ugovoriti ugovornu zabranu natjecanja?', 'Prema Zakonu o radu, poslodavac i radnik mogu ugovoriti da se određeno vrijeme nakon prestanka ugovora o radu, radnik ne smije zaposliti kod druge osobe koja je u tržišnom natjecanju s poslodavcem te da ne smije za svoj račun ili za račun treće osobe sklapati poslove kojima se natječe s poslodavcem (ugovorna zabrana natjecanja). Odredba o ugovornoj zabrani natjecanja je ništetna ako se odnosi na maloljetnika ili radnika koji je u vrijeme sklapanja ugovora u kojem je ona propisana primao plaću manjuod prosječne plaće u Republici Hrvatskoj.') }}
                            {{ Form::fRadio($model, 'regulate_competition', 0, 'Ne', ['id' => 'regulate_competition_0', 'checked' => !isset($model->regulate_competition)]) }}
                            {{ Form::fRadio($model, 'regulate_competition', 1, 'Da', ['id' => 'regulate_competition_1']) }}
                        </div>
                    </div>

                    <div id="regulate_competition_container"
                         style="{{ !isset($model->regulate_competition) || !$model->regulate_competition? "display:none" : null }}">

                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fText($model, 'regulate_competition_period', null, '<span class="dot"></span> Na koje razdoblje se ugovara zabrana natjecanja?', ['placeholder' => 'Npr: 12 mjeseci', 'data-force-start-case' => 'lower'], null, 'Ugovorna zabrana natjecanja ne smije se ugovoriti za razdoblje duže od dvije godine od prestanka radnog odnosa.') }}
                            </div>
                        </div>

                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'regulate_competition_has_compensation', '<span class="dot"></span> Preuzima li poslodavac na sebe obvezu da radniku za vrijeme ugovorne zabrane natjecanja isplaćuje naknadu za ugovornu zabranu natjecanja?', 'Prema Zakonu o radu, ugovorna zabrana natjecanja ne obvezuje radnika ako poslodavac ne preuzme obvezu da će radniku za vrijeme trajanja zabrane isplaćivati naknadu najmanje u iznosu polovice prosječne plaće isplaćene radniku u tri mjeseca prije prestanka ugovora o radu. Međutim,  i ako poslodavac ne preuzme na sebe tu obvezu, može se ugovoriti ugovorna kazna za slučaj nepoštivanja ugovorne zabrane natjecanja od strane radnika. U tom slučaju, poslodavac može tražiti isplatu te kazne ako radnik ne bude poštovao ugovornu zabranu natjecanja.') }}

                                {{ Form::fRadio($model, 'regulate_competition_has_compensation', 1, 'Da', ['id' => 'regulate_competition_has_compensation', 'checked' => !isset($model->regulate_competition_has_compensation)]) }}
                                {{ Form::fRadio($model, 'regulate_competition_has_compensation', 0, 'Ne', ['id' => 'regulate_competition_has_not_compensation',]) }}
                            </div>
                        </div>

                        <div id="regulate_competition_compensation_container"
                             style="{{ isset($model->regulate_competition_has_compensation) && !$model->regulate_competition_has_compensation? "display:none" : null }}">

                            <hr/>
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{ Form::fLabel($model, 'regulate_competition_compensation', '<span class="dot"></span> Koji iznos naknade za ugovornu zabranu natjecanja će poslodavac isplaćivati radniku?') }}

                                    {{ Form::fRadio($model, 'regulate_competition_compensation', 'half_average_pay_for_last_three_months', 'U iznosu polovice prosječne plaće isplaćene radniku u tri mjeseca prije prestanka ugovora o radu', [ 'checked' => !isset($model->regulate_competition_compensation)]) }}
                                    {{ Form::fRadio($model, 'regulate_competition_compensation', 'average_pay_for_last_three_months', 'U iznosu prosječne plaće isplaćene radniku u tri mjeseca prije prestanka ugovora o radu') }}
                                    <div class="form-check">
                                        <label class="form-check-label radio-input">
                                            {{ Form::radio('regulate_competition_compensation', 'custom', null, ['class' => 'form-check-input']) }}
                                            {{ Form::fText($model, 'regulate_competition_compensation_custom', null, null, ['class' => 'form-control', 'placeholder' => 'Npr: 1.000,00', 'data-currency' => 'EUR'], '€') }}
                                        </label>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'regulate_competition_has_penalty', '<span class="dot"></span> Ugovara li se ugovorna kazna za slučaj nepoštovanja ugovorne zabrane natjecanja od strane radnika?', 'Prema Zakonu o radu, ugovorna zabrana natjecanja ne obvezuje radnika ako poslodavac ne preuzme obvezu da će radniku za vrijeme trajanja zabrane isplaćivati naknadu najmanje u iznosu polovice prosječne plaće isplaćene radniku u tri mjeseca prije prestanka ugovora o radu. Međutim, i ako poslodavac ne preuzme na sebe tu obvezu, može se ugovoriti ugovorna kazna za slučaj nepoštivanja ugovorne zabrane natjecanja od strane radnika. U tom slučaju, poslodavac može tražiti isplatu te kazne ako radnik ne bude poštovao ugovornu zabranu natjecanja.') }}

                                {{ Form::fRadio($model, 'regulate_competition_has_penalty', 0, 'Ne', ['id' => 'regulate_competition_has_not_penalty', 'checked' => !isset($model->regulate_competition_penalty), 'disabled' => isset($model->regulate_competition_has_compensation) && !$model->regulate_competition_has_compensation]) }}
                                {{ Form::fRadio($model, 'regulate_competition_has_penalty', 1, 'Da', ['id' => 'regulate_competition_has_penalty']) }}
                            </div>
                        </div>

                        <div id="regulate_competition_penalty_container"
                             style="{{ empty($model->regulate_competition_has_penalty) ? "display:none" : null }}">

                            <hr/>

                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{ Form::fLabel($model, 'regulate_competition_penalty', '<span class="dot"></span> Koji je iznos ugovorne kazne za slučaj nepoštivanja ugovorne zabrane natjecanja?') }}

                                    {{ Form::fRadio($model, 'regulate_competition_penalty', 'total_gross_income_for_12_months', 'Ukupan iznos bruto plaća isplaćenih radniku u posljednjih 12 mjeseci prije prestanka ugovora o radu', [ 'checked' => !isset($model->regulate_competition_penalty)]) }}
                                    <div class="form-check radio-input">
                                        <label class="form-check-label">
                                            {{ Form::radio('regulate_competition_penalty', 'custom', null, ['class' => 'form-check-input']) }}
                                            {{ Form::fText($model, 'regulate_competition_penalty_custom', null, null, ['class' => 'form-control', 'placeholder' => 'Npr: 10.000,00', 'data-currency' => 'EUR'], '€') }}
                                        </label>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>

                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'transfer_intellectual_rights_to_employer', '3. Želiš li ugovoriti da se sva prava intelektualnog vlasništva vezana uz rad radnika prenesu na poslodavca u mjeri u kojoj je to dopušteno zakonom?') }}

                            {{ Form::fRadio($model, 'transfer_intellectual_rights_to_employer', 0, 'Ne', ['checked' => !isset($model->transfer_intellectual_rights_to_employer)]) }}
                            {{ Form::fRadio($model, 'transfer_intellectual_rights_to_employer', 1, 'Da') }}
                        </div>
                    </div>

                </div>

            </div>

        </div>

    </div>


    {{ Form::close() }}
@endsection
