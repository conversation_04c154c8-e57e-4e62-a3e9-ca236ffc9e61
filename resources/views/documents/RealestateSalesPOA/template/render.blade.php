<h2 class="text-center editable-segment mb-0 pb-0" data-type="title">
    PUNOMOĆ
</h2>
<div class="editable-segment" data-type="body">
    <p class="text-center">kojom ja,</p>
   <p>
       <strong>{!! $data['principal_name'] !!},</strong> {!! $data['principal_address'] !!}, {!! $data['principal_postal_code'] !!}
       {!! $data['principal_city'] !!}, {!! $data['principal_country'] !!}, OIB: {!! $data['principal_oib'] !!}@if(!empty($data['principal_representatives'])), kao opuno<PERSON>itelj, @else, kao opunomoćitelj (u daljnjem tekstu: <strong>Opunomoćitelj</strong>)@endif

       @if(!empty($data['principal_representatives']))
           @if(count($data['principal_representatives']) == 1)
               kojeg zastupa
           @else
               kojeg zastupaju
           @endif

           @foreach($data['principal_representatives'] as $_i => $_authorized_person)
                   {!! !empty($_authorized_person['role']) ? $_authorized_person['role'] : $builder::$placeholder !!} {!! !empty($_authorized_person['name']) ? $_authorized_person['name'] : $builder::$placeholder  !!},
                   {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder  !!},
                   {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder  !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                   {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder  !!},
                   OIB: {!! !empty($_authorized_person['oib']) ? $_authorized_person['oib'] : $builder::$placeholder  !!}@if($_i < (count($data['principal_representatives'])-1))@if(($_i+2) == count($data['principal_representatives'])) i @else, @endif @else() (u daljnjem tekstu: <strong>Opunomoćitelj</strong>)@endif
           @endforeach
       @endif
   </p>
    <p class="text-center">
        dajem ovlaštenje da me
    </p>
    <p>
        <strong>{!! $data['agent_name'] !!},</strong> {!! $data['agent_address'] !!}, {!! $data['agent_postal_code'] !!}
        {!! $data['agent_city'] !!}, {!! $data['agent_country'] !!}, OIB: {!! $data['agent_oib'] !!}@if(!empty($data['agent_principal_relationship'])), {!! $data['agent_principal_relationship'] !!} Opunomoćitelja, @else, @endif kao opunomoćenik (u daljnjem tekstu: <strong>Opunomoćenik</strong>)
    </p>

    <p>
        zastupa vezano za prodaju @if(!empty($data['additional_realestates'])) nekretnina @else nekretnine @endif u mojem vlasništvu i to:
    </p>

    <ul>

        @include('documents.RealestateSalesPOA.template.partials.realestate', [
           'data' => $data,
		   'seller_has_full_realestate_ownership' => $data['seller_has_full_realestate_ownership'] ?? true,
           'is_full_sale' => $data['is_full_sale'] ?? true
       ])

        @if(!empty($data['additional_realestates']))

            @foreach($data['additional_realestates'] as $_data)
                @include('documents.RealestateSalesPOA.template.partials.realestate', [
                   'data' => $_data,
				   'seller_has_full_realestate_ownership' => $_data['seller_has_full_realestate_ownership'] ?? true,
                   'is_full_sale' => $_data['is_full_sale'] ?? true
               ])
            @endforeach

        @endif

    </ul>

    <p>
        tako da može u moje ime i za moj račun poduzeti sljedeće pravne radnje:
    </p>

    <ul>

        @if(!empty($data['agent_authorities']) || !empty($data['custom_agent_authorities']))
            @if(!empty($data['agent_authorities']))
                @foreach($data['agent_authorities'] as $_i => $_agent_authority)
                    <li>{!! $_agent_authority !!}{!! empty($data['custom_agent_authorities']) && ( ($_i == count($data['agent_authorities']) - 1) && substr($_agent_authority, -1) != "." ) ? "." : null !!}</li>
                @endforeach
            @endif

            @if(!empty($data['custom_agent_authorities']))
                @foreach($data['custom_agent_authorities'] as $_i => $_custom_agent_authority)
                    @if(!empty($_custom_agent_authority['name']))
                        <li>{!! $_custom_agent_authority['name'] !!}{!! ( ($_i == count($data['custom_agent_authorities']) - 1) && substr($_custom_agent_authority['name'], -1) != "." ) ? "." : null !!}</li>
                    @else
                        <li>{!! $builder::$placeholder !!}</li>
                    @endif
                @endforeach
            @endif
        @else
            <li>
                {!! $builder::$placeholder !!}
            </li>
        @endif

    </ul>

    @if(!empty($data['lawyer_engagement_allowed']))
        <p>
            Opunomoćenik je ovlašten u moje ime i za moj račun angažirati odvjetnika i druge stručne osobe za poduzimanje gore navedenih pravnih radnji.
        </p>
    @endif

    @if(!empty($data['supplemental_agent_exists']))
        @if(!isset($data['supplemental_agent_type']) || $data['supplemental_agent_type'] == 'non_specific')
            <p>
                Ako bi Opunomoćenik zbog bilo kojeg razloga bio spriječen u poduzimanju pravnih radnji za koje je ovlašten na temelju ove Punomoći, pristajem da ga mijenja bilo koja osoba koju Opunomoćenik odredi za tu svrhu.
            </p>
        @else
            <p>
                Ako bi Opunomoćenik zbog bilo kojeg razloga bio spriječen u poduzimanju pravnih radnji za koje je ovlašten na temelju ove Punomoći, pristajem da ga mijenja
                {!! $data['supplemental_agent_name'] !!}, {!! $data['supplemental_agent_address'] !!}, {!! $data['supplemental_agent_postal_code'] !!}
                {!! $data['supplemental_agent_city'] !!}, {!! $data['supplemental_agent_country'] !!}, OIB:
                {!! $data['supplemental_agent_oib'] !!}.
            </p>
        @endif
    @endif

    @if(!empty($data['custom_provisions']))
        @foreach($data['custom_provisions'] as $_custom_provision)
            @if(!empty($_custom_provision['provision']))
                @php
                    $p_index = 1;

                    $_custom_provision_content = App\Helpers\StringHelper::wordpressContent($_custom_provision['provision']);

                    foreach(explode("<p>", $_custom_provision_content) as $_p) {
                        if(!empty(trim($_p))){
                            echo "<p> $_p";
                        }
                    }
                @endphp
            @endif

        @endforeach
    @endif

    <div class="avoid-page-break">
        <p>
            Ova Punomoć vrijedi
            @switch($data['poa_validity_period'])
                @case('until_revoked')
                    do opoziva.
                    @break
                @case('until_end')
                    do okončanja postupaka poduzetih na temelju ovlaštenja navedenih u Punomoći.
                    @break
                @case('until_date')
                    do dana {!! $data['poa_validity_period_until_date'] !!} godine.
                    @break
                @case('custom')
                    {!! $data['poa_validity_period_custom'] !!}.
                    @break
                @default
                    do opoziva.
            @endswitch
        </p>

        <p>
            {!! $data['poa_place'] !!}, dana {!! StringHelper::dateToText($data['poa_date'], $builder::$placeholder) !!} godine.
        </p>

        <table id="signatures-segment" style="table-layout: fixed; width: 100%; overflow: wrap;" autosize="1">
            @if(!empty($data['parties']))
                @foreach($data['parties'] as $_i => $_party)
                    <tr class="avoid-page-break">
                        <td style="vertical-align:top; width: 50%;">
                            <div style="margin:0; padding: 0;">
                                <img alt="signature" src="{L{{ $_i }}}">
                            </div>
                            <span class="signature-line">_____________________________________________</span><br>
                            <small>{!! $builder->getPartyLabel($_party, 'Opunomoćitelj') !!}</small>
                            <br/>
                        </td>
                        <td style="vertical-align:top; padding-left: 5%; width: 50%;">
                        </td>
                    </tr>
                @endforeach
            @else
                <tr class="avoid-page-break">
                    <td style="vertical-align:top; width: 50%;">
                        <div style="margin:0; padding: 0;">
                            <img alt="signature" src="{L0}">
                        </div>
                        <span class="signature-line">_____________________________________________</span><br>
                        <small>Opunomoćitelj</small>
                        <br/>
                    </td>
                    <td style="vertical-align:top; padding-left: 5%; width: 50%;">
                    </td>
                </tr>
            @endif
        </table>
    </div>

</div>