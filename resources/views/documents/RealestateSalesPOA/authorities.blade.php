@extends('layouts.document.master')
@section('content')
    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class='row'>
        <div class='col'>
            <div class='card mb-4'>
                <div class='card-header'>
                    Ovlaštenja
                </div>
                <div class='card-body'>
                    <div class='row'>
                        <div class='form-group col-lg-12'>
                            {{ Form::fLabel($model, 'agent_authorities', '1. Opunomoćitelj ovlašćuje opunomoćenika da u njegovo ime i za njegov račun može poduzeti sljedeće pravne radnje vezane za prodaju nekretnine:') }}
                            <div id="fixed_agent_authorities_container" class="form-group col-lg-12 mb-1">
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'oglašavati nekretninu i na druge načine tražiti kupca', 'oglašavati nekretninu i na druge načine tražiti kupca') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'pokazivati nekretninu potencijalnim kupcima', 'pokazivati nekretninu potencijalnim kupcima') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'samostalno odrediti kupoprodajnu cijenu nekretnine', 'samostalno odrediti kupoprodajnu cijenu nekretnine') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'pregovarati s potencijalnim kupcima o sklapanju predugovora o kupoprodaji nekretnine, ugovora o kupoprodaji nekretnine i eventualnih dodataka (aneksa) tim ugovorima', 'pregovarati s potencijalnim kupcima o sklapanju predugovora o kupoprodaji nekretnine, ugovora o kupoprodaji nekretnine i eventualnih dodataka (aneksa) tim ugovorima') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'samostalno odabrati kupca', 'samostalno odabrati kupca') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'pribaviti svu dokumentaciju potrebnu za sklapanje predugovora o kupoprodaji nekretnine, ugovora o kupoprodaji nekretnine i eventualnih dodataka tim ugovorima, kao što je zemljišnoknjižni izvadak, energetski certifikat, izvod iz matične knjige, potvrde komunalnih društava za priključke koji postoje na nekretnini, itd.', 'pribaviti svu dokumentaciju potrebnu za sklapanje predugovora o kupoprodaji nekretnine, ugovora o kupoprodaji nekretnine i eventualnih dodataka tim ugovorima, kao što je zemljišnoknjižni izvadak, energetski certifikat, izvod iz matične knjige, potvrde komunalnih društava za priključke koji postoje na nekretnini, itd.') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'poduzeti sve potrebne radnje za provedbu usklađivanja stanja u katastru nekretnina sa zemljišnoknjižnim stanjem za nekretninu', 'poduzeti sve potrebne radnje za provedbu usklađivanja stanja u katastru nekretnina sa zemljišnoknjižnim stanjem za nekretninu') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 's odabranim kupcem potpisati predugovor o kupoprodaji nekretnine, ugovor o kupoprodaji nekretnine i eventualne dodatke (anekse) tim ugovorima i na njima ovjeriti potpis kod javnog bilježnika', 's odabranim kupcem potpisati predugovor o kupoprodaji nekretnine, ugovor o kupoprodaji nekretnine i eventualne dodatke (anekse) tim ugovorima i na njima ovjeriti potpis kod javnog bilježnika') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'primiti iznos kapare u gotovini ili na vlastiti bankovni račun', 'primiti iznos kapare u gotovini ili na vlastiti bankovni račun') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'primiti isplatu kupoprodajne cijene u gotovini ili na vlastiti bankovni račun', 'primiti isplatu kupoprodajne cijene u gotovini ili na vlastiti bankovni račun') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'po potrebi u moje ime otvoriti bilo koju vrstu računa kod bilo koje banke u Republici Hrvatskoj na koji će se izvršiti uplata kapare i/ili kupoprodajne cijene te me u tu svrhu zastupati kod odabrane banke, pribaviti svu potrebnu dokumentaciju, potpisati sve zahtjeve, obrasce i slično', 'po potrebi u moje ime otvoriti bilo koju vrstu računa kod bilo koje banke u Republici Hrvatskoj na koji će se izvršiti uplata kapare i/ili kupoprodajne cijene te me u tu svrhu zastupati kod odabrane banke, pribaviti svu potrebnu dokumentaciju, potpisati sve zahtjeve, obrasce i slično') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'potpisati ugovor o kreditu, odnosno ugovor o zasnivanju založnog prava u svojstvu založnog dužnika kojim se u korist banke kupca zasniva založno pravo na nekretnini, podnijeti takav ugovor na solemnizaciju javnom bilježniku i izdati tabularnu izjavu tj. dopuštenje banci kupca za uknjižbu založnog prava na nekretnini bez ikakve daljnje suglasnosti ili odobrenja, radi osiguranja namirenja tražbine prema kupcu na temelju ugovora o kreditu, sve u svrhu isplate kupoprodajne cijene ako kupac sredstva za isplatu kupoprodajne cijene osigurava hipotekarnim kreditom', 'potpisati ugovor o kreditu, odnosno ugovor o zasnivanju založnog prava u svojstvu založnog dužnika kojim se u korist banke kupca zasniva založno pravo na nekretnini, podnijeti takav ugovor na solemnizaciju javnom bilježniku i izdati tabularnu izjavu tj. dopuštenje banci kupca za uknjižbu založnog prava na nekretnini bez ikakve daljnje suglasnosti ili odobrenja, radi osiguranja namirenja tražbine prema kupcu na temelju ugovora o kreditu, sve u svrhu isplate kupoprodajne cijene ako kupac sredstva za isplatu kupoprodajne cijene osigurava hipotekarnim kreditom') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'potpisati izjavu kojom se, sukladno članku 75. stavku 5. Ovršnog zakona, daje izričita i neopoziva suglasnost i dopuštenje banci kupca da, u slučaju prisilnog namirenja tražbine koju banka ima prema kupcu iz ugovora o kreditu kojim se osiguravaju sredstva za isplatu kupoprodajne cijene, može provesti ovrhu na nekretnini na kojoj je zasnovano založno pravo, radi osiguranja tražbine koju banka ima prema kupcu na temelju navedenog ugovora o kreditu, i na njoj ovjeriti potpis kod javnog bilježnika', 'potpisati izjavu kojom se, sukladno članku 75. stavku 5. Ovršnog zakona, daje izričita i neopoziva suglasnost i dopuštenje banci kupca da, u slučaju prisilnog namirenja tražbine koju banka ima prema kupcu iz ugovora o kreditu kojim se osiguravaju sredstva za isplatu kupoprodajne cijene, može provesti ovrhu na nekretnini na kojoj je zasnovano založno pravo, radi osiguranja tražbine koju banka ima prema kupcu na temelju navedenog ugovora o kreditu, i na njoj ovjeriti potpis kod javnog bilježnika') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'izdati kupcu tabularnu izjavu tj. dopuštenje kupcu za uknjižbu prava vlasništva i na njoj ovjeriti potpis kod javnog bilježnika', 'izdati kupcu tabularnu izjavu tj. dopuštenje kupcu za uknjižbu prava vlasništva i na njoj ovjeriti potpis kod javnog bilježnika') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'predati nekretninu u posjed kupcu i potpisati primopredajni zapisnik', 'predati nekretninu u posjed kupcu i potpisati primopredajni zapisnik') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'ishoditi promjenu korisnika javnih i drugih usluga koje se odnose na nekretninu koja je predmet ugovora o kupoprodaji kod svih komunalnih društava, energetskih distributera i drugih nadležnih službi, kao što su struja, voda, odvoz smeća, zajednička pričuva, čišćenje stubišta, itd.', 'ishoditi promjenu korisnika javnih i drugih usluga koje se odnose na nekretninu koja je predmet ugovora o kupoprodaji kod svih komunalnih društava, energetskih distributera i drugih nadležnih službi, kao što su struja, voda, odvoz smeća, zajednička pričuva, čišćenje stubišta, itd.') }}
                                {{ Form::fCheckbox($model, 'agent_authorities[]', 'poduzeti sve druge pravne radnje vezane za realizaciju prodaje predmetne nekretnine', 'poduzeti sve druge pravne radnje vezane za realizaciju prodaje predmetne nekretnine') }}
                            </div>
                            <div id="custom_agent_authority_template" class="d-none">
                                <div class="custom_agent_authority_content">
                                    <div class="pb-1">
                                        {{ Form::fCheckbox($model, 'custom_agent_authorities[{INDEX}][exists]', 1, Form::text("custom_agent_authorities[{INDEX}][name]", null, ['class' => 'checkbox_input_text form-control ', 'placeholder' => 'Upiši ovlaštenje...']), ['class' => 'form-check-input toggle_custom_agent_authority', 'checked' => 'checked']) }}
                                    </div>
                                </div>
                            </div>
                            <div class="form-group col-lg-12" id="custom_agent_authorities_container">
                                @if(!empty($model->custom_agent_authorities))
                                    @foreach($model->custom_agent_authorities as $_i => $_custom_expense)
                                        <div class="custom_agent_authority_content">
                                            <div class="pb-1">
                                                {{ Form::fCheckbox($model, "custom_agent_authorities[$_i][exists]", 1, Form::text("custom_agent_authorities[{$_i}][name]", null, ['class' => 'checkbox_input_text form-control', 'placeholder' => 'Upiši ovlaštenje...']), ['class' => 'form-check-input toggle_custom_agent_authority', 'checked' => 'checked']) }}
                                            </div>
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                            <div class="form-group col-lg-12">
                                <a class="btn btn-info" id="add_custom_agent_authority">+ Dodaj
                                    ovlaštenje</a>
                            </div>
                        </div>
					</div>

                    <hr/>

                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'lawyer_engagement_allowed', '2. Smije li opunomoćitelj za poduzimanje pravnih radnji navedenih u prethodnom pitanju angažirati odvjetnika i druge stručne osobe?') }}
                            {{ Form::fRadio($model, 'lawyer_engagement_allowed', 0, 'Ne', ['checked' => !isset($model->lawyer_engagement_allowed)] ) }}
                            {{ Form::fRadio($model, 'lawyer_engagement_allowed', 1, 'Da') }}
                        </div>
                    </div>

                    <hr/>

                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'supplemental_agent_exists', '3. Smije li opunomoćenik za slučaj svoje spriječenosti odrediti drugog opunomoćenika koji će ga mijenjati?') }}
                            {{ Form::fRadio($model, 'supplemental_agent_exists', 0, 'Ne', ['id' => 'supplemental_agent_does_not_exist', 'checked' => !isset($model->supplemental_agent_exists)] ) }}
                            {{ Form::fRadio($model, 'supplemental_agent_exists', 1, 'Da', ['id' => 'supplemental_agent_exists',]) }}
                        </div>
                    </div>

                    <div id="supplemental_agent_container" style="{{ !isset($model->supplemental_agent_exists) ||  !$model->supplemental_agent_exists ? "display:none" : ""}}">
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'supplemental_agent_type', '<span class="dot"></span> Koga može opunomoćenik odrediti za zamjenika?') }}
                                {{ Form::fRadio($model, 'supplemental_agent_type', 'non_specific', 'Bilo koju osobu po svom izboru', ['id' => 'supplemental_agent_type_non_specific', 'checked' => !isset($model->supplemental_agent_type)] ) }}
                                {{ Form::fRadio($model, 'supplemental_agent_type', 'specific', 'Točno određenu osobu', ['id' => 'supplemental_agent_type_specific',]) }}
                            </div>
                        </div>
                        <div id="supplemental_agent_type_specific_container" style="{{ !isset($model->supplemental_agent_type) ||  $model->supplemental_agent_type == 'non_specific' ? "display:none" : ""}}">
                            <hr/>
                            <div class="maps_autofill_container">
                                <div class="row">
                                    <div class="form-group col-lg-6">
                                        {{ Form::fText($model, 'supplemental_agent_name', null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: Iva Ivić']) }}
                                    </div>
                                    <div class="form-group col-lg-6">
                                        {{ Form::fText($model, 'supplemental_agent_address', null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-lg-6">
                                        {{ Form::fText($model, 'supplemental_agent_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                    </div>
                                    <div class="form-group col-lg-6">
                                        {{ Form::fNumber($model, 'supplemental_agent_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-lg-6">
                                        {{ Form::fText($model, 'supplemental_agent_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                    </div>
                                    <div class="form-group col-lg-6">
                                        {{ Form::fNumber($model, 'supplemental_agent_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

				</div>
            </div>

        </div>

    </div>
    {{ Form::close() }}
@endsection
