@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                <div class="card-header">
                    Naknada
                </div>


                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'compensation_type', '<span class="question_index">1</span>. Ugovara li se naknada izvođaču u fiksnom ili jediničnom iznosu?', 'Plaćanje naknade u jediničnom iznosu odaberi ako se naknada ugovara u ovisnosti o broju jedinica npr. po m2, po satu, po kartici teksta i slično (npr. 50 €/m2, 300,00 €/sat i slično).') }}
                            {{ Form::fRadio($model, 'compensation_type', 'fixed', 'U fiksnom iznosu', ['id' => 'compensation_type_fixed','checked' => !isset($model->compensation_type)]) }}
                            {{ Form::fRadio($model, 'compensation_type', 'unit', 'U jediničnom iznosu', ['id' => 'compensation_type_unit', ])}}
                        </div>
                    </div>
                    <div id="compensation_type_fixed_container" style="@if(isset($model->compensation_type) && $model->compensation_type != 'fixed') display:none; @endif">
                        <hr/>
                        <div class="row">
                            <div class="form group col-lg-12">
                                {{ Form::fText($model, 'compensation_gross_amount', null, '<span class="dot"></span> Upiši bruto iznos ukupne naknade koju se naručitelj obvezuje platiti izvođaču', ['placeholder'=>'Npr: 1.000,00', 'data-currency' => 'EUR'], '€') }}
                            </div>
                        </div>
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'compensation_in_installments', '<span class="dot"></span> Plaća li naručitelj izvođaču naknadu odjednom ili u dijelovima?') }}
                                {{ Form::fRadio($model, 'compensation_in_installments', 0, 'Odjednom', ['checked' => !isset($model->compensation_in_installments), 'id' => 'compensation_in_full_radio']) }}
                                {{ Form::fRadio($model, 'compensation_in_installments', 1, 'U dijelovima', ['id' => 'compensation_in_installments_radio']) }}
                            </div>
                        </div>

                        <div class="dynamic" id="compensation_in_full_container"
                             style="@if(!empty($model->compensation_in_installments)) display:none; @endif">
                            <hr/>
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{ Form::fLabel($model, 'fixed_compensation_deadline', '<span class="dot"></span> U kojem roku se naručitelj obvezuje platiti izvođaču ukupnu naknadu?') }}
                                    <div class="form-check mb-1 mt-1">
                                        <label class="form-check-label" style="width:100%;">
                                            {{ Form::radio('fixed_compensation_deadline', 'custom', null, ['class' => 'form-check-input compensation_deadline_custom_radio', 'checked' => !isset($model->fixed_compensation_deadline)]) }}
                                            Upiši rok
                                            <br/>
                                            {{ Form::fText($model, 'fixed_compensation_deadline_custom', null, null, ['class' => 'form-control compensation_deadline_custom', 'placeholder' => 'Npr: u roku od 15 dana od izvršenja Djela od strane Izvođača', 'autocomplete' => 'off', 'data-force-start-case' => 'lower']) }}
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <label class="form-check-label" style="width:100%;">
                                            {{ Form::radio('fixed_compensation_deadline', 'date', null, ['class' => 'form-check-input compensation_deadline_date_radio']) }}
                                            Najkasnije dana
                                            <br/>
                                            {{ Form::fText($model, 'fixed_compensation_deadline_date', null, null, ['class' => 'form-control compensation_deadline_date', 'data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="dynamic" id="compensation_in_installments_container"
                             style="@if(empty($model->compensation_in_installments)) display:none; @endif">

                            <hr/>

                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{ Form::fDropdown($model, 'compensation_installments_number', null, array_combine(range(2, 10), range(2, 10)), '<span class="dot"></span> U koliko dijelova će naručitelj platiti izvođaču ukupnu naknadu?', ['id' => 'compensation_installments_number']) }}
                                </div>
                            </div>

                            <div id="compensation_installment_template" style="display:none;">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        {INDEX}. dio naknade
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fText($model, 'compensation_installments[{KEY}][amount]', null, 'Bruto iznos', ['placeholder' => 'Npr: 500,00', 'data-currency' => 'EUR'], '€') }}
                                            </div>
                                            <div class="form-group col-lg-12">
                                                <hr/>
                                                <label>U kojem roku se naručitelj obvezuje platiti izvođaču {INDEX}. dio naknade?</label>

                                                <div class="form-check mb-1 mt-1">
                                                    <label class="form-check-label" style="width:100%;">
                                                        {{ Form::radio('compensation_installments[{KEY}][deadline]', 'custom', null, ['class' => 'form-check-input compensation_deadline_custom_radio', 'checked' => true]) }}
                                                        Upiši rok
                                                        <br/>
                                                        {{ Form::fText($model, 'compensation_installments[{KEY}][deadline_custom]', null, null, ['class' => 'form-control compensation_deadline_custom', 'placeholder' => 'Npr: u roku od 15 dana od izvršenja Djela od strane Izvođača', 'autocomplete' => 'off']) }}
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <label class="form-check-label" style="width:100%;">
                                                        {{ Form::radio('compensation_installments[{KEY}][deadline]', 'date', null, ['class' => 'form-check-input compensation_deadline_date_radio']) }}
                                                        Najkasnije dana
                                                        <br/>
                                                        {{ Form::fText($model, 'compensation_installments[{KEY}][deadline_date]', null, null, ['class' => 'form-control compensation_deadline_date','data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                                    </label>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="compensation_installments">
                                @if(empty($model->compensation_installments))
                                    <div class="card mb-4">
                                        <div class="card-header">
                                            1. dio naknade
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="form-group col-lg-12">
                                                    {{ Form::fText($model, 'compensation_installments[0][amount]', null, 'Bruto iznos', ['placeholder' => 'Npr: 500,00', 'data-currency' => 'EUR'], '€') }}
                                                </div>
                                                <div class="form-group col-lg-12">
                                                    <hr/>
                                                    <label>U kojem roku se naručitelj obvezuje platiti izvođaču 1. dio naknade?
                                                    </label>
                                                    <div class="form-check mb-1 mt-1">
                                                        <label class="form-check-label" style="width:100%;">
                                                            {{ Form::radio('compensation_installments[0][deadline]', 'custom', null, ['class' => 'form-check-input compensation_deadline_custom_radio', 'checked' => true]) }}
                                                            Upiši rok
                                                            <br/>
                                                            {{ Form::fText($model, 'compensation_installments[0][deadline_custom]', null, null, ['class' => 'form-control compensation_deadline_custom', 'placeholder' => 'Npr: kao predujam na dan potpisivanja ovog Ugovora', 'autocomplete' => 'off', 'data-force-start-case' => 'lower']) }}
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <label class="form-check-label" style="width:100%;">
                                                            {{ Form::radio('compensation_installments[0][deadline]', 'date', null, ['class' => 'form-check-input compensation_deadline_date_radio']) }}
                                                            Najkasnije dana
                                                            <br/>
                                                            {{ Form::fText($model, 'compensation_installments[0][deadline_date]', null, null, ['class' => 'form-control compensation_deadline_date', 'data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card mb-4">
                                        <div class="card-header">
                                            2. dio naknade
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="form-group col-lg-12">
                                                    {{ Form::fText($model, 'compensation_installments[1][amount]', null, 'Bruto iznos', ['placeholder' => 'Npr: 500,00', 'data-currency' => 'EUR'], '€') }}
                                                </div>
                                                <div class="form-group col-lg-12">
                                                    <hr/>
                                                    <label>U kojem roku se naručitelj obvezuje platiti izvođaču 2. dio naknade?
                                                    </label>
                                                    <div class="form-check mb-1 mt-1">
                                                        <label class="form-check-label" style="width:100%;">
                                                            {{ Form::radio('compensation_installments[1][deadline]', 'custom', null, ['class' => 'form-check-input compensation_deadline_custom_radio', 'checked' => true]) }}
                                                            Upiši rok
                                                            <br/>
                                                            {{ Form::fText($model, 'compensation_installments[1][deadline_custom]', null, null, ['class' => 'form-control compensation_deadline_custom', 'placeholder' => 'Npr: u roku od 15 dana od izvršenja Djela od strane Izvođača', 'autocomplete' => 'off', 'data-force-start-case' => 'lower']) }}
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <label class="form-check-label" style="width:100%;">
                                                            {{ Form::radio('compensation_installments[1][deadline]', 'date', null, ['class' => 'form-check-input compensation_deadline_date_radio']) }}
                                                            Najkasnije dana
                                                            <br/>
                                                            {{ Form::fText($model, 'compensation_installments[1][deadline_date]', null, null, ['class' => 'form-control compensation_deadline_date', 'data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    @foreach($model->compensation_installments as $_key => $_installment)
                                        <div class="card mb-4">
                                            <div class="card-header">
                                                {{$_key+1}}. dio naknade
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-12">
                                                        {{ Form::fText($model, "compensation_installments[$_key][amount]", null, 'Iznos', ['placeholder' => 'Npr: 500,00', 'data-currency' => 'EUR'], '€') }}
                                                    </div>
                                                    <div class="form-group col-lg-12">
                                                        <hr/>
                                                        <label>
                                                            U kojem roku se naručitelj obvezuje platiti izvođaču {{$_key+1}}. dio naknade?
                                                        </label>
                                                        <div class="form-check mb-1 mt-1">
                                                            <label class="form-check-label" style="width:100%;">
                                                                {{ Form::radio("compensation_installments[$_key][deadline]", 'custom', null, ['class' => 'form-check-input compensation_deadline_custom_radio']) }}
                                                                Upiši rok
                                                                <br/>
                                                                {{ Form::fText($model, "compensation_installments[$_key][deadline_custom]", null, null, ['class' => 'form-control compensation_deadline_custom', 'placeholder' => 'Npr: u roku od 15 dana od izvršenja Djela od strane Izvođača', 'autocomplete' => 'off', 'data-force-start-case' => 'lower']) }}
                                                            </label>
                                                        </div>
                                                        <div class="form-check">
                                                            <label class="form-check-label" style="width:100%;">
                                                                {{ Form::radio("compensation_installments[$_key][deadline]", 'date', null, ['class' => 'form-check-input compensation_deadline_date_radio']) }}
                                                                Najkasnije dana
                                                                <br/>
                                                                {{ Form::fText($model, "compensation_installments[$_key][deadline_date]", null, null, ['class' => 'form-control compensation_deadline_date', 'data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                        </div>
                    </div>

                    <div id="compensation_type_unit_container" style="@if(!isset($model->compensation_type) || $model->compensation_type != 'unit') display:none; @endif">
                        <hr/>
                        <div class="row">
                            <div class="col-lg-6 form-group">
                                {{ Form::fText($model, 'unit_compensation_amount', null, '<span class="dot"></span> Upiši bruto jedinični iznos naknade', ['placeholder' => 'Npr: 100,00', 'data-currency' => 'EUR'], '€') }}
                            </div>
                            <div class="col-lg-6 form-group">
                                {{ Form::fText($model, 'unit_compensation_unit', null, '<span class="dot"></span> Upiši jedinicu po kojoj se obračunava naknada', ['placeholder' => 'Npr: m2'], 'po') }}
                            </div>
                        </div>
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'compensation_deadline', '<span class="dot"></span> U kojem roku se naručitelj obvezuje platiti izvođaču ukupnu naknadu?') }}
                                <div class="form-check mb-1 mt-1">
                                    <label class="form-check-label" style="width:100%;">
                                        {{ Form::radio('unit_compensation_deadline', 'custom', null, ['class' => 'form-check-input compensation_deadline_custom_radio', 'checked' => !isset($model->unit_compensation_deadline)]) }}
                                        Upiši rok
                                        <br/>
                                        {{ Form::fText($model, 'unit_compensation_deadline_custom', null, null, ['class' => 'form-control compensation_deadline_custom', 'placeholder' => 'Npr: u roku od 15 dana od izvršenja Djela od strane Izvođača', 'autocomplete' => 'off', 'data-force-start-case' => 'lower']) }}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <label class="form-check-label" style="width:100%;">
                                        {{ Form::radio('unit_compensation_deadline', 'date', null, ['class' => 'form-check-input compensation_deadline_date_radio']) }}
                                        Najkasnije dana
                                        <br/>
                                        {{ Form::fText($model, 'unit_compensation_deadline_date', null, null, ['class' => 'form-control compensation_deadline_date', 'data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'contractor_is_taxpayer', '<span class="question_index">2</span>. Je li izvođač u sustavu poreza na dodanu vrijednost (PDV-a)?') }}
                            {{ Form::fRadio($model, 'contractor_is_taxpayer', 0, 'Ne', ['checked' => !isset($model->documentor_is_taxpayer)]) }}
                            {{ Form::fRadio($model, 'contractor_is_taxpayer', 1, 'Da')}}
                        </div>
                    </div>
                    <div style="@if($model->document->getValue('client_is_business') == null ||  $model->document->getValue('client_is_business') == 1) display:none; @endif">
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'tax_expenses_responsibility', '<span class="question_index populate"></span>. Tko se obvezuje platiti porez, prirez, doprinose i druga davanja?') }}
                                {{ Form::fRadio($model, 'tax_expenses_responsibility', 'contractor', 'Izvođač', ['checked' => !isset($model->tax_expenses_responsibility)]) }}
                                {{ Form::fRadio($model, 'tax_expenses_responsibility', 'client', 'Naručitelj')}}
                            </div>
                        </div>
                    </div>

                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fText($model, 'compensation_iban', null, '<span class="question_index populate"></span>. Upiši IBAN žiro računa na koji će naručitelj platiti naknadu izvođaču', ['placeholder' => 'Npr: *********************']) }}
                        </div>
                    </div>
                </div>
            </div>


        </div>

    </div>

    {{ Form::close() }}
@endsection
