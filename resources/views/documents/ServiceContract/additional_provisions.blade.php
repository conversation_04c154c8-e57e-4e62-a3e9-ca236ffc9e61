@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                <div class="card-header">
                    Do<PERSON><PERSON><PERSON> odredbe
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'is_confidential', '1. <PERSON><PERSON><PERSON> li ugovoriti obvezu izvođača da čuva povjerljivost osobnih i poslovnih podataka?') }}
                            {{ Form::fRadio($model, 'is_confidential', 0, 'Ne', [ 'checked' => !isset($model->is_confidential)]) }}
                            {{ Form::fRadio($model, 'is_confidential', 1, 'Da') }}
                        </div>
                    </div>

                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'has_deadline_penalty', '2. <PERSON><PERSON><PERSON> li ugovoriti ugovornu kaznu za slučaj prekoračenja roka za izvršenje ugovorenih poslova od strane izvođača?') }}

                            {{ Form::fRadio($model, 'has_deadline_penalty', 0, 'Ne', ['id' => 'has_not_deadline_penalty', 'checked' => !isset($model->has_deadline_penalty)]) }}
                            {{ Form::fRadio($model, 'has_deadline_penalty', 1, 'Da', ['id' => 'has_deadline_penalty']) }}
                        </div>
                    </div>

                    <div id="deadline_penalty_container"
                         style="{{ empty($model->has_deadline_penalty) ? "display:none" : null }}">

                        <hr/>

                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fText($model, 'deadline_penalty', null, '<span class="dot"></span> Koji je iznos ugovorne kazne po danu zakašnjenja?', ['placeholder' => 'Npr. 1.000,00', 'data-currency' => 'EUR'], '€') }}
                            </div>
                        </div>

                    </div>

                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'transfer_intellectual_rights_to_client', '3. Želiš li ugovoriti da se sva prava intelektualnog vlasništva vezana uz djelo koje naručitelj isporuči prenesu na naručitelja u mjeri u kojoj je to dopušteno zakonom?') }}

                            {{ Form::fRadio($model, 'transfer_intellectual_rights_to_client', 0, 'Ne', ['checked' => !isset($model->transfer_intellectual_rights_to_client)]) }}
                            {{ Form::fRadio($model, 'transfer_intellectual_rights_to_client', 1, 'Da') }}
                        </div>
                    </div>



                </div>

            </div>

        </div>

    </div>


    {{ Form::close() }}
@endsection
