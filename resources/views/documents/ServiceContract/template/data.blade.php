@extends($builder->getLayout())
@section('content')
    @php

        // define variables for rendering view
        $data["client_name"] = $builder->get("client_name");
        $data["client_address"] = $builder->get("client_address");
        $data["client_city"] = $builder->get("client_city");
        $data["client_postal_code"] = $builder->get("client_postal_code");
        $data["client_country"] = $builder->get("client_country");
        $data["client_oib"] = $builder->get("client_oib");
        $data["client_is_business"] = $builder->get("client_is_business", false);

        if($data["client_is_business"] === null){
            $data["client_is_business"] = 1;
        }

        $data["authorized_client_persons"] = $builder->get("authorized_client_persons", false);

        // standardize array keys
        if(!empty($data['authorized_client_persons'])){
            $data['authorized_client_persons'] = array_values($data['authorized_client_persons']);
			
            // default case
            foreach($data['authorized_client_persons'] as &$_authorized_client_person){
                if(empty($_authorized_client_person['name'])) $_authorized_client_person['name'] = $builder->get('placeholder');
                if(empty($_authorized_client_person['role'])) $_authorized_client_person['role'] = $builder->get('placeholder');

                $_authorized_client_person['has_entered_address'] = !empty($_authorized_client_person['address'])
                || !empty($_authorized_client_person['city'])
                || !empty($_authorized_client_person['postal_code']);
            }

			// always unset when looping by reference!
		    unset($_authorized_client_person);
        }

        $data["contractor_name"] = $builder->get("contractor_name");
        $data["contractor_address"] = $builder->get("contractor_address");
        $data["contractor_city"] = $builder->get("contractor_city");
        $data["contractor_postal_code"] = $builder->get("contractor_postal_code");
        $data["contractor_country"] = $builder->get("contractor_country");
        $data["contractor_oib"] = $builder->get("contractor_oib");
        $data["work_responsibilities"] = $builder->get("work_responsibilities", false);

        // standardize array keys
		$data['work_responsibilities'] = \App\Helpers\ArrayHelper::standardizeArrayKeys($data['work_responsibilities']);

        $work_requires_materials = $builder->get("work_requires_materials", false);
        $data["work_requires_materials"] = isset($work_requires_materials) ? $work_requires_materials : 1;

        $data["responsible_for_materials"] = $builder->get("responsible_for_materials", false) ?: "contractor_at_own_expense";
        $data["materials"] = $builder->get("materials", false);

		$data['materials'] = \App\Helpers\ArrayHelper::standardizeArrayKeys($data['materials']);

        $data["client_has_guidelines"] = $builder->get("client_has_guidelines", false);
        $data["client_guidelines"] = $builder->get("client_guidelines", false);

        // standardize array keys
		$data['client_guidelines'] = \App\Helpers\ArrayHelper::standardizeArrayKeys($data['client_guidelines']);

        $data["work_deadline"] = $builder->get("work_deadline", false);
        $data["work_deadline_date"] = $builder->get("work_deadline_date");
        $data["work_deadline_custom"] = $builder->get("work_deadline_custom");
        $data["compensation_type"] = $builder->get("compensation_type", false) ?: "fixed";
        $data["compensation_gross_amount"] = $builder->get("compensation_gross_amount");
        $data["compensation_in_installments"] = $builder->get("compensation_in_installments", false);
        $data["fixed_compensation_deadline"] = $builder->get("fixed_compensation_deadline", false) ?: "custom";
        $data["fixed_compensation_deadline_custom"] = $builder->get("fixed_compensation_deadline_custom");
        $data["fixed_compensation_deadline_date"] = $builder->get("fixed_compensation_deadline_date");
        $data["compensation_installments"] = $builder->get("compensation_installments", false);

        // standardize array keys
		$data['compensation_installments'] = \App\Helpers\ArrayHelper::standardizeArrayKeys($data['compensation_installments']);

        $data["unit_compensation_amount"] = $builder->get("unit_compensation_amount");
        $data["unit_compensation_unit"] = $builder->get("unit_compensation_unit");
        $data["unit_compensation_deadline"] = $builder->get("unit_compensation_deadline", false);
        $data["unit_compensation_deadline_custom"] = $builder->get("unit_compensation_deadline_custom");
        $data["unit_compensation_deadline_date"] = $builder->get("unit_compensation_deadline_date");
        $data["contractor_is_taxpayer"] = $builder->get("contractor_is_taxpayer", false);
        $data["tax_expenses_responsibility"] = $builder->get("tax_expenses_responsibility");
        $data["compensation_iban"] = $builder->get("compensation_iban");
        $data["is_confidential"] = $builder->get("is_confidential", false);
        $data["has_deadline_penalty"] = $builder->get("has_deadline_penalty", false);
        $data["deadline_penalty"] = $builder->get("deadline_penalty");
        $data["transfer_intellectual_rights_to_client"] = $builder->get("transfer_intellectual_rights_to_client", false);
        $data["contract_copy_count"] = $builder->get("contract_copy_count");
        $data["client_contract_copy_count"] = $builder->get("client_contract_copy_count");
        $data["contractor_contract_copy_count"] = $builder->get("contractor_contract_copy_count");
        $data["contract_date"] = $builder->get("contract_date");
        $data["contract_place"] = $builder->get("contract_place");
        $data["custom_provisions"] = $builder->get("custom_provisions", false);


    @endphp

    @include($builder->render_view, ['builder' => $builder, 'data' => $data])

@endsection
