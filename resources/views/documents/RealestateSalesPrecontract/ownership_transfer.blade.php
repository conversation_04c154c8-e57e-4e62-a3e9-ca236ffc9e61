@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                <div class="card-header">
                    Preuzimanje nekretnine
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'ownership_transfer_time',(empty($model->document->getValue('additional_buyers')) ? ('1. Kada kupac stupa u posjed '. (empty($model->document->getValue('additional_realestates')) ? 'nekretnine?' : 'nekretnina?')) : ('2. Kada kupci stupaju u posjed '. (empty($model->document->getValue('additional_realestates')) ? 'nekretnine?' : 'nekretnina?')))) }}
                            {{ Form::fRadio($model, 'ownership_transfer_time', 'when_paid', 'Odmah po isplati kupoprodajne cijene u cijelosti', ['checked' => !isset($model->ownership_transfer_time)])}}
                            {{ Form::fRadio($model, 'ownership_transfer_time', 'when_contract_signed', 'Odmah po sklapanju glavnog ugovora o kupoprodaji '.(empty($model->document->getValue('additional_realestates')) ? 'nekretnine' : 'nekretnina'))}}
                            <div class="form-check radio-input">
                                <label class="form-check-label" style="width:100%;">
                                    {{ Form::radio('ownership_transfer_time', 'custom', null, ['class' => 'form-check-input']) }}
                                    {{ Form::fText($model, 'ownership_transfer_time_custom', null, null, ['class' => 'form-control', 'placeholder' => 'Npr: u roku od 2 mjeseca od sklapanja glavnog ugovora o kupoprodaji '.(empty($model->document->getValue('additional_realestates')) ? 'nekretnine' : 'nekretnina'), 'data-force-start-case' => 'lower']) }}
                                </label>
                            </div>
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'includes_movables', '2. '.(empty($model->document->getValue('additional_sellers')) ? 'Prepušta li prodavatelj' : 'Prepuštaju li prodavatelji').' '.(empty($model->document->getValue('additional_buyers')) ? 'kupcu' : 'kupcima').' u cijeni '.(empty($model->document->getValue('additional_realestates')) ? 'nekretnine' : 'nekretnina').' i neke pokretnine čija je vrijednost uključena u kupoprodajnu cijenu?') }}
                            {{ Form::fRadio($model, 'includes_movables', 0, 'Ne', ['checked' => !isset($model->includes_movables), 'id' => 'does_not_include_movables']) }}
                            {{ Form::fRadio($model, 'includes_movables', 1, 'Da', ['id' => 'includes_movables']) }}
                        </div>
                    </div>
                    <div id="movables_container" style="@if(empty($model->includes_movables)) display:none; @endif">
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'movables', '<span class="dot"></span> Upiši pokretnine koje prodavatelj prepušta kupcu') }}
                                <div id="movables" class="dynamic">
                                    <div id="movable_template" class="d-none">
                                        <div class="movable_content">
                                            <div class="input-group mt-2 mb-2">
                                                <input placeholder="Upiši..." type="text"
                                                       class="movables form-control"
                                                       name="movables[{INDEX}]">
                                                <div class="input-group-append remove_movable"
                                                     style="cursor: pointer;">
                                                <span class="input-group-text">
                                                    <i class="fa fa-trash"></i>
                                                </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    @if(isset($model->movables))
                                        @foreach($model->movables as $i => $_movable)
                                            <div class="movable_content">
                                                <div class="input-group mt-2 mb-2">
                                                    <input value="{{ $model->movables[$i] }}"
                                                           placeholder="Upiši..." type="text"
                                                           class="movables form-control"
                                                           name="movables[{{$i}}]">
                                                    <div class="input-group-append remove_movable"
                                                         style="cursor: pointer;">
                                                <span class="input-group-text">
                                                    <i class="fa fa-trash"></i>
                                                </span>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    @else
                                        <div class="movable_content">
                                            <div class="input-group mt-2 mb-2">
                                                <input placeholder="Npr: kuhinja koja se sastoji od 8 elemenata"
                                                       type="text" class="movables form-control"
                                                       name="movables[0]">
                                                <div class="input-group-append remove_movable"
                                                     style="cursor: pointer;">
                                                <span class="input-group-text">
                                                    <i class="fa fa-trash"></i>
                                                </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="movable_content">
                                            <div class="input-group mt-2 mb-2">
                                                <input placeholder="Npr:  hladnjak marke „Bosch“"
                                                       type="text" class="movables form-control"
                                                       name="movables[1]">
                                                <div class="input-group-append remove_movable"
                                                     style="cursor: pointer;">
                                                <span class="input-group-text">
                                                    <i class="fa fa-trash"></i>
                                                </span>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <a class="btn btn-info mt-2" id="add_movable">Dodaj još</a>

                            </div>
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'is_seller_liable', '3. Želiš li u ugovor uključiti odredbu viđeno-kupljeno o isključenju odgovornosti prodavatelja za materijalne nedostatke na '.(empty($model->document->getValue('additional_realestates')) ? 'nekretnini?' : 'nekretninama?'), 'Prema Zakonu o obveznim odnosima, ugovaratelji mogu ograničiti ili sasvim isključiti prodavateljevu odgovornost za materijalne nedostatke stvari. Međutim, treba voditi računa o tome da je odredba ugovora o ograničenju ili potpunom isključenju odgovornosti za materijalne nedostatke ništetna ako je nedostatak bio poznat prodavatelju, a on o njemu nije obavijestio kupca, a i onda kad je prodavatelj nametnuo tu odredbu koristeći se svojim monopolskim položajem te ako se radi o potrošačkom ugovoru.') }}
                            {{ Form::fRadio($model, 'is_seller_liable', 1, 'Da', ['checked' => !isset($model->is_seller_liable)]) }}
                            {{ Form::fRadio($model, 'is_seller_liable', 0, 'Ne') }}
                        </div>
                    </div>

                    <hr/>

                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'clausula_intabulandi', '4. Kada će '.(empty($model->document->getValue('additional_sellers')) ? 'prodavatelj' : 'prodavatelji').' '.(empty($model->document->getValue('additional_buyers')) ? 'kupcu' : 'kupcima').' izdati dopuštenje za uknjižbu prava vlasništva tj. tabularnu izjavu?') }}
                            {{ Form::fRadio($model, 'clausula_intabulandi', 0,  (empty($model->document->getValue('additional_sellers')) ? 'Prodavatelj' : 'Prodavatelji').' će dopuštenje za uknjižbu prava vlasništva izdati i predati '.(empty($model->document->getValue('additional_buyers')) ? 'kupcu' : 'kupcima').' odmah po plaćanju kupoprodajne cijene u cijelosti', ['checked' => !isset($model->clausula_intabulandi)])}}
                            {{ Form::fRadio($model, 'clausula_intabulandi', 1, 'Bezuvjetno dopuštenje za uknjižbu prava vlasništva bit će sastavni dio glavnog ugovora o kupoprodaji '.(empty($model->document->getValue('additional_realestates')) ? 'nekretnine' : 'nekretnina').' te će '.(empty($model->document->getValue('additional_buyers')) ? 'kupac' : 'kupci').' moći uknjižiti pravo vlasništva odmah po sklapanju tog ugovora')}}
                            {{ Form::fRadio($model, 'clausula_intabulandi', 2, (empty($model->document->getValue('additional_sellers')) ? 'Prodavatelj' : 'Prodavatelji').' će dopuštenje za uknjižbu prava vlasništva izdati istovremeno sa sklapanjem glavnog ugovora o kupoprodaji '.(empty($model->document->getValue('additional_realestates')) ? 'nekretnine' : 'nekretnina').', s time da će ga deponirati kod odabranog javnog bilježnika ili odvjetnika koji će ga predati '.(empty($model->document->getValue('additional_buyers')) ? 'kupcu' : 'kupcima').' odmah po primitku potvrde o plaćenoj kupoprodajnoj cijeni u cijelosti')}}
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>


    {{ Form::close() }}
@endsection

