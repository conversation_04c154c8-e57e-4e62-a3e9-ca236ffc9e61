<div class="editable-segment" data-section="parties" data-type="parties">
    @if(!empty($data['additional_sellers']))
        <p>

            <strong>{!! $data['seller_name'] !!},</strong> {!! $data['seller_address'] !!}, {!! $data['seller_postal_code'] !!}
            {!! $data['seller_city'] !!}, {!! $data['seller_country'] !!}, OIB: {!! $data['seller_oib'] !!}@if(!empty($data['authorized_seller_persons']) || $data['seller_cosigner_exists']), kao prodavatelj,@elseif(count($data['additional_sellers'])==1), kao prodavatelj (u daljnjem tekstu: <strong>Prodavatelj {!! $data['seller_name'] !!}</strong>) <br>i @else, kao prodavatelj (u daljnjem tekstu: <strong>Prodavatelj {!! $data['seller_name'] !!}</strong>),@endif

            @if(!empty($data['authorized_seller_persons']))
                @if(count($data['authorized_seller_persons']) == 1)
                    kojeg zastupa
                @else
                    kojeg zastupaju
                @endif

                @foreach($data['authorized_seller_persons'] as $_i => $_authorized_person)
                        {!! $_authorized_person['role'] !!} {!! $_authorized_person['name']  !!}<span></span>@if($_authorized_person['has_entered_address']),
                        {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder !!},
                        {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                        {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder !!}@endif<span></span>@if(!empty($_authorized_person['oib'])), OIB: {!! $_authorized_person['oib'] !!}@endif<span></span>@if($_i < (count($data['authorized_seller_persons'])-1))@if(($_i+2) == count($data['authorized_seller_persons'])) i @else, @endif @elseif($data['seller_cosigner_exists']), @elseif(count($data['additional_sellers']) > 1 ) (u daljnjem tekstu: <strong>Prodavatelj {!! $data['seller_name'] !!}</strong>),@else() (u daljnjem tekstu: <strong>Prodavatelj {!! $data['seller_name'] !!}</strong>) <br>i @endif
                @endforeach

            @endif

            @if($data['seller_cosigner_exists'])
                uz suglasnost koju za sklapanje ovog Ugovora daje {!! mb_strtolower($data['seller_cosigner_type'], 'UTF-8') !!}
                {!! $data["seller_cosigner_name"] !!}, {!! $data["seller_cosigner_address"] !!}, {!! $data["seller_cosigner_postal_code"] !!} {!! $data["seller_cosigner_city"] !!}, {!! $data["seller_cosigner_country"] !!}, OIB: {!! $data["seller_cosigner_oib"] !!}@if(count($data['additional_sellers'])>1) (u daljnjem tekstu: <strong>Prodavatelj {!! $data['seller_name'] !!}</strong>), @else() (u daljnjem tekstu: <strong>Prodavatelj {!! $data['seller_name'] !!}</strong>) <br>i @endif
            @endif

            @foreach($data['additional_sellers'] as $_i => $additional_seller)
                <br>
                {!! !empty($additional_seller['name']) ? "<strong>".$additional_seller['name']."</strong>" : $builder::$placeholder  !!},
                {!! !empty($additional_seller['address']) ? $additional_seller['address'] : $builder::$placeholder !!},
                {!! !empty($additional_seller['postal_code']) ? $additional_seller['postal_code'] : $builder::$placeholder !!}
                {!! !empty($additional_seller['city']) ? $additional_seller['city'] : $builder::$placeholder !!},
                {!! !empty($additional_seller['country']) ? $additional_seller['country'] : $builder::$placeholder !!}, OIB:
                {!! !empty($additional_seller['oib']) ? $additional_seller['oib'] : $builder::$placeholder !!}@if(!empty($data['authorized_additional_seller_persons'][$_i]) || !empty($additional_seller['seller_cosigner_exists'])), kao prodavatelj,@elseif( (count($data['additional_sellers']) - $_i) > 2), kao prodavatelj (u daljnjem tekstu: <strong>Prodavatelj {!! !empty($additional_seller['name']) ? $additional_seller['name'] : $builder::$placeholder  !!}</strong>),@elseif($_i != count($data['additional_sellers'])-1), kao prodavatelj (u daljnjem tekstu: <strong>Prodavatelj {!! !empty($additional_seller['name']) ? $additional_seller['name'] : $builder::$placeholder  !!}</strong>) <br>i @else, kao prodavatelj (u daljnjem tekstu: <strong>Prodavatelj {!! !empty($additional_seller['name']) ? $additional_seller['name'] : $builder::$placeholder  !!}</strong>), @endif
                @if(!empty($data['authorized_additional_seller_persons'][$_i]))
                    @if(count($data['authorized_additional_seller_persons'][$_i]) == 1)
                        kojeg zastupa
                    @else
                        kojeg zastupaju
                    @endif

                    @foreach($data['authorized_additional_seller_persons'][$_i] as $_j => $_authorized_person)
                            {!! $_authorized_person['role'] ?: $builder::$placeholder !!} {!! $_authorized_person['name'] ?: $builder::$placeholder !!}<span></span>@if($_authorized_person['has_entered_address']),
                            {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder !!},
                            {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                            {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder !!}@endif<span></span>@if(!empty($_authorized_person['oib'])), OIB: {!! $_authorized_person['oib'] !!}@endif<span></span>@if($_j < (count($data['authorized_additional_seller_persons'][$_i])-1))@if(($_j+2) == count($data['authorized_additional_seller_persons'][$_i])) i @else, @endif @elseif(isset($data['additional_sellers'][$_i+1]))@if(count($data['additional_sellers']) == $_i+2)@if(empty($additional_seller['seller_cosigner_exists'])) (u daljnjem tekstu: <strong>Prodavatelj {!! !empty($additional_seller['name']) ? $additional_seller['name'] : $builder::$placeholder  !!}</strong>) <br> i @else, @endif @endif	@elseif(empty($additional_seller['seller_cosigner_exists'])) (u daljnjem tekstu: <strong>Prodavatelj {!! !empty($additional_seller['name']) ? $additional_seller['name'] : $builder::$placeholder  !!}</strong>), @else, @endif
                    @endforeach
                @endif

                @if(!empty($additional_seller['seller_cosigner_exists']))
                    uz suglasnost koju za sklapanje ovog Ugovora daje {!! mb_strtolower($additional_seller['seller_cosigner_type'], 'UTF-8') !!}
                    {!! !empty($additional_seller['seller_cosigner_name']) ? $additional_seller['seller_cosigner_name'] : $builder::$placeholder !!}, {!! !empty($additional_seller['seller_cosigner_address']) ? $additional_seller['seller_cosigner_address'] : $builder::$placeholder !!}, {!! !empty($additional_seller['seller_cosigner_postal_code']) ? $additional_seller['seller_cosigner_postal_code'] : $builder::$placeholder !!} {!! !empty($additional_seller['seller_cosigner_city']) ? $additional_seller['seller_cosigner_city'] : $builder::$placeholder !!}, {!! !empty($additional_seller['seller_cosigner_country']) ? $additional_seller['seller_cosigner_country'] : $builder::$placeholder !!}, OIB: {!! !empty($additional_seller['seller_cosigner_oib']) ? $additional_seller['seller_cosigner_oib'] : $builder::$placeholder !!}@if($_i == (count($data['additional_sellers'])-2)) (u daljnjem tekstu: <strong>Prodavatelj {!! !empty($additional_seller['name']) ? $additional_seller['name'] : $builder::$placeholder  !!}</strong>) <br>i  @else() (u daljnjem tekstu: <strong>Prodavatelj {!! !empty($additional_seller['name']) ? $additional_seller['name'] : $builder::$placeholder  !!}</strong>), @endif
                @endif

            @endforeach

            <br><br>(svi prodavatelji u daljnjem tekstu zajednički:  <strong>Prodavatelji</strong>)
        </p>
    @else
        <p>

            <strong>{!! $data['seller_name'] !!},</strong> {!! $data['seller_address'] !!}, {!! $data['seller_postal_code'] !!}
            {!! $data['seller_city'] !!}, {!! $data['seller_country'] !!}, OIB: {!! $data['seller_oib'] !!}@if(!empty($data['authorized_seller_persons']) || $data['seller_cosigner_exists']), kao prodavatelj,@else, kao prodavatelj<br/><br/>(u daljnjem tekstu: <strong>Prodavatelj</strong>)@endif

            @if(!empty($data['authorized_seller_persons']))
                @if(count($data['authorized_seller_persons']) == 1)
                    kojeg zastupa
                @else
                    kojeg zastupaju
                @endif

                @foreach($data['authorized_seller_persons'] as $_i => $_authorized_person)
                        {!! $_authorized_person['role'] !!} {!! $_authorized_person['name']  !!}<span></span>@if($_authorized_person['has_entered_address']),
                        {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder !!},
                        {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                        {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder !!}@endif<span></span>@if(!empty($_authorized_person['oib'])), OIB: {!! $_authorized_person['oib'] !!}@endif<span></span>@if($_i < (count($data['authorized_seller_persons'])-1))@if(($_i+2) == count($data['authorized_seller_persons'])) i @else, @endif @elseif($data['seller_cosigner_exists']), @else(), <br/><br/> (u daljnjem tekstu: <strong>Prodavatelj</strong>) @endif
                @endforeach

            @endif

            @if($data['seller_cosigner_exists'])
                uz suglasnost koju za sklapanje ovog Ugovora daje {!! mb_strtolower($data['seller_cosigner_type'], 'UTF-8') !!}
                {!! $data["seller_cosigner_name"] !!}, {!! $data["seller_cosigner_address"] !!}, {!! $data["seller_cosigner_postal_code"] !!} {!! $data["seller_cosigner_city"] !!}, {!! $data["seller_cosigner_country"] !!}, OIB: {!! $data["seller_cosigner_oib"] !!}, <br/><br/> (u daljnjem tekstu: <strong>Prodavatelj</strong>)
            @endif


        </p>
    @endif


    <p>i</p>

    @if(!empty($data['additional_buyers']))
        <p>

            <strong>{!! $data['buyer_name'] !!},</strong> {!! $data['buyer_address'] !!}, {!! $data['buyer_postal_code'] !!}
            {!! $data['buyer_city'] !!}, {!! $data['buyer_country'] !!}, OIB: {!! $data['buyer_oib'] !!}@if(!empty($data['authorized_buyer_persons'])), kao kupac,@elseif(count($data['additional_buyers'])==1), kao kupac (u daljnjem tekstu: <strong>Kupac {!! $data['buyer_name'] !!}</strong>) <br>i @else, kao kupac (u daljnjem tekstu: <strong>Kupac {!! $data['buyer_name'] !!}</strong>),@endif

            @if(!empty($data['authorized_buyer_persons']))
                @if(count($data['authorized_buyer_persons']) == 1)
                    kojeg zastupa
                @else
                    kojeg zastupaju
                @endif

                @foreach($data['authorized_buyer_persons'] as $_i => $_authorized_person)
                        {!! $_authorized_person['role'] !!} {!! $_authorized_person['name']  !!}<span></span>@if($_authorized_person['has_entered_address']),
                        {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder !!},
                        {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                        {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder !!}@endif<span></span>@if(!empty($_authorized_person['oib'])), OIB: {!! $_authorized_person['oib'] !!}@endif<span></span>@if($_i < (count($data['authorized_buyer_persons'])-1))@if(($_i+2) == count($data['authorized_buyer_persons'])) i @else, @endif @elseif(count($data['additional_buyers'])>1) (u daljnjem tekstu: <strong>Kupac {!! $data['buyer_name'] !!}</strong>),@else() (u daljnjem tekstu: <strong>Kupac {!! $data['buyer_name'] !!}</strong>) <br>i @endif
                @endforeach

            @endif

            @foreach($data['additional_buyers'] as $_i => $additional_buyer)
                <br>
                {!! !empty($additional_buyer['name']) ? "<strong>".$additional_buyer['name']."</strong>" : $builder::$placeholder  !!},
                {!! !empty($additional_buyer['address']) ? $additional_buyer['address'] : $builder::$placeholder !!},
                {!! !empty($additional_buyer['postal_code']) ? $additional_buyer['postal_code'] : $builder::$placeholder !!}
                {!! !empty($additional_buyer['city']) ? $additional_buyer['city'] : $builder::$placeholder !!},
                {!! !empty($additional_buyer['country']) ? $additional_buyer['country'] : $builder::$placeholder !!}, OIB:
                {!! !empty($additional_buyer['oib']) ? $additional_buyer['oib'] : $builder::$placeholder !!}@if(!empty($data['authorized_additional_buyer_persons'][$_i])), kao kupac,@elseif( (count($data['additional_buyers']) - $_i) > 2), kao kupac (u daljnjem tekstu: <strong>Kupac {!! !empty($additional_buyer['name']) ? $additional_buyer['name'] : $builder::$placeholder !!}</strong>),@elseif($_i != count($data['additional_buyers'])-1), kao kupac (u daljnjem tekstu: <strong>Kupac {!! !empty($additional_buyer['name']) ? $additional_buyer['name'] : $builder::$placeholder !!}</strong>) <br>i @else, kao kupac (u daljnjem tekstu: <strong>Kupac {!! !empty($additional_buyer['name']) ? $additional_buyer['name'] : $builder::$placeholder !!}</strong>), @endif
                @if(!empty($data['authorized_additional_buyer_persons'][$_i]))
                    @if(count($data['authorized_additional_buyer_persons'][$_i]) == 1)
                        kojeg zastupa
                    @else
                        kojeg zastupaju
                    @endif

                    @foreach($data['authorized_additional_buyer_persons'][$_i] as $_j => $_authorized_person)
                            {!! $_authorized_person['role'] ?: $builder::$placeholder !!} {!! $_authorized_person['name'] ?: $builder::$placeholder !!}<span></span>@if($_authorized_person['has_entered_address']),
                            {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder !!},
                            {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                            {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder !!}@endif<span></span>@if(!empty($_authorized_person['oib'])), OIB: {!! $_authorized_person['oib'] !!}@endif<span></span>@if($_j < (count($data['authorized_additional_buyer_persons'][$_i])-1))@if(($_j+2) == count($data['authorized_additional_buyer_persons'][$_i])) i @else, @endif @elseif(isset($data['additional_buyers'][$_i+1]))@if(count($data['additional_buyers']) == $_i+2) (u daljnjem tekstu: <strong>Kupac {!! !empty($additional_buyer['name']) ? $additional_buyer['name'] : $builder::$placeholder !!}</strong>) <br>i @else() (u daljnjem tekstu: <strong>Kupac {!! !empty($additional_buyer['name']) ? $additional_buyer['name'] : $builder::$placeholder !!}</strong>), @endif @else() (u daljnjem tekstu: <strong>Kupac {!! !empty($additional_buyer['name']) ? $additional_buyer['name'] : $builder::$placeholder !!}</strong>), @endif
                    @endforeach
                @endif

            @endforeach

            <br><br>(svi kupci u daljnjem tekstu zajednički:  <strong>Kupci</strong>)
        </p>
    @else
        <p>
            <strong>{!! $data['buyer_name'] !!},</strong> {!! $data['buyer_address'] !!}, {!! $data['buyer_postal_code'] !!}
            {!! $data['buyer_city'] !!}, {!! $data['buyer_country'] !!}, OIB: {!! $data['buyer_oib'] !!},

            @if(!empty($data['authorized_buyer_persons']))
                @if(count($data['authorized_buyer_persons']) == 1)
                    kao kupac kojeg zastupa
                @else
                    kao kupac kojeg zastupaju
                @endif

                @foreach($data['authorized_buyer_persons'] as $_i => $_authorized_person)
                        {!! $_authorized_person['role'] !!} {!! $_authorized_person['name']  !!}<span></span>@if($_authorized_person['has_entered_address']),
                        {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder !!},
                        {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                        {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder !!}@endif<span></span>@if(!empty($_authorized_person['oib'])), OIB: {!! $_authorized_person['oib'] !!}@endif<span></span>@if($_i < (count($data['authorized_buyer_persons'])-1))@if(($_i+2) == count($data['authorized_buyer_persons'])) i @else, @endif @else, @endif
                @endforeach

            @else
                kao kupac
            @endif

            <br><br>(u daljnjem tekstu: <strong>Kupac</strong>)
        </p>
    @endif


    <p>
        sklopili su sljedeći:
    </p>
</div>

<h2 class="text-center editable-segment" data-type="title">
    PREDUGOVOR O KUPOPRODAJI @if(!empty($data['additional_realestates'])) NEKRETNINA @else NEKRETNINE @endif
</h2>

<div class="editable-segment" data-section="conditions" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(1, true) }}. Predmet predugovora
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke ovim Predugovorom preuzimaju obvezu međusobno sklopiti glavni Ugovor o kupoprodaji {{ !empty($data['additional_realestates']) ? "nekretnina pobliže opisanih" : "nekretnine pobliže opisane" }}
            u članku <span class="article-reference">{{ $builder->getArticleIndex(2) }}</span>. ovog Predugovora
            @if($data['contract_conclusion_deadline'] == 'custom')
                {!! $data['contract_conclusion_deadline_custom'] !!}
            @else
                najkasnije dana {!! $data['contract_conclusion_deadline_by_latest'] !!} godine,
            @endif

            i to pod uvjetima i sa sadržajem utvrđenim ovim Predugovorom (u daljnjem tekstu: Glavni ugovor).
        </p>
    </div>
</div>

<div class="editable-segment" data-section="realestate" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(2, true) }}. @if(!empty($data['additional_realestates'])) Nekretnine koje su @else Nekretnina koja je @endif predmet kupoprodaje
    </p>
    <div class="article-body">
        @php
            $singleton = \App\Helpers\ViewSingleton::getInstance();
            $singleton->p_index = 1;
        @endphp

        @include('documents.RealestateSalesPrecontract.template.partials.realestate', [
            'data' => $data,
            'singleton' => $singleton,
            'article_index' => $builder->getCurrentArticleIndex(),
            'realestate_index_nominativ' => !empty($data['additional_realestates']) ? 'Nekretnina 1' : 'Nekretnina',
            'realestate_index_genitiv' => !empty($data['additional_realestates']) ? 'Nekretnine 1' : 'Nekretnine',
            'realestate_index_akuzativ' => !empty($data['additional_realestates']) ? 'Nekretninu 1' : 'Nekretninu',
            'seller_has_full_realestate_ownership' => $data['seller_has_full_realestate_ownership'] ?? true,
            'additional_sellers' => $data['additional_sellers'],
            'additional_buyers' => $data['additional_buyers'],
            'seller_name' => $data['seller_name'],
            'buyer_name' => $data['buyer_name'],
        ])

        @if(!empty($data['additional_realestates']))

            @foreach($data['additional_realestates'] as $_aditional_realestate_index => $_data)
                @include('documents.RealestateSalesPrecontract.template.partials.realestate', [
                    'data' => $_data,
                    'singleton' => $singleton,
                    'article_index' => $builder->getCurrentArticleIndex(),
                    'realestate_index_nominativ' => 'Nekretnina '.($_aditional_realestate_index+2),
                    'realestate_index_genitiv' => 'Nekretnine '.($_aditional_realestate_index+2),
                    'realestate_index_akuzativ' => 'Nekretninu '.($_aditional_realestate_index+2),
                    'seller_has_full_realestate_ownership' => $_data['seller_has_full_realestate_ownership'] ?? true,
                    'additional_sellers' => $data['additional_sellers'],
                    'additional_buyers' => $data['additional_buyers'],
                    'seller_name' => $data['seller_name'],
                    'buyer_name' => $data['buyer_name'],
                ])
            @endforeach

        @endif
    </div>
</div>

@php $dynamic_index = 3; @endphp

@if($data["is_cancellation_fee_agreed"])
    <div class="editable-segment" data-type="article">
        <p class="article-header">
            Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Kapara kao odustatnina
        </p>
        <div class="article-body">
            @php $p_index = 1; @endphp
            @if(!isset($data["cancellation_fee_payment_deadline"]) || $data["cancellation_fee_payment_deadline"] == "when_contract_signed")
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Potpisom ovog Predugovora,
                    {{ !empty($data['additional_sellers']) ? "Prodavatelji potvrđuju" : "Prodavatelj potvrđuje" }} da
                    {{ !empty($data['additional_buyers']) ? "su Kupci" : "je Kupac" }}
                    na ime kapare kao odustatnine u trenutku potpisivanja ovog Predugovora
                    {{ !empty($data['additional_sellers']) ? "Prodavateljima" : "Prodavatelju" }}
                    {{ !empty($data['additional_buyers']) ? "isplatili" : "isplatio" }}
                    iznos od {!! StringHelper::amountToText($data['cancellation_fee_amount']) !!}, i to
                    @if(!isset($data['cancellation_fee_payment_method']) || $data['cancellation_fee_payment_method'] == 'iban')
                        na bankovni račun IBAN {!! $data['cancellation_fee_payment_method_iban'] !!}
                    @else
                        u gotovini
                    @endif()
                    (u daljnjem tekstu: Kapara).
                </p>
            @else
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    {{ !empty($data['additional_buyers']) ? "Kupci se obvezuju" : "Kupac se obvezuje" }}
                    na ime kapare kao odustatnine
                    {!! $data['cancellation_fee_payment_deadline_custom'] !!}
                    {{ !empty($data['additional_sellers']) ? "Prodavateljima" : "Prodavatelju" }} isplatiti
                    iznos od {!! StringHelper::amountToText($data['cancellation_fee_amount']) !!}, i to
                    @if(!isset($data['cancellation_fee_payment_method']) || $data['cancellation_fee_payment_method'] == 'iban')
                        na bankovni račun IBAN {!! $data['cancellation_fee_payment_method_iban'] !!}
                    @else
                        u gotovini
                    @endif()
                    (u daljnjem tekstu: Kapara).
                </p>
            @endif


            @if(!isset($data["cancellation_fee_price_inclusion"]) || $data["cancellation_fee_price_inclusion"] == "included")
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Ugovorne stranke suglasno utvrđuju da se Kapara uračunava u kupoprodajnu cijenu iz članka <span class="article-reference">{{ $data['purchase_price_article_index'] }}</span>. ovog Predugovora.
                </p>
            @else
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Ugovorne stanke suglasno utvrđuju da se Kapara ne uračunava u kupoprodajnu cijenu iz članka <span class="article-reference">{{ $data['purchase_price_article_index'] }}</span>. ovog Predugovora.
                    {{ !empty($data['additional_sellers']) ? "Prodavatelji se obvezuju" : "Prodavatelj se obvezuje" }} Kaparu vratiti
                    {{ !empty($data['additional_buyers']) ? "Kupcima" : "Kupcu" }}
                    @if($data['cancellation_fee_refund_deadline'] != "custom")
                        odmah po isplati ukupne kupoprodajne cijene iz članka <span class="article-reference">{{ $data['purchase_price_article_index'] }}</span>. ovog Predugovora u cijelosti
                    @else
                        {!! $data['cancellation_fee_refund_deadline_custom'] !!}
                    @endif
                    i to
                    @if(!isset($data['cancellation_fee_refund_method']) || $data['cancellation_fee_refund_method'] == 'iban')
                        na bankovni račun IBAN {!! $data['cancellation_fee_refund_method_iban'] !!}.
                    @else
                        u gotovini.
                    @endif
                </p>
            @endif

            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ugovorne stranke suglasno utvrđuju da
                {{ !empty($data['additional_buyers']) ? "Kupci imaju pravo" : "Kupac ima pravo" }}
                na vraćanje dvostruke Kapare ako od sklapanja Glavnog ugovora
                {{ !empty($data['additional_sellers']) ? "odustanu Prodavatelji" : "odustane Prodavatelj" }},
                a ako od sklapanja Glavnog ugovora
                {{ !empty($data['additional_buyers']) ? "odustanu Kupci" : "odustane Kupac" }},
                {{ !empty($data['additional_sellers']) ? "Prodavatelji imaju pravo " : "Prodavatelj ima pravo" }}
                zadržati primljenu Kaparu.
            </p>
        </div>
    </div>
@else
    @php $builder->skipped_articles[] = $dynamic_index++; @endphp
@endif

@if($data['financing_method'] != "own")
    <div class="editable-segment" data-type="article">
        <p class="article-header">
            Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Financiranje kupoprodajne cijene
        </p>
        <div class="article-body">
            @php $p_index = 1; @endphp
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                {{ !empty($data['additional_sellers']) ? "Prodavatelji primaju na znanje i suglasni su" : "Prodavatelj prima na znanje i suglasan je" }} s time da će
                {{ !empty($data['additional_buyers']) ? "Kupci" : "Kupac" }}  kupoprodajnu cijenu iz članka <span class="article-reference">{{ $data['purchase_price_article_index'] }}</span>. ovog Predugovora

                @if(($data["is_cancellation_fee_agreed"]) && (!isset($data["cancellation_fee_price_inclusion"]) || $data["cancellation_fee_price_inclusion"] == "included"))
                    umanjenu za Kaparu
                @endif

                @if($data['financing_method'] == "loan+own")
                    djelomično
                @else
                    u cijelosti
                @endif
                isplatiti
                {{ !empty($data['additional_sellers']) ? "Prodavateljima" : "Prodavatelju" }} iz sredstava kredita koji će
                {{ !empty($data['additional_buyers']) ? "Kupci" : "Kupac" }} zatražiti od kreditne institucije
                @if(!isset($data['is_credit_institution_determined']) || $data['is_credit_institution_determined'])
                    {!! $data['credit_institution_name'] !!}
                @else
                    po svom izboru
                @endif
                @if($data['is_loan_subsidized'])
                    (u daljnjem tekstu: Davatelj kredita), uz podnošenje zahtjeva za subvencioniranje kredita Agenciji za pravni promet i posredovanje nekretninama (u daljnjem tekstu: APN).
                @else()
                    (u daljnjem tekstu: Davatelj kredita).
                @endif
            </p>

            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                {{ !empty($data['additional_sellers']) ? "Prodavatelji se obvezuju" : "Prodavatelj se obvezuje" }}
                {{ !empty($data['additional_buyers']) ? "Kupcima" : "Kupcu" }}
                pružiti svu potrebnu pomoć vezano uz
                @if($data['is_loan_subsidized'])
                    odobravanje kredita od strane Davatelja kredita i vezano uz odobravanje zahtjeva za subvencioniranje kredita od strane APN-a,
                @else
                    odobravanje kredita od strane Davatelja kredita,
                @endif
                između ostalog, tako da
                {{ !empty($data['additional_sellers']) ? "pribave" : "pribavi" }}
                i
                {{ !empty($data['additional_buyers']) ? "Kupcima" : "Kupcu" }}
                pravodobno
                {{ !empty($data['additional_sellers']) ? "stave" : "stavi" }}
                na raspolaganje svu za to potrebnu dokumentaciju koja se odnosi na
                {{ !empty($data['additional_realestates']) ? "Nekretnine" : "Nekretninu" }},
                {{ !empty($data['additional_sellers']) ? "omoguće" : "omogući" }}
                ovlaštenom procjenitelju pregled
                {{ !empty($data['additional_realestates']) ? "Nekretnina" : "Nekretnine" }}
                te
                {{ !empty($data['additional_sellers']) ? "ispune" : "ispuni" }}
                sve zahtjeve
                {{ !empty($data['additional_buyers']) ? "Kupaca" : "Kupca" }}
                odnosno Davatelja kredita koji se odnose na upis hipoteke na
                {{ !empty($data['additional_realestates']) ? "Nekretninama" : "Nekretnini" }}
                u korist Davatelja kredita (primjerice, izdavanje javnobilježnički ovjerene isprave podobne za provedbu uknjižbe založnog prava na
                {{ !empty($data['additional_realestates']) ? "Nekretninama" : "Nekretnini" }}
                od strane Davatelja kredita, sklapanje s Davateljem kredita javnobilježnički ovjerenog sporazuma o zasnivanju založnog prava na
                {{ !empty($data['additional_realestates']) ? "Nekretninama" : "Nekretnini" }}
                u svojstvu založnog dužnika i slično).
            </p>

            @if($data["is_cancellation_fee_agreed"])
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    {{ !empty($data['additional_sellers']) ? "Prodavatelji primaju na znanje i suglasni su s time da će se smatrati da su odustali" : "Prodavatelj prima na znanje i suglasan je s time da će se smatrati da je odustao" }}
                    od sklapanja Glavnog ugovora, ako pravodobno ne
                    {{ !empty($data['additional_sellers']) ? "ispune" : "ispuni" }}
                    obveze određene ovim člankom Predugovora.
                </p>
            @endif
        </div>
    </div>
@else
    @php $builder->skipped_articles[] = $dynamic_index++; @endphp
@endif

@if($data["is_any_mortgage_for_loan"])
    <div class="editable-segment" data-type="article">
        <p class="article-header">
            Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Postojeća hipoteka vezana uz kredit
        </p>
        <div class="article-body">
            @php
                $singleton->p_index = 1;
            @endphp

            @if($data['is_mortgage_for_loan'])
                @include('documents.RealestateSalesPrecontract.template.partials.mortgage', [
                    'data' => $data,
                    'singleton' => $singleton,
                    'article_index' => $builder->getCurrentArticleIndex(),
                    'builder' => $builder,
                    'additional_sellers' => $data['additional_sellers'],
                    'additional_buyers' => $data['additional_buyers'],
                    'realestate_index_nominativ' => !empty($data['additional_realestates']) ? 'Nekretnina 1' : 'Nekretnina',
                    'realestate_index_genitiv' => !empty($data['additional_realestates']) ? 'Nekretnine 1' : 'Nekretnine',
                    'realestate_index_akuzativ' => !empty($data['additional_realestates']) ? 'Nekretninu 1' : 'Nekretninu',
                ])
            @endif

            @if(!empty($data['additional_realestates']))

                @foreach($data['additional_realestates'] as $_aditional_realestate_index => $_additional_realestate)

                    @if(!empty($data['additional_realestate_conditions'][$_aditional_realestate_index]))
                        @if(!empty($data['additional_realestate_conditions'][$_aditional_realestate_index]['is_mortgage_for_loan']))
                            @include('documents.RealestateSalesPrecontract.template.partials.mortgage', [
                                'data' => $data['additional_realestate_conditions'][$_aditional_realestate_index],
                                'singleton' => $singleton,
                                'article_index' => $builder->getCurrentArticleIndex(),
                                'builder' => $builder,
                                'additional_sellers' => $data['additional_sellers'],
                                'additional_buyers' => $data['additional_buyers'],
                                'realestate_index_nominativ' => 'Nekretnina '.($_aditional_realestate_index+2),
                                'realestate_index_genitiv' => 'Nekretnine '.($_aditional_realestate_index+2),
                                'realestate_index_akuzativ' => 'Nekretninu '.($_aditional_realestate_index+2),
                            ])
                        @endif
                    @elseif(!empty($_additional_realestate['type_particular_has_encumbrances_sheet_data']) || !empty($_additional_realestate['type_flat_has_encumbrances_sheet_data']) || !empty($_additional_realestate['type_other_has_encumbrances_sheet_data']))
                        @include('documents.RealestateSalesPrecontract.template.partials.mortgage', [
                                'data' => null,
                                'singleton' => $singleton,
                                'article_index' => $builder->getCurrentArticleIndex(),
                                'builder' => $builder,
                                'additional_sellers' => $data['additional_sellers'],
                                'additional_buyers' => $data['additional_buyers'],
                                'realestate_index_nominativ' => 'Nekretnina '.($_aditional_realestate_index+2),
                                'realestate_index_genitiv' => 'Nekretnine '.($_aditional_realestate_index+2),
                                'realestate_index_akuzativ' => 'Nekretninu '.($_aditional_realestate_index+2),
                            ])
                    @endif

                @endforeach
            @endif

            @if($data["is_cancellation_fee_agreed"])
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $singleton->p_index++ }}.
                    {{ !empty($data['additional_sellers']) ? "Prodavatelji primaju na znanje i suglasni su s time da će se smatrati da su odustali" : "Prodavatelj prima na znanje i suglasan je s time da će se smatrati da je odustao" }}
                    od sklapanja Glavnog ugovora, ako u ugovorenom roku ne
                    {{ !empty($data['additional_sellers']) ? "ispune" : "ispuni" }}
                    obveze određene ovim člankom Predugovora.
                </p>
            @endif
        </div>
    </div>
@else
    @php $builder->skipped_articles[] = $dynamic_index++; @endphp
@endif


@if($data["is_any_realestate_tenant_purchase"])

    <div class="editable-segment" data-type="article">
        <p class="article-header">
            Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Postojeći otkup
        </p>
        <div class="article-body">
            @php
                $singleton->p_index = 1;
            @endphp

            @if($data['is_realestate_tenant_purchase'])
                @include('documents.RealestateSalesPrecontract.template.partials.tenant_purchase', [
                    'data' => $data,
                    'singleton' => $singleton,
                    'article_index' => $builder->getCurrentArticleIndex(),
                    'builder' => $builder,
                    'additional_sellers' => $data['additional_sellers'],
                    'additional_buyers' => $data['additional_buyers'],
                    'realestate_index_nominativ' => !empty($data['additional_realestates']) ? 'Nekretnina 1' : 'Nekretnina',
                    'realestate_index_genitiv' => !empty($data['additional_realestates']) ? 'Nekretnine 1' : 'Nekretnine',
                    'realestate_index_akuzativ' => !empty($data['additional_realestates']) ? 'Nekretninu 1' : 'Nekretninu',
                ])
            @endif

            @if(!empty($data['additional_realestate_conditions']))

                @foreach($data['additional_realestate_conditions'] as $_aditional_realestate_index => $_data)
                    @if(!empty($_data['is_realestate_tenant_purchase']))
                        @include('documents.RealestateSalesPrecontract.template.partials.tenant_purchase', [
                            'data' => $_data,
                            'builder' => $builder,
                            'singleton' => $singleton,
                            'article_index' => $builder->getCurrentArticleIndex(),
                            'additional_sellers' => $data['additional_sellers'],
                            'additional_buyers' => $data['additional_buyers'],
                            'realestate_index_nominativ' => 'Nekretnina '.($_aditional_realestate_index+2),
                            'realestate_index_genitiv' => 'Nekretnine '.($_aditional_realestate_index+2),
                            'realestate_index_akuzativ' => 'Nekretninu '.($_aditional_realestate_index+2),
                        ])
                    @endif
                @endforeach

            @endif

            @if($data['show_final_paragraph_tenant_purchase'])
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $singleton->p_index++ }}.
                    {{ !empty($data['additional_sellers']) ? "Prodavatelji primaju na znanje i suglasni su s time da će se smatrati da su odustali" : "Prodavatelj prima na znanje i suglasan je s time da će se smatrati da je odustao" }}
                    od sklapanja Glavnog ugovora, ako u ugovorenom roku ne
                    {{ !empty($data['additional_sellers']) ? "ispune" : "ispuni" }}
                    obveze određene ovim člankom Predugovora.
                </p>
            @endif
        </div>
    </div>
@else
    @php $builder->skipped_articles[] = $dynamic_index++; @endphp
@endif

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Energetski certifikat
    </p>
    <div class="article-body">
        @php
            $singleton->p_index = 1;
        @endphp

        @include('documents.RealestateSalesPrecontract.template.partials.energy_performance_certificate', [
            'data' => $data,
            'builder' => $builder,
            'singleton' => $singleton,
            'article_index' => $builder->getCurrentArticleIndex(),
            'additional_sellers' => $data['additional_sellers'],
            'additional_buyers' => $data['additional_buyers'],
            'realestate_index_nominativ' => !empty($data['additional_realestates']) ? 'Nekretnina 1' : 'Nekretnina',
            'realestate_index_genitiv' => !empty($data['additional_realestates']) ? 'Nekretnine 1' : 'Nekretnine',
            'realestate_index_akuzativ' => !empty($data['additional_realestates']) ? 'Nekretninu 1' : 'Nekretninu',
        ])

        @if(!empty($data['additional_realestate_conditions']))

            @foreach($data['additional_realestate_conditions'] as $_aditional_realestate_index => $_data)
                @include('documents.RealestateSalesPrecontract.template.partials.energy_performance_certificate', [
                    'data' => $_data,
                    'builder' => $builder,
                    'singleton' => $singleton,
                    'article_index' => $builder->getCurrentArticleIndex(),
                    'additional_sellers' => $data['additional_sellers'],
                    'additional_buyers' => $data['additional_buyers'],
                    'realestate_index_nominativ' => 'Nekretnina '.($_aditional_realestate_index+2),
                    'realestate_index_genitiv' => 'Nekretnine '.($_aditional_realestate_index+2),
                    'realestate_index_akuzativ' => 'Nekretninu '.($_aditional_realestate_index+2),
                ])
            @endforeach

        @endif

        @if($data["show_final_paragraph_energy_performance_certificate"])
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $singleton->p_index++ }}.
                {{ !empty($additional_sellers) ? "Prodavatelji primaju na znanje i suglasni su" : "Prodavatelj prima na znanje i suglasan je" }}
                s time da će se smatrati da
                {{ !empty($additional_sellers) ? "su odustali" : "je odustao" }}
                od sklapanja Glavnog ugovora, ako u ugovorenom roku ne
                {{ !empty($additional_sellers) ? "ispune" : "ispuni" }}
                obveze određene ovim člankom Predugovora.
            </p>
        @endif
    </div>
</div>

<div class="editable-segment" data-section="financial_information" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Kupoprodajna cijena
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da ukupna kupoprodajna cijena koju će se
            {{ !empty($data['additional_buyers']) ? "Kupci" : "Kupac" }}
            Glavnim ugovorom obvezati platiti za
            {{ !empty($data['additional_realestates']) ? "Nekretnine" : "Nekretninu" }}
            iznosi {!! StringHelper::amountToText($data['realestate_value_amount']) !!} (u daljnjem tekstu: Kupoprodajna cijena).
            @if($data['is_any_buyout_contract_financial_report_indisclosed'])
                Ugovorne stranke suglasno utvrđuju da je Kupoprodajna cijena konačna, fiksna i nepromjenjiva, osim u slučaju iz članka <span class="article-reference">{{ $builder->getArticleIndex(6) }}</span>. ovog Predugovora.
            @else
                Ugovorne stranke suglasno utvrđuju da je Kupoprodajna cijena konačna, fiksna i nepromjenjiva.
            @endif

        </p>

        @if($data['render_first_purchase_price_paragraph'])
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                {{ !empty($data['additional_buyers']) ? "Kupci" : "Kupac" }}
                će se Glavnim ugovorom Kupoprodajnu cijenu
                @if($data["is_cancellation_fee_agreed"])
                    @if(!isset($data["cancellation_fee_price_inclusion"]) || $data["cancellation_fee_price_inclusion"] == "included")
                        umanjenu za iznos Kapare
                    @endif
                @endif
                obvezati platiti
                {{ !empty($data['additional_sellers']) ? "Prodavateljima" : "Prodavatelju" }}
                @if($data['financing_method'] != "own")
                    @if($data['financing_method'] == "loan+own")
                        dijelom iz sredstava kredita opisanog u članku <span class="article-reference">{{ $builder->getArticleIndex(4) }}</span>. ovog Predugovora,
                        i to iznos od {!! StringHelper::amountToText($data['loan_amount']) !!},
                        i dijelom iz vlastitih sredstava, i to iznos od
                        {!! StringHelper::amountToText($data['own_funds_amount']) !!},
                    @else
                        u cijelosti iz sredstava kredita opisanog u članku <span class="article-reference">{{ $builder->getArticleIndex(4) }}</span>. ovog Predugovora,
                    @endif

                @else
                    u cijelosti iz vlastitih sredstava,
                @endif
                i to

                @if($data['payment_deadline'] != 'custom')
                    najkasnije dana {!! $data['payment_deadline_by_latest'] !!} godine,
                @else
                    {!! $data['payment_deadline_custom'] !!},
                @endif

                @if($data['payment_method'] != 'cash')
                    na bankovni račun IBAN {!! $data['payment_method_iban'] !!}.
                @else
                    u gotovini.
                @endif

            </p>
        @else
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                {{ !empty($data['additional_buyers']) ? "Kupci" : "Kupac" }}
                će se Glavnim ugovorom Kupoprodajnu cijenu
                @if($data["is_cancellation_fee_agreed"])
                    @if(!isset($data["cancellation_fee_price_inclusion"]) || $data["cancellation_fee_price_inclusion"] == "included")
                        umanjenu za iznos Kapare
                    @endif
                @endif
                obvezati platiti
                {{ !empty($data['additional_sellers']) ? "Prodavateljima" : "Prodavatelju" }}
                @if($data['financing_method'] != "own")
                    @if($data['financing_method'] == "loan+own")
                        dijelom iz sredstava kredita opisanog u članku <span class="article-reference">{{ $builder->getArticleIndex(4) }}</span>. ovog Predugovora,
                        i to iznos od {!! StringHelper::amountToText($data['loan_amount']) !!},
                        i dijelom iz vlastitih sredstava, i to iznos od
                        {!! StringHelper::amountToText($data['own_funds_amount']) !!},
                    @else
                        u cijelosti iz sredstava kredita opisanog u članku <span class="article-reference">{{ $builder->getArticleIndex(4) }}</span>. ovog Predugovora,
                    @endif

                @else
                    u cijelosti iz vlastitih sredstava,
                @endif
                i to

                @if($data['payment_deadline'] != 'custom')
                    najkasnije dana {!! $data['payment_deadline_by_latest'] !!} godine,
                @else
                    {!! $data['payment_deadline_custom'] !!},
                @endif

                kako slijedi:
            </p>

            <ul>
                <li>
                    iznos naveden u Pismu namjere izravno hipotekarnom vjerovniku sukladno uputama navedenima u Pismu namjere,
                </li>
                <li>
                    preostali iznos do punog iznosa Kupoprodajne cijene
                    @if($data["is_cancellation_fee_agreed"])
                        @if(!isset($data["cancellation_fee_price_inclusion"]) || $data["cancellation_fee_price_inclusion"] == "included")
                            umanjenog za iznos Kapare
                        @endif
                    @endif
                    {{ !empty($data['additional_sellers']) ? "Prodavateljima" : "Prodavatelju" }}
                    @if($data['payment_method'] != 'cash')
                        na bankovni račun IBAN {!! $data['payment_method_iban'] !!}.
                    @else
                        u gotovini.
                    @endif
                </li>
            </ul>
        @endif

        @if($data['is_deadline_essential_term'])
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ugovorne stranke suglasno utvrđuju da će plaćanje Kupoprodajne cijene u ugovorenom roku predstavljati bitni sastojak Glavnog ugovora te će se u slučaju da
                {{ !empty($data['additional_buyers']) ? "Kupci ne izvrše" : "Kupac ne izvrši" }}
                plaćanje u ugovorenom roku Glavni ugovor raskinuti po samom zakonu.
            </p>
        @endif
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Porezi, pristojbe i troškovi vezani uz sklapanje i provedbu predugovora i ugovora
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        @if($data['is_VAT_applied'])
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                {{ !empty($data['additional_sellers']) ? "Prodavatelji izjavljuju" : "Prodavatelj izjavljuje" }}
                da se na promet
                {{ !empty($additional_realestates) ? "Nekretnina" : "Nekretnine" }}
                plaća porez na dodanu vrijednost koji je uključen u sve iznose navedene u ovom Predugovoru.
            </p>

            @if($data['tax_payer'] == 'seller')
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    {{ !empty($data['additional_sellers']) ? "Prodavatelji se obvezuju" : "Prodavatelj se obvezuje" }}
                    snositi sve troškove u vezi sa sklapanjem ovog Predugovora i Glavnog ugovora,
                    kao i s njihovom provedbom u zemljišnim knjigama i drugim javnim upisnicima u koje se upisuje pravo vlasništva ili posjedovno stanje,
                    uključujući troškove radnji javnog bilježnika i sudske pristojbe.
                </p>
            @else
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    {{ !empty($data['additional_buyers']) ? "Kupci se obvezuju" : "Kupac se obvezuje" }}
                    snositi sve troškove u vezi sa sklapanjem ovog Predugovora i Glavnog ugovora,
                    kao i s njihovom provedbom u zemljišnim knjigama i drugim javnim upisnicima u koje se upisuje pravo vlasništva ili posjedovno stanje,
                    uključujući troškove radnji javnog bilježnika i sudske pristojbe.
                </p>
            @endif
        @else
            @if($data['tax_payer'] == 'seller')
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    {{ !empty($data['additional_sellers']) ? "Prodavatelji se obvezuju" : "Prodavatelj se obvezuje" }}
                    snositi porez na promet nekretnina i sve troškove u vezi sa sklapanjem ovog Predugovora i Glavnog ugovora,
                    kao i s njihovom provedbom u zemljišnim knjigama i drugim javnim upisnicima u koje se upisuje pravo vlasništva ili posjedovno stanje,
                    uključujući troškove radnji javnog bilježnika i sudske pristojbe.
                </p>
            @else
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    {{ !empty($data['additional_buyers']) ? "Kupci se obvezuju" : "Kupac se obvezuje" }}
                    snositi porez na promet nekretnina i sve troškove u vezi sa sklapanjem ovog Predugovora i Glavnog ugovora,
                    kao i s njihovom provedbom u zemljišnim knjigama i drugim javnim upisnicima u koje se upisuje pravo vlasništva ili posjedovno stanje,
                    uključujući troškove radnji javnog bilježnika i sudske pristojbe.
                </p>
            @endif
        @endif
    </div>
</div>

<div class="editable-segment" data-section="ownership_transfer" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Stupanje u posjed
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da će se Glavnim ugovorom
            {{ !empty($data['additional_sellers']) ? "Prodavatelji" : "Prodavatelj" }}
            obvezati
            {{ !empty($data['additional_buyers']) ? "Kupcima" : "Kupcu" }}
            predati u posjed
            {{ !empty($data['additional_realestates']) ? "Nekretnine slobodne" : "Nekretninu slobodnu" }}
            od osoba i stvari,
            @if($data['includes_movables'])
                izuzev stvari navedenih u članku <span class="article-reference">{{ $builder->getArticleIndex($dynamic_index) }}</span>. ovog Predugovora,
            @endif
            a
            {{ !empty($data['additional_buyers']) ? "Kupci" : "Kupac" }}
            obvezati stupiti u posjed
            {{ !empty($data['additional_realestates']) ? "Nekretnina" : "Nekretnine" }}
            @if(!isset($data['ownership_transfer_time']) || $data['ownership_transfer_time'] == 'when_paid')
                odmah po isplati Kupoprodajne cijene u cijelosti
            @elseif($data['ownership_transfer_time'] == 'when_contract_signed')
                odmah po sklapanju Glavnog ugovora
            @else
                {!! $data['ownership_transfer_time_custom'] !!}
            @endif()
            (u daljnjem tekstu: Dan predaje u posjed).
        </p>

        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da će se Glavnim ugovorom
            {{ !empty($data['additional_sellers']) ? "Prodavatelji" : "Prodavatelj" }}
            obvezati snositi sve režijske i druge troškove koji terete
            {{ !empty($data['additional_realestates']) ? "Nekretnine" : "Nekretninu" }}
            do Dana predaje u posjed
            {{ !empty($data['additional_buyers']) ? "Kupcima" : "Kupcu" }}
            odnosno do dolaska
            {{ !empty($data['additional_buyers']) ? "Kupaca" : "Kupca" }}
            u zakašnjenje u slučaju kašnjenja predaje
            {{ !empty($data['additional_realestates']) ? "Nekretnina" : "Nekretnine" }}
            u posjed
            {{ !empty($data['additional_buyers']) ? "Kupcima krivnjom Kupaca, a Kupci" : "Kupcu krivnjom Kupca, a Kupac" }}
            će se obvezati snositi sve poreze, doprinose, režijske i druge troškove koji terete
            {{ !empty($data['additional_realestates']) ? "Nekretnine" : "Nekretninu" }}
            od Dana predaje u posjed odnosno od dana dolaska
            {{ !empty($data['additional_buyers']) ? "Kupaca u zakašnjenje krivnjom Kupaca" : "Kupca u zakašnjenje krivnjom Kupca" }}
            nadalje. Istoga dana na
            {{ !empty($data['additional_buyers']) ? "Kupce" : "Kupca" }}
            će prijeći i svi rizici, uključujući rizik slučajne propasti i oštećenja
            {{ !empty($data['additional_realestates']) ? "Nekretnina" : "Nekretnine" }}
            te rizik nasilnog useljenja treće osobe.

        </p>
    </div>
</div>

@if($data['includes_movables'])
    <div class="editable-segment" data-type="article">
        <p class="article-header">
            Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Pokretnine uključene u cijenu
        </p>
        <div class="article-body">
            @php $p_index = 1; @endphp
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ugovorne stranke suglasno utvrđuju da će se Glavnim ugovorom
                {{ !empty($data['additional_sellers']) ? "Prodavatelji" : "Prodavatelj" }}
                obvezati
                {{ !empty($data['additional_buyers']) ? "Kupcima" : "Kupcu" }}
                zajedno i istovremeno s
                {{ !empty($data['additional_realestates']) ? "Nekretninama" : "Nekretninom" }}
                predati u vlasništvo i posjed sljedeće pokretnine:
            </p>
            <ul>

                @if(!empty($data['movables']))
                    @foreach($data['movables'] as $_movable)
                        <li>{!! !empty($_movable) ? $_movable : $builder::$placeholder !!}</li>
                    @endforeach
                @else
                    <li>{!! $builder::$placeholder !!}</li>
                @endif
            </ul>

            <p>
                (u daljnjem  tekstu: Pokretnine).
            </p>

            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ugovorne stranke suglasno utvrđuju da će Glavnim ugovorom vrijednost Pokretnina biti uključena u Kupoprodajnu cijenu te da
                {{ !empty($data['additional_buyers']) ? "Kupci" : "Kupac" }}
                neće imati obvezu platiti dodatnu cijenu za Pokretnine.
            </p>
        </div>
    </div>
@else
    @php $builder->skipped_articles[] = $dynamic_index++; @endphp
@endif

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Odgovornost za nedostatke
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        @if(!isset($data['is_seller_liable']) || $data['is_seller_liable'])
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ugovorne stranke suglasno utvrđuju da
                {{ !empty($data['additional_buyers']) ? "Kupci kupuju" : "Kupac kupuje" }}
                {{ !empty($data['additional_realestates']) ? "Nekretnine" : "Nekretninu" }}
                @if($data['includes_movables'])
                    i Pokretnine
                @endif
                u stanju u kakvom se
                {{ (!empty($data['additional_realestates']) || $data['includes_movables']) ? "nalaze" : "nalazi" }}
                po načelu viđeno-kupljeno,
                što znači da se
                {{ !empty($data['additional_buyers']) ? "Kupci odriču" : "Kupac odriče" }}
                prava na isticanje bilo kakvih prigovora
                {{ !empty($data['additional_sellers']) ? "Prodavateljima" : "Prodavatelju" }}
                zbog eventualnih materijalnih nedostataka na
                {{ !empty($data['additional_realestates']) ? "Nekretninama" : "Nekretnini" }}
                @if($data['includes_movables'])
                    i/ili Pokretninama
                @endif
                te da
                {{ !empty($data['additional_sellers']) ? "Prodavatelji ne odgovaraju" : "Prodavatelj ne odgovara" }}
                {{ !empty($data['additional_buyers']) ? "Kupcima" : "Kupcu" }}
                za eventualne materijalne nedostatke na
                @if($data['includes_movables'])
                    {{ !empty($data['additional_realestates']) ? "Nekretninama" : "Nekretnini" }} i/ili Pokretninama.
                @else
                    {{ !empty($data['additional_realestates']) ? "Nekretninama" : "Nekretnini" }}.
                @endif
            </p>
        @endif

        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            {{ !empty($data['additional_sellers']) ? "Prodavatelji izjavljuju i jamče" : "Prodavatelj izjavljuje i jamči" }}
            {{ !empty($data['additional_buyers']) ? "Kupcima" : "Kupcu" }}
            da na
            {{ !empty($data['additional_realestates']) ? "Nekretninama" : "Nekretnini" }}
            ne postoji nikakvo pravo trećega koje isključuje, umanjuje ili ograničuje prava
            {{ !empty($data['additional_buyers']) ? "Kupaca" : "Kupca" }}
            čije postojanje nije izričito navedeno u ovom Predugovoru. Između ostalog,
            {{ !empty($data['additional_sellers']) ? "Prodavatelji izjavljuju i jamče" : "Prodavatelj izjavljuje i jamči" }}
            {{ !empty($data['additional_buyers']) ? "Kupcima" : "Kupcu" }}
            da
            {{ !empty($data['additional_realestates']) ? "su Nekretnine" : "je Nekretnina" }}
            {{ !empty($data['additional_sellers']) ? "njihovo" : "njegovo" }}
            isključivo vlasništvo te da nije opterećeno nikakvim zemljišnoknjižnim ili izvanknjižnim teretima koji nisu izričito navedeni u ovom Predugovoru. Isto tako,
            {{ !empty($data['additional_sellers']) ? "Prodavatelji izjavljuju i jamče" : "Prodavatelj izjavljuje i jamči" }}
            {{ !empty($data['additional_buyers']) ? "Kupcima" : "Kupcu" }}
            da
            {{ !empty($data['additional_realestates']) ? "Nekretnine nisu" : "Nekretnina nije" }}
            predmet spora u parničnom, ovršnom, upravnom i/ili drugom postupku na temelju kojih bi mogla biti ograničena prava
            {{ !empty($data['additional_buyers']) ? "Kupaca" : "Kupca" }}, a
            koji nisu izričito navedeni u ovom Predugovoru.
        </p>

        @if(!$data['any_seller_cosigner_exists'])
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                {{ !empty($data['additional_sellers']) ? "Prodavatelji izjavljuju i jamče" : "Prodavatelj izjavljuje i jamči" }}
                {{ !empty($data['additional_buyers']) ? "Kupcima" : "Kupcu" }}
                da
                {{ !empty($data['additional_realestates']) ? "Nekretnine nisu" : "Nekretnina nije" }}
                dio bračne, izvanbračne ili partnerske stečevine Prodavatelja.
            </p>
        @endif
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Tabularna izjava
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        @if(!$data['clausula_intabulandi'])
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ugovorne stranke suglasno utvrđuju da će se Glavnim ugovorom
                {{ !empty($data['additional_sellers']) ? "Prodavatelji" : "Prodavatelj" }}
                obvezati
                {{ !empty($data['additional_buyers']) ? "Kupcima" : "Kupcu" }}
                dati dopuštenje za uknjižbu prava vlasništva
                {{ !empty($data['additional_buyers']) ? "Kupaca" : "Kupca" }}
                na
                {{ !empty($data['additional_realestates']) ? "Nekretninama" : "Nekretnini" }}
                tj. izdati tabularnu izjavu (clausulu intabulandi) podobnu za uknjižbu prava vlasništva
                {{ !empty($data['additional_buyers']) ? "Kupaca" : "Kupca" }}
                na
                {{ !empty($data['additional_realestates']) ? "Nekretninama" : "Nekretnini" }}
                odmah po plaćanju Kupoprodajne cijene u cijelosti.
            </p>
        @elseif($data['clausula_intabulandi'] == 1)
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ugovorne stranke suglasno utvrđuju da će potpisom Glavnog ugovora
                {{ !empty($data['additional_sellers']) ? "Prodavatelji" : "Prodavatelj" }}
                ovlastiti
                {{ !empty($data['additional_buyers']) ? "Kupce" : "Kupca" }}
                da na temelju Glavnog ugovora, bez ikakvog daljnjeg pitanja ili odobrenja,
                {{ !empty($data['additional_buyers']) ? "ishode" : "ishodi" }}
                uknjižbu prava vlasništva na
                {{ !empty($data['additional_realestates']) ? "Nekretninama" : "Nekretnini" }}
                na svoje ime u zemljišnim knjigama i drugim javnim upisnicima u koje se upisuje pravo vlasništva ili posjedovno stanje.
            </p>
        @elseif($data['clausula_intabulandi'] == 2)
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ugovorne stranke suglasno utvrđuju da će se Glavnim ugovorom
                {{ !empty($data['additional_sellers']) ? "Prodavatelji" : "Prodavatelj" }}
                obvezati odmah po potpisu Glavnog ugovora izdati tabularnu izjavu (clausulu intabulandi) podobnu za uknjižbu prava vlasništva
                {{ !empty($data['additional_buyers']) ? "Kupaca" : "Kupca" }}
                na
                {{ !empty($data['additional_realestates']) ? "Nekretninama" : "Nekretnini" }}
                i deponirati ju kod odabranog javnog bilježnika ili odvjetnika s uputom da ju odabrani javni bilježnik ili odvjetnik preda
                {{ !empty($data['additional_buyers']) ? "Kupcima" : "Kupcu" }}
                odmah po primitku potvrde o plaćenoj Kupoprodajnoj cijeni u cijelosti.
            </p>
        @endif
    </div>
</div>

@if(!empty($data['custom_provisions']))
    @foreach($data['custom_provisions'] as $_i => $_custom_provision)
        @if(!empty(trim($_custom_provision['provision'])))
            <div class="editable-segment" data-type="article">
                <p class="article-header">
                    Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. {{ $_custom_provision['title'] }}
                </p>
                <div class="article-body">
                    @php
                        $p_index = 1;

                        $_custom_provision_content = App\Helpers\StringHelper::wordpressContent($_custom_provision['provision']);

                        foreach(explode("<p>", $_custom_provision_content) as $_p) {
                            if(!empty(trim($_p))){
                                echo "<p> ".$builder->getCurrentArticleIndex(). "." .$p_index++.". $_p";
                            }
                        }
                    @endphp
                </div>
            </div>
        @endif
    @endforeach
@endif

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Ništetnost
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ako se bilo koja odredba ovog Predugovora pokaže ništetnom, ostale odredbe ovog Predugovora u cijelosti ostaju na snazi. U slučaju ništetnosti jedne ili više odredaba ovog Predugovora, ugovorne stranke se obvezuju odmah pristupiti zamjeni ništetnih odredaba drugima, vodeći pri tome računa da se izmijenjenim odredbama postigne isti stupanj zadovoljenja interesa ugovornih stranaka, ali na način koji je dopušten.
        </p>
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Izmjene predugovora
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da bilo kakva izmjena, dopuna ili dodatak ovom Predugovoru moraju biti sastavljeni u pisanom obliku, valjano potpisani i odobreni od svih ugovornih stranaka, a eventualni usmeni dogovori moraju biti pisano potvrđeni.
        </p>
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Mjerodavno pravo
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da se na sva pitanja koja proizlaze iz ili u vezi s ovim Predugovorom koja nisu izrijekom uređena u njemu primjenjuju odgovarajući propisi na snazi u Republici Hrvatskoj.
        </p>
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Rješavanje sporova
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da će sve sporove koji nastanu iz ili u vezi s ovim Predugovorom koje ne mogu riješiti mirnim putem rješavati stvarno i mjesno nadležni sud.
        </p>
    </div>
</div>

<div class="editable-segment" data-section="final_provisions" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Broj primjeraka predugovora
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ovaj Predugovor sklapa se u {!! $data["contract_copy_count"] !!} {{ StringHelper::getPrimjerakaString($data['contract_copy_count']) }}, od kojih
            {!! $data["seller_contract_copy_count"] !!} zadržava Prodavatelj<span></span>@if(empty($data['additional_sellers'])),@else
            {!! $data["seller_name"] !!},
                @foreach($data['additional_sellers'] as $_ad_i => $_additional_seller)
                    {!! !empty($data['additional_seller_contract_copy_count'][$_ad_i]) ? $data['additional_seller_contract_copy_count'][$_ad_i] : $builder::$placeholder !!}
                    zadržava Prodavatelj {!! $_additional_seller['name'] ?: $builder::$placeholder !!},
                @endforeach
                @endif
            @if(empty($data['additional_buyers']))
                a {!! $data["buyer_contract_copy_count"] !!} zadržava Kupac.
            @else
                {!! $data["buyer_contract_copy_count"] !!} zadržava Kupac {!! $data["buyer_name"] !!}@if(count($data['additional_buyers']) == 1), a @else, @endif
                @foreach($data['additional_buyers'] as $_ab_i => $_additional_buyer)
                    {!! !empty($data['additional_buyer_contract_copy_count'][$_ab_i]) ? $data['additional_buyer_contract_copy_count'][$_ab_i] : $builder::$placeholder !!}
                    zadržava Kupac {!! $_additional_buyer['name'] ?: $builder::$placeholder !!}@if($_ab_i == (count($data['additional_buyers'])-1)). @elseif($_ab_i == (count($data['additional_buyers'])-2)), a @else, @endif
                @endforeach
            @endif
        </p>
    </div>
</div>

@include('layouts.document.partials.signatures', [
  'data' => $data,
  'builder' => $builder,
  'dynamic_index' => $dynamic_index,
  'text' => 'Ugovorne stranke su suglasne da je u odredbama ovog Predugovora sadržana njihova prava i stvarna volja te ga u znak prihvata prava i obveza koje iz Predugovora proizlaze vlastoručno potpisuju.',
  'left_parties' => $builder->getParties()->where('side', 'left')->values()->toArray(),
  'right_parties' => $builder->getParties()->where('side', 'right')->values()->toArray(),
  'default_party_label_left' => 'Prodavatelj',
  'default_party_label_right' => 'Kupac',
])
