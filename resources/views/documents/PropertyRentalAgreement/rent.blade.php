@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                <div class="card-header">
                    Trajanje najma
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'rent_type', 'Ugovara li se najam na određeno ili neodređeno vrijeme?') }}
                            {{ Form::fRadio($model, 'rent_type', 'neodređeno vrijeme', 'Neodređeno vrijeme', ['id' => 'rent_indefinite', 'checked' => !isset($model->rent_indefinite)], 'Kod ugovaranja najma na neodređeno vrijeme najmodavac treba voditi računa o tome da može otkazati ili raskinuti ugovor isključivo ako se ispune pretpostavke navedene u članku 19.-22. Zakona o najmu stanova, dok najmoprimac može otkazati ugovor bez obrazloženja uz otkazni rok od 3 mjeseca.' ) }}
                            {{ Form::fRadio($model, 'rent_type', 'određeno vrijeme', 'Određeno vrijeme', ['id' => 'rent_definite'], 'Kod ugovaranja najma na određeno vrijeme treba voditi računa o tome da se, prema Zakonu o najmu stanova, ugovor o najmu smatra prešutno obnovljenim za isto vrijeme trajanja ako nijedna ugovorna strana najmanje 30 dana prije isteka ugovorenog vremena ne obavijesti u pisanom obliku drugu ugovornu stranu da ne namjerava sklopiti ugovor na određeno vrijeme za daljnje razdoblje.') }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fText($model, 'rent_start', null, '<span class="dot"></span> Upiši datum primopredaje stana tj. početka najma', ['data-datepicker' => 1, 'autocomplete' => 'off', 'placeholder' => 'Npr: 11. 10. '.date('Y').'.']) }}
                        </div>
                    </div>
                    <div class="row" id="rent_expiration_container"
                         style="{{ !isset($model->rent_type) ||  $model->rent_type == 'neodređeno vrijeme' ? "display:none" : ""}}">
                        <div class="form-group col-lg-12">
                            <hr/>
                            {{ Form::fText($model, 'rent_end', null, '<span class="dot"></span> Upiši datum isteka najma', ['data-datepicker' => 1, 'autocomplete' => 'off', 'placeholder' => 'Npr: 31. 12. '.date('Y').'.']) }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="card mb-4">
                <div class="card-header">
                    Najamnina
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'rent_payment_agreement', '1. Plaća li se najamnina unaprijed ili unatrag?') }}
                            {{ Form::fRadio($model, 'rent_payment_agreement', 'u tekućem mjesecu za tekući mjesec (unaprijed)', 'U tekućem mjesecu za tekući mjesec (unaprijed)', ['checked' => !isset($model->rent_payment_agreement)] ) }}
                            {{ Form::fRadio($model, 'rent_payment_agreement', 'u tekućem mjesecu za prethodni mjesec (unatrag)', 'U tekućem mjesecu za prethodni mjesec (unatrag)') }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fNumber($model, 'rent_payment_day', null, '2. Kojeg dana u mjesecu najamnina dospijeva na plaćanje?', ['placeholder' => 'Npr: 5']) }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'rent_proportional_decrease', '3. Primjenjuje li se razmjerno umanjenje najamnine?', 'Razmjerno umanjenje najamnine za vrijeme najma koje je kraće od mjesec dana može se koristiti, primjerice, ako najam ne počinje prvog dana u kalendarskom mjesecu, odnosno ne završava zadnjeg dana u kalendarskom mjesecu. U tom slučaju, iznos prve odnosno zadnje najamnine izračunava se pomoću formule „(iznos mjesečne najamnine/ukupan broj dana u kalendarskom mjesecu) * broj dana najma u tom mjesecu“') }}

                            {{ Form::fRadio($model, 'rent_proportional_decrease', 0, 'Ne', ['checked' => !isset($model->rent_proportional_decrease)] ) }}
                            {{ Form::fRadio($model, 'rent_proportional_decrease', 1, 'Da', []) }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fText($model, 'rent_amount', null, '4. Upiši bruto iznos mjesečne najamnine u eurima', ['placeholder' => 'Npr: 500,00', 'data-currency' => 'EUR'], '€') }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fText($model, 'rent_iban', null, '5. Upiši IBAN žiro računa na koji će se plaćati najamnina', ['placeholder' => 'Npr: *******************']) }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="card mb-4">
                <div class="card-header">
                    Troškovi u svezi sa stanovanjem
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'rent_includes_expenses', '1. Koji troškovi u svezi sa stanovanjem su uključeni u najamninu?') }}
                            {{ Form::fRadio($model, 'rent_includes_expenses', 0, 'U najamninu su uključeni svi troškovi za koje u pitanju 2. ispod nije izričito definirano da ih snosi najmoprimac', ['id' => 'rent_does_not_include_expenses', 'checked' => !isset($model->rent_includes_expenses)] ) }}
                            {{ Form::fRadio($model, 'rent_includes_expenses', 1, 'U najamninu su uključeni troškovi u svezi sa stanovanjem koji se odnose na:', ['id' => 'rent_includes_expenses']) }}
                        </div>
                    </div>
                    <div class="row">
                        <div id="fixed_rent_included_expenses_container" class="form-group col-lg-12 mb-1">
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'električnu energiju', 'električnu energiju') }}
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'plin', 'plin') }}
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'toplinsku energiju (toplana)', 'toplinsku energiju (toplana)') }}
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'vodne usluge i naknade', 'vodne usluge i naknade') }}
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'zajedničku pričuvu', 'zajedničku pričuvu') }}
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'prikupljanje komunalnog otpada', 'prikupljanje komunalnog otpada') }}
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'komunalnu naknadu', 'komunalnu naknadu') }}
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'naknadu za uređenje voda', 'naknadu za uređenje voda') }}
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'čišćenje zajedničkog stubišta i površina', 'čišćenje zajedničkog stubišta i površina') }}
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'RTV pristojbu', 'RTV pristojbu') }}
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'čišćenje dimnjaka', 'čišćenje dimnjaka') }}
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'spomeničku rentu', 'spomeničku rentu') }}
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'suzbijanje štetočina', 'suzbijanje štetočina') }}
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'očitanja - grijanje', 'očitanja - grijanje') }}
                            {{ Form::fCheckbox($model, 'rent_included_expenses[]', 'naknadu za gradnju građevina za gospodarenje komunalnim otpadom', 'naknadu za gradnju građevina za gospodarenje komunalnim otpadom') }}
                        </div>
                        <div id="custom_rent_included_expense_template" class="d-none">
                            <div class="custom_rent_included_expense_content">
                                <div class="pb-1">
                                    {{ Form::fCheckbox($model, 'custom_rent_included_expenses[{INDEX}][exists]', 1, Form::text("custom_rent_included_expenses[{INDEX}][name]", null, ['class' => 'checkbox_input_text form-control ', 'placeholder' => 'Npr: telefon']), ['class' => 'form-check-input toggle_custom_rent_included_expense', 'checked' => 'checked']) }}
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-lg-12" id="custom_rent_included_expenses_container">
                            @if(!empty($model->custom_rent_included_expenses))
                                @foreach($model->custom_rent_included_expenses as $_i => $_custom_expense)
                                    <div class="custom_rent_included_expense_content">
                                        <div class="pb-1">
                                            {{ Form::fCheckbox($model, "custom_rent_included_expenses[$_i][exists]", 1, Form::text("custom_rent_included_expenses[{$_i}][name]", null, ['class' => 'checkbox_input_text form-control', 'placeholder' => 'Npr: telefon']), ['class' => 'form-check-input toggle_custom_rent_included_expense', 'checked' => 'checked']) }}
                                        </div>
                                    </div>
                                @endforeach
                            @endif
                        </div>
                        <div class="form-group col-lg-12">
                            <a class="btn btn-info" id="add_custom_rent_included_expense">+ Dodaj
                                trošak</a>
                        </div>
                    </div>

                    <hr/>

                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'tenant_pays_expenses', '2. Koje troškove u svezi sa stanovanjem snosi najmoprimac?') }}
                            <div id="tenant_does_not_pay_expenses_container" style="@if(empty($model->rent_includes_expenses)) display:none; @endif">
                                {{ Form::fRadio($model, 'tenant_pays_expenses', 0, 'Najmoprimac ne snosi nikakve troškove u svezi sa stanovanjem', ['id' => 'tenant_does_not_pay_expenses'] ) }}
                            </div>
                            {{ Form::fRadio($model, 'tenant_pays_expenses', 1, 'Najmoprimac snosi troškove u svezi sa stanovanjem koji se odnose na:', ['id' => 'tenant_pays_expenses', 'checked' => !isset($model->tenant_pays_expenses)]) }}
                        </div>
                    </div>
                    <div class="row">
                        <div id="fixed_tenant_expenses_container" class="form-group col-lg-12 mb-1">
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'električnu energiju', 'električnu energiju') }}
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'plin', 'plin') }}
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'toplinsku energiju (toplana)', 'toplinsku energiju (toplana)') }}
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'vodne usluge i naknade', 'vodne usluge i naknade') }}
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'zajedničku pričuvu', 'zajedničku pričuvu') }}
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'prikupljanje komunalnog otpada', 'prikupljanje komunalnog otpada') }}
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'komunalnu naknadu', 'komunalnu naknadu') }}
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'naknadu za uređenje voda', 'naknadu za uređenje voda') }}
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'čišćenje zajedničkog stubišta i površina', 'čišćenje zajedničkog stubišta i površina') }}
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'RTV pristojbu', 'RTV pristojbu') }}
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'čišćenje dimnjaka', 'čišćenje dimnjaka') }}
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'spomeničku rentu', 'spomeničku rentu') }}
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'suzbijanje štetočina', 'suzbijanje štetočina') }}
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'očitanja - grijanje', 'očitanja - grijanje') }}
                            {{ Form::fCheckbox($model, 'tenant_expenses[]', 'naknadu za gradnju građevina za gospodarenje komunalnim otpadom', 'naknadu za gradnju građevina za gospodarenje komunalnim otpadom') }}
                        </div>
                        <div id="custom_tenant_expense_template" class="d-none">
                            <div class="custom_tenant_expense_content">
                                <div class="pb-1">
                                    {{ Form::fCheckbox($model, 'custom_tenant_expenses[{INDEX}][exists]', 1, Form::text("custom_tenant_expenses[{INDEX}][name]", null, ['class' => 'checkbox_input_text form-control ', 'placeholder' => 'Npr: telefon']), ['class' => 'form-check-input toggle_custom_tenant_expense', 'checked' => 'checked']) }}
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-lg-12" id="custom_tenant_expenses_container">
                            @if(!empty($model->custom_tenant_expenses))
                                @foreach($model->custom_tenant_expenses as $_i => $_custom_expense)
                                    <div class="custom_tenant_expense_content">
                                        <div class="pb-1">
                                            {{ Form::fCheckbox($model, "custom_tenant_expenses[$_i][exists]", 1, Form::text("custom_tenant_expenses[{$_i}][name]", null, ['class' => 'checkbox_input_text form-control ', 'placeholder' => 'Npr: telefon']), ['class' => 'form-check-input toggle_custom_tenant_expense', 'checked' => 'checked']) }}
                                        </div>
                                    </div>
                                @endforeach
                            @endif
                        </div>
                        <div class="form-group col-lg-12">
                            <a class="btn btn-info" id="add_custom_tenant_expense">+ Dodaj
                                trošak</a>
                        </div>
                    </div>
                    <div id="tenant_pays_expenses_container" style="@if(isset($model->tenant_pays_expenses) && $model->tenant_pays_expenses == 0) display:none; @endif">
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'tenant_expenses_payment_type', '<span class="dot"></span> Na koji način najmoprimac podmiruje troškove u svezi sa stanovanjem?') }}
                                {{ Form::fRadio($model, 'tenant_expenses_payment_type', 'directly', 'Najmoprimac plaća račune za troškove u ime najmodavca izravno pružateljima usluga, a najmodavcu dostavlja dokaze o podmirenju računa', ['id' => 'tenant_expenses_payment_type_directly', 'checked' => !isset($model->tenant_expenses_payment_type)] ) }}
                                {{ Form::fRadio($model, 'tenant_expenses_payment_type', 'to_landlord', 'Najmodavac sam plaća račune za troškove pružateljima usluga, a najmoprimac podmiruje iznos troškova najmodavcu nakon uvida u račune', ['id' => 'tenant_expenses_payment_type_to_landlord']) }}
                            </div>
                        </div>
                        <div id="tenant_expenses_payment_type_directly_container" style="@if(isset($model->tenant_expenses_payment_type) && $model->tenant_expenses_payment_type == 'to_landlord') display:none; @endif">
                            <hr/>
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{ Form::fText($model, 'tenant_to_landlord_expenses_paid_confirmation_deadline', null, '<span class="dot"></span> U kojem roku najmoprimac mora najmodavcu dostaviti dokaze da je podmirio račune za troškove?', ['placeholder'=> 'Npr: do kraja mjeseca u kojem računi dospijevaju na plaćanje', 'data-force-start-case' => 'lower']) }}
                                </div>
                            </div>
                            <hr/>
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{ Form::fText($model, 'tenant_to_landlord_expenses_paid_confirmation_channel', null, '<span class="dot"></span> Na koji način najmoprimac dostavlja dokaze o podmirenju računa najmodavcu na uvid?', ['placeholder' => 'Npr: slanjem na adresu e-pošte <EMAIL>', 'data-force-start-case' => 'lower']) }}
                                </div>
                            </div>
                        </div>
                        <div id="tenant_expenses_payment_type_to_landlord_container" style="@if(!isset($model->tenant_expenses_payment_type) || $model->tenant_expenses_payment_type == 'directly') display:none; @endif">
                            <hr/>
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{ Form::fText($model, 'landlord_to_tenant_expenses_delivery_channel', null, '<span class="dot"></span> Na koji način najmodavac dostavlja račune za troškove u svezi sa stanovanjem najmoprimcu na uvid?', ['placeholder'=> 'Npr: slanjem na adresu e-pošte <EMAIL>', 'data-force-start-case' => 'lower']) }}
                                </div>
                            </div>
                            <hr/>
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{ Form::fText($model, 'tenant_expenses_paid_deadline', null, '<span class="dot"></span> U kojem roku najmoprimac mora podmiriti iznos troškova najmodavcu?', ['placeholder' => 'Npr: u roku od 8 dana od primitka računa na uvid', 'data-force-start-case' => 'lower']) }}
                                </div>
                            </div>
                            <hr/>
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{ Form::fLabel($model, 'tenant_to_landlord_expenses_payment_type', '<span class="dot"></span> Na koji način najmoprimac podmiruje iznos troškova najmodavcu?') }}
                                    {{ Form::fRadio($model, 'tenant_to_landlord_expenses_payment_type', 'cash', 'U gotovini') }}

                                    <div class="radio-input">
                                        {{ Form::fRadio($model, 'tenant_to_landlord_expenses_payment_type', 'landlord_iban', 'Na tekući račun najmodavca IBAN', ['checked' => !isset($model->tenant_to_landlord_expenses_payment_type)]) }}
                                        {{ Form::fText($model, 'tenant_to_landlord_expenses_payment_type_iban', null, null, ['placeholder' => 'Npr: *********************']) }}
                                    </div>
                                    <div class="radio-input">
                                        {{ Form::fRadio($model, 'tenant_to_landlord_expenses_payment_type', 'custom', 'Na drugi način') }}
                                        {{ Form::fText($model, 'tenant_to_landlord_expenses_payment_type_custom', null, null, ['placeholder' => 'Npr: preko PayPal-a', 'data-force-start-case' => 'lower']) }}
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>


                </div>
            </div>
            <div class="card mb-4">
                <div class="card-header">
                    Polog
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'deposit_exists', 'Daje li najmoprimac polog kao osiguranje za neplaćanje najamnine i eventualnu štetu na
                                stanu?') }}
                            {{ Form::fRadio($model, 'deposit_exists', 0, 'Ne', ['id' => 'deposit_does_not_exist', 'checked' => !isset($model->deposit_exists)] ) }}
                            {{ Form::fRadio($model, 'deposit_exists', 1, 'Da', ['id' => 'deposit_exists']) }}
                        </div>
                    </div>
                    <div id="deposit_container"
                         style="{{ !isset($model->deposit_exists) ||  !$model->deposit_exists ? "display:none" : ""}}">
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{  Form::fText($model, 'deposit_amount', null, '<span class="dot"></span> Upiši iznos pologa u eurima', ['placeholder' => 'Npr: 1.000,00', 'data-currency' => 'eur'], "€")  }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fLabel($model, 'deposit_payment_type', '<span class="dot"></span> Na koji način se polog uplaćuje najmodavcu?') }}
                                {{ Form::fRadio($model, 'deposit_payment_type', 'u gotovini', 'U gotovini', ['checked' => !isset($model->deposit_payment_type)] ) }}
                                {{ Form::fRadio($model, 'deposit_payment_type', 'uplatom na tekući račun Najmodavca', 'Uplatom na tekući račun najmodavca', ['id' => 'deposit_payment_type_iban']) }}
                                <div class="form-check radio-input">
                                    {{ Form::radio('deposit_payment_type', 'custom', null, ['class' => 'form-check-input']) }}
                                    <label style="width: 100%;" class="form-check-label" for="deposit_payment_type">
                                        {{ Form::fText($model, "custom_deposit_payment_type", null, null, ['class' => 'radio_input_text form-control', 'placeholder' => 'Npr: u roku od 5 dana od potpisivanja Ugovora...', 'data-force-start-case' => 'lower']) }}
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row" id="deposit_iban_container"
                             style="{{ !isset($model->deposit_payment_type) ||  $model->deposit_payment_type != 'uplatom na tekući račun Najmodavca' ? "display:none" : ""}}">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{  Form::fText($model, 'deposit_iban', null, '<span class="dot"></span> Upiši IBAN tekućeg računa najmodavca za uplatu pologa', ['placeholder' => 'Npr: ********************'])  }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fLabel($model, 'deposit_payment_time', '<span class="dot"></span> Kada se polog uplaćuje?') }}
                                {{ Form::fRadio($model, 'deposit_payment_time', 'na dan potpisivanja ugovora', 'Na dan potpisivanja ugovora', ['checked' => !isset($model->deposit_payment_time)]) }}
                                {{ Form::fRadio($model, 'deposit_payment_time', 'na dan primopredaje Stana', 'Na dan primopredaje stana') }}
                                <div class="form-check radio-input">
                                    {{ Form::radio('deposit_payment_time', 'custom', null, ['class' => 'form-check-input']) }}
                                    <label style="width: 100%;" class="form-check-label" for="deposit_payment_time">
                                        {{ Form::fText($model, "custom_deposit_payment_time", null, null, ['class' => 'radio_input_text form-control', 'placeholder' => 'Npr: u roku od 5 dana od potpisivanja Ugovora...', 'data-force-start-case' => 'lower']) }}
                                    </label>
                                </div>                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fLabel($model, 'deposit_return_deadline', '<span class="dot"></span> Upiši rok u kojem će najmodavac vratiti najmoprimcu polog nakon prestanka ugovora o najmu') }}
                                {{ Form::fRadio($model, 'deposit_return_deadline', 'na dan prestanka Ugovora o najmu', 'Na dan prestanka ugovora o najmu', ['checked' => !isset($model->deposit_return_deadline)]) }}
                                <div class="form-check radio-input">
                                    {{ Form::radio('deposit_return_deadline', 'custom', null, ['class' => 'form-check-input']) }}
                                    <label style="width: 100%;" class="form-check-label" for="deposit_return_deadline">
                                        {{ Form::fText($model, "custom_deposit_return_deadline", null, null, ['class' => 'radio_input_text form-control', 'placeholder' => 'Npr: 5 dana', 'data-force-start-case' => 'lower']) }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>

    </div>

    {{ Form::close() }}
@endsection
