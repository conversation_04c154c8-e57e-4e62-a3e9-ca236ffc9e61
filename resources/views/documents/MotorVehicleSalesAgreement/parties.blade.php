@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                <div class="card-header">
                    Upiši podatke o prodavatelju
                </div>
                <div class="card-body" id="seller_content">
                    <div class="maps_autofill_container">
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'seller_name', null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: <PERSON><PERSON>']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'seller_address', null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'seller_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'seller_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'seller_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'seller_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                            </div>
                        </div>

                    </div>

                    <div class="authorized_seller_persons">

                        <div class="row">
                            <div class="form-group col-lg-12 pt-2">
                                {{ Form::fCheckbox($model, '', 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog prodavatelja', ['class' => 'authorized_seller_persons_exist form-check-input', 'checked' => !empty($model->authorized_seller_persons)], 'Ovu opciju označi ako je prodavatelj pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun prodavatelja u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je prodavatelj fizička osoba (obrt, samostalna djelatnost, itd.), ali je prodavatelj ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                            </div>
                        </div>

                        <div class="authorized_seller_persons_container dynamic"
                             style="@if(empty($model->authorized_seller_persons)) display:none; @endif">

                            @if(!empty($model->authorized_seller_persons))
                                @foreach($model->authorized_seller_persons as $i => $_authorized_person)
                                    <div class="authorized_seller_person_content maps_autofill_container">
                                        <hr/>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <strong><a class="btn btn-danger btn-sm float-right remove_authorized_seller_person"><i
                                                                class="fa fa-trash"></i> Ukloni</a></strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_seller_persons[$i][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'],  null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa prodavatelja') }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_seller_persons[$i][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_seller_persons[$i][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_seller_persons[$i][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_seller_persons[$i][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_seller_persons[$i][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "authorized_seller_persons[$i][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @endif

                        </div>

                        <div class="row add_authorized_seller_person_container"
                             style="@if(empty($model->authorized_seller_persons)) display:none; @endif">
                            <div class="form-group col-lg-12">
                                <a data-id="authorized_seller_persons" class="btn btn-info add_authorized_seller_person">+ Dodaj
                                    zastupnika</a>
                            </div>
                        </div>

                        <hr class="authorized_seller_persons_separator" style="@if(empty($model->authorized_seller_persons)) display:none; @endif"/>

                    </div>

                    <div class="row">
                        <div class="form-group col-lg-12 pt-2">
                            {{ Form::fCheckbox($model, 'seller_cosigner_exists', 1, 'Postoji bračni ili izvanbračni drug ili životni partner koji na sklapanje ugovora mora dati suglasnost jer se prodaje dio bračne, izvanbračne ili partnerske stečevine', ['class' => 'form-check-input seller_cosigner_exists'], 'Prema Obiteljskom zakonu za izvanredne poslove na nekretninama ili pokretninama koje se upisuju u javne upisnike poput otuđenja cijele stvari potrebno je zajedničko poduzimanje posla ili pisana suglasnost drugoga bračnog druga s ovjerom potpisa kod javnog bilježnika. Navedeno se primjenjuje i u slučaju partnerske stečevine uređene Zakonom o životnom partnerstvu osoba istog spola, s obzirom na to da je njime propisano da se na imovinske odnose životnih partnera koji nisu obuhvaćeni Zakonom o životnom partnerstvu osoba istog spola primjenjuju odredbe Obiteljskog zakona.') }}
                        </div>
                    </div>

                    <div class="seller_cosigner_container"
                         style="{{ !isset($model->seller_cosigner_exists) || (!$model->seller_cosigner_exists) ? 'display:none;' : '' }}">
                        <hr/>
                        <div class="maps_autofill_container">
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{ Form::fDropdown($model, 'seller_cosigner_type', null, [0 => 'Bračni drug', 1 => 'Izvanbračni drug', 2 => 'Životni partner'], 'Radi li se o bračnom ili izvanbračnom drugu ili životnom partneru?') }}
                                </div>
                            </div>
                            <hr/>
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'seller_cosigner_name', null, 'Ime i prezime', ['placeholder' => 'Npr: Ana Zorić']) }}
                                </div>
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'seller_cosigner_address', null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'seller_cosigner_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                </div>
                                <div class="form-group col-lg-6">
                                    {{ Form::fNumber($model, 'seller_cosigner_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'seller_cosigner_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                </div>
                                <div class="form-group col-lg-6">
                                    {{ Form::fNumber($model, 'seller_cosigner_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <div id="additional_sellers_container"
                 class="dynamic">
                <div id="additional_sellers">
                    @if(!empty($model->additional_sellers))
                        @foreach($model->additional_sellers as $i => $_additional_seller)

                            <div class="card mb-4 additional_seller_content">
                                <div class="card-header">
                                    Dodani prodavatelj
                                    <strong><a class="btn btn-danger btn-sm float-right remove_additional_seller"><i
                                                    class="fa fa-trash"></i> Ukloni</a></strong>
                                </div>
                                <div class="card-body">
                                    <div class="maps_autofill_container">
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_sellers[$i][name]", null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: Ante Antić']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_sellers[$i][address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_sellers[$i][city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_sellers[$i][postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_sellers[$i][country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_sellers[$i][oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="authorized_seller_persons">

                                        <div class="row">
                                            <div class="form-group col-lg-12 pt-2">
                                                {{ Form::fCheckbox($model, "", 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog prodavatelja', ['class' => 'authorized_seller_persons_exist form-check-input', 'checked' => !empty($model->authorized_additional_seller_persons[$i])], 'Ovu opciju označi ako je prodavatelj pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun prodavatelja u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je prodavatelj fizička osoba (obrt, samostalna djelatnost, itd.), ali je prodavatelj ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                                            </div>
                                        </div>

                                        <div class="authorized_seller_persons_container dynamic"
                                             style="@if(empty($model->authorized_additional_seller_persons[$i])) display:none; @endif">

                                            @if(!empty($model->authorized_additional_seller_persons[$i]))
                                                @foreach($model->authorized_additional_seller_persons[$i] as $j => $_authorized_additional_seller_person)
                                                    <div class="authorized_seller_person_content maps_autofill_container">
                                                        <hr/>
                                                        <div class="row">
                                                            <div class="col-lg-12">
                                                                <strong><a class="btn btn-danger btn-sm float-right remove_authorized_seller_person"><i
                                                                                class="fa fa-trash"></i> Ukloni</a></strong>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_seller_persons[$i][$j][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa prodavatelja') }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_seller_persons[$i][$j][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_seller_persons[$i][$j][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_seller_persons[$i][$j][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_seller_persons[$i][$j][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_seller_persons[$i][$j][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fNumber($model, "authorized_additional_seller_persons[$i][$j][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @endif

                                        </div>

                                        <div class="row add_authorized_seller_person_container"
                                             style="@if(empty($model->authorized_additional_seller_persons[$i])) display:none; @endif">
                                            <div class="form-group col-lg-12">
                                                <a data-id="authorized_additional_seller_persons[{{$i}}]"
                                                   class="btn btn-info add_authorized_seller_person">+ Dodaj
                                                    zastupnika</a>
                                            </div>
                                        </div>

                                        <hr class="authorized_seller_persons_separator" style="@if(empty($model->authorized_additional_seller_persons[$i])) display:none; @endif"/>

                                    </div>

                                    <div class="row">
                                        <div class="form-group col-lg-12 pt-2">
                                            {{ Form::fCheckbox($model, "additional_sellers[$i][seller_cosigner_exists]", 1, 'Postoji bračni ili izvanbračni drug ili životni partner koji na sklapanje ugovora mora dati suglasnost jer se prodaje dio bračne, izvanbračne ili partnerske stečevine', ['class' => 'form-check-input seller_cosigner_exists', 'checked' => !empty($model->additional_sellers[$i]['seller_cosigner_exists'])], 'Prema Obiteljskom zakonu za izvanredne poslove na nekretninama ili pokretninama koje se upisuju u javne upisnike poput otuđenja cijele stvari potrebno je zajedničko poduzimanje posla ili pisana suglasnost drugoga bračnog druga s ovjerom potpisa kod javnog bilježnika. Navedeno se primjenjuje i u slučaju partnerske stečevine uređene Zakonom o životnom partnerstvu osoba istog spola, s obzirom na to da je njime propisano da se na imovinske odnose životnih partnera koji nisu obuhvaćeni Zakonom o životnom partnerstvu osoba istog spola primjenjuju odredbe Obiteljskog zakona.') }}
                                        </div>
                                    </div>

                                    <div class="seller_cosigner_container"
                                         style="{{ !isset($model->additional_sellers[$i]["seller_cosigner_exists"]) || (!$model->additional_sellers[$i]["seller_cosigner_exists"]) ? 'display:none;' : '' }}">
                                        <hr/>
                                        <div class="maps_autofill_container">
                                            <div class="row">
                                                <div class="form-group col-lg-12">
                                                    {{ Form::fDropdown($model, "additional_sellers[$i][seller_cosigner_type]", null, [0 => 'Bračni drug', 1 => 'Izvanbračni drug', 2 => 'Životni partner'], 'Radi li se o bračnom ili izvanbračnom drugu ili životnom partneru?') }}
                                                </div>
                                            </div>
                                            <hr/>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "additional_sellers[$i][seller_cosigner_name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ana Zorić']) }}
                                                </div>
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "additional_sellers[$i][seller_cosigner_address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "additional_sellers[$i][seller_cosigner_city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                                </div>
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fNumber($model, "additional_sellers[$i][seller_cosigner_postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "additional_sellers[$i][seller_cosigner_country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                                </div>
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fNumber($model, "additional_sellers[$i][seller_cosigner_oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @endif
                    <div id="additional_seller_template" class="d-none">
                        <div class="card mb-4 additional_seller_content">
                            <div class="card-header">
                                Dodani prodavatelj
                                <strong><a class="btn btn-danger btn-sm float-right remove_additional_seller"><i
                                                class="fa fa-trash"></i> Ukloni</a></strong>
                            </div>
                            <div class="card-body">
                                <div class="maps_autofill_container">
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_sellers[{INDEX}][name]", null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: Ante Antić']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_sellers[{INDEX}][address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_sellers[{INDEX}][city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fNumber($model, "additional_sellers[{INDEX}][postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_sellers[{INDEX}][country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fNumber($model, "additional_sellers[{INDEX}][oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                        </div>
                                    </div>
                                </div>

                                <div class="authorized_seller_persons">

                                    <div class="row">
                                        <div class="form-group col-lg-12 pt-2">
                                            {{ Form::fCheckbox($model, '', 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog prodavatelja', ['class' => 'authorized_seller_persons_exist form-check-input'], 'Ovu opciju označi ako je prodavatelj pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun prodavatelja u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je prodavatelj fizička osoba (obrt, samostalna djelatnost, itd.), ali je prodavatelj ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                                        </div>
                                    </div>

                                    <div class="authorized_seller_persons_container dynamic"
                                         style="display:none;">

                                    </div>

                                    <div class="row add_authorized_seller_person_container" style="display:none;">
                                        <div class="form-group col-lg-12">
                                            <a data-id="authorized_additional_seller_persons[{INDEX}]"
                                               class="btn btn-info add_authorized_seller_person">+ Dodaj
                                                zastupnika</a>
                                        </div>
                                    </div>

                                    <hr class="authorized_seller_persons_separator" style="display:none;"/>

                                </div>

                                <div class="row">
                                    <div class="form-group col-lg-12 pt-2">
                                        {{ Form::fCheckbox($model, "additional_sellers[{INDEX}][seller_cosigner_exists]", 1, 'Postoji bračni ili izvanbračni drug ili životni partner koji na sklapanje ugovora mora dati suglasnost jer se prodaje dio bračne, izvanbračne ili partnerske stečevine', ['class' => 'form-check-input seller_cosigner_exists'], 'Prema Obiteljskom zakonu za izvanredne poslove na nekretninama ili pokretninama koje se upisuju u javne upisnike poput otuđenja cijele stvari potrebno je zajedničko poduzimanje posla ili pisana suglasnost drugoga bračnog druga s ovjerom potpisa kod javnog bilježnika. Navedeno se primjenjuje i u slučaju partnerske stečevine uređene Zakonom o životnom partnerstvu osoba istog spola, s obzirom na to da je njime propisano da se na imovinske odnose životnih partnera koji nisu obuhvaćeni Zakonom o životnom partnerstvu osoba istog spola primjenjuju odredbe Obiteljskog zakona.') }}
                                    </div>
                                </div>

                                <div class="seller_cosigner_container"
                                     style="display:none;">
                                    <hr/>
                                    <div class="maps_autofill_container">
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fDropdown($model, "additional_sellers[{INDEX}][seller_cosigner_type]", null, [0 => 'Bračni drug', 1 => 'Izvanbračni drug', 2 => 'Životni partner'], 'Radi li se o bračnom ili izvanbračnom drugu ili životnom partneru?') }}
                                            </div>
                                        </div>
                                        <hr/>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_sellers[{INDEX}][seller_cosigner_name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ana Zorić']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_sellers[{INDEX}][seller_cosigner_address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_sellers[{INDEX}][seller_cosigner_city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_sellers[{INDEX}][seller_cosigner_postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_sellers[{INDEX}][seller_cosigner_country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_sellers[{INDEX}][seller_cosigner_oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-2">
                <div class="form-group col-lg-12">
                      <span data-toggle="tooltip"
                            data-original-title='Ako je vozilo u vlasništvu više osoba, pomoću opcije "Dodaj prodavatelja" upiši podatke o svim ostalim suvlasnicima vozila.'>

                          <a class="btn btn-info btn-block" id="add_additional_seller">+ Dodaj prodavatelja <i class="fa fa-info-circle px-lg-0 px-2"></i></a>
                      </span>
                </div>
            </div>

            <div id="authorized_seller_person_template" class="d-none">
                <div class="authorized_seller_person_content maps_autofill_container">
                    <hr/>
                    <div class="row">
                        <div class="col-lg-12">
                            <strong><a class="btn btn-danger btn-sm float-right remove_authorized_seller_person"><i
                                            class="fa fa-trash"></i> Ukloni</a></strong>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa prodavatelja') }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fNumber($model, "{ID}[{INDEX}][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    Upiši podatke o kupcu
                </div>
                <div class="card-body" id="buyer_content">
                    <div class="maps_autofill_container">

                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'buyer_name', null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: Stanko Zorić']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'buyer_address', null, 'Adresa',
                                ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'buyer_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'buyer_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'buyer_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'buyer_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                            </div>
                        </div>
                    </div>

                    <div class="authorized_buyer_persons">

                        <div class="row">
                            <div class="form-group col-lg-12 pt-2">
                                {{ Form::fCheckbox($model, '', 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog kupca', ['class' => 'authorized_buyer_persons_exist form-check-input', 'checked' => !empty($model->authorized_buyer_persons)], 'Ovu opciju označi ako je kupac pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun kupca u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je kupac fizička osoba (obrt, samostalna djelatnost, itd.), ali je kupac ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                            </div>
                        </div>

                        <div class="authorized_buyer_persons_container dynamic"
                             style="@if(empty($model->authorized_buyer_persons)) display:none; @endif">

                            @if(!empty($model->authorized_buyer_persons))
                                @foreach($model->authorized_buyer_persons as $i => $_authorized_person)
                                    <div class="authorized_buyer_person_content maps_autofill_container">
                                        <hr/>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <strong><a class="btn btn-danger btn-sm float-right remove_authorized_buyer_person"><i
                                                                class="fa fa-trash"></i> Ukloni</a></strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_buyer_persons[$i][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa kupca') }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_buyer_persons[$i][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_buyer_persons[$i][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_buyer_persons[$i][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_buyer_persons[$i][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_buyer_persons[$i][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "authorized_buyer_persons[$i][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @endif

                        </div>

                        <div class="row add_authorized_buyer_person_container"
                             style="@if(empty($model->authorized_buyer_persons)) display:none; @endif">
                            <div class="form-group col-lg-12">
                                <a data-id="authorized_buyer_persons" class="btn btn-info add_authorized_buyer_person">+ Dodaj
                                    zastupnika</a>
                            </div>
                        </div>

                    </div>

                </div>
            </div>

            <div id="additional_buyers_container"
                 class="dynamic">
                <div id="additional_buyers">
                    @if(!empty($model->additional_buyers))
                        @foreach($model->additional_buyers as $i => $_additional_buyer)

                            <div class="card mb-4 additional_buyer_content">
                                <div class="card-header">
                                    Dodani kupac
                                    <strong><a class="btn btn-danger btn-sm float-right remove_additional_buyer"><i
                                                    class="fa fa-trash"></i> Ukloni</a></strong>
                                </div>
                                <div class="card-body">
                                    <div class="maps_autofill_container">
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_buyers[$i][name]", null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: Ante Antić']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_buyers[$i][address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_buyers[$i][city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_buyers[$i][postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_buyers[$i][country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_buyers[$i][oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="authorized_buyer_persons">

                                        <div class="row">
                                            <div class="form-group col-lg-12 pt-2">
                                                {{ Form::fCheckbox($model, "", 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog kupca', ['class' => 'authorized_buyer_persons_exist form-check-input', 'checked' => !empty($model->authorized_additional_buyer_persons[$i])], 'Ovu opciju označi ako je kupac pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun kupca u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je kupac fizička osoba (obrt, samostalna djelatnost, itd.), ali je kupac ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                                            </div>
                                        </div>

                                        <div class="authorized_buyer_persons_container dynamic"
                                             style="@if(empty($model->authorized_additional_buyer_persons[$i])) display:none; @endif">

                                            @if(!empty($model->authorized_additional_buyer_persons[$i]))
                                                @foreach($model->authorized_additional_buyer_persons[$i] as $j => $_authorized_additional_buyer_person)
                                                    <div class="authorized_buyer_person_content maps_autofill_container">
                                                        <hr/>
                                                        <div class="row">
                                                            <div class="col-lg-12">
                                                                <strong><a class="btn btn-danger btn-sm float-right remove_authorized_buyer_person"><i
                                                                                class="fa fa-trash"></i> Ukloni</a></strong>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_buyer_persons[$i][$j][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa kupca') }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_buyer_persons[$i][$j][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_buyer_persons[$i][$j][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_buyer_persons[$i][$j][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_buyer_persons[$i][$j][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_buyer_persons[$i][$j][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fNumber($model, "authorized_additional_buyer_persons[$i][$j][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @endif

                                        </div>

                                        <div class="row add_authorized_buyer_person_container"
                                             style="@if(empty($model->authorized_additional_buyer_persons[$i])) display:none; @endif">
                                            <div class="form-group col-lg-12">
                                                <a data-id="authorized_additional_buyer_persons[{{$i}}]"
                                                   class="btn btn-info add_authorized_buyer_person">+ Dodaj
                                                    zastupnika</a>
                                            </div>
                                        </div>

                                    </div>

                                </div>
                            </div>
                        @endforeach
                    @endif
                    <div id="additional_buyer_template" class="d-none">
                        <div class="card mb-4 additional_buyer_content">

                            <div class="card-header">
                                Dodani kupac
                                <strong><a class="btn btn-danger btn-sm float-right remove_additional_buyer"><i
                                                class="fa fa-trash"></i> Ukloni</a></strong>
                            </div>
                            <div class="card-body">
                                <div class="maps_autofill_container">
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_buyers[{INDEX}][name]", null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: Ante Antić']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_buyers[{INDEX}][address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_buyers[{INDEX}][city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fNumber($model, "additional_buyers[{INDEX}][postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_buyers[{INDEX}][country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fNumber($model, "additional_buyers[{INDEX}][oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                        </div>
                                    </div>
                                </div>

                                <div class="authorized_buyer_persons">

                                    <div class="row">
                                        <div class="form-group col-lg-12 pt-2">
                                            {{ Form::fCheckbox($model, '', 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog kupca', ['class' => 'authorized_buyer_persons_exist form-check-input'], 'Ovu opciju označi ako je kupac pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun kupca u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je kupac fizička osoba (obrt, samostalna djelatnost, itd.), ali je kupac ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                                        </div>
                                    </div>

                                    <div class="authorized_buyer_persons_container dynamic"
                                         style="display:none;">

                                    </div>

                                    <div class="row add_authorized_buyer_person_container" style="display:none;">
                                        <div class="form-group col-lg-12">
                                            <a data-id="authorized_additional_buyer_persons[{INDEX}]"
                                               class="btn btn-info add_authorized_buyer_person">+ Dodaj
                                                zastupnika</a>
                                        </div>
                                    </div>

                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-group col-lg-12">

                                <span data-toggle="tooltip"
                                      data-original-title='Ako vlasništvo vozila stekne više osoba koji će biti suvlasnici, pomoću opcije "Dodaj kupca" upiši podatke o suvlasnicima vozila. U tom slučaju, prema Pravilniku o registraciji i označavanju vozila, vozilo se registrira na jednu od tih osoba sukladno pisanoj izjavi koju vlasnici daju stanici za tehnički pregled vozila, a u prometnu dozvolu u prostor za napomenu upisuje se riječ: »Suvlasništvo«.'>
                        <a class="btn btn-info btn-block" id="add_additional_buyer">+ Dodaj kupca  <i class="fa fa-info-circle px-lg-0 px-2"></i> </a>
                    </span>

                </div>
            </div>

            <div id="authorized_buyer_person_template" class="d-none">
                <div class="authorized_buyer_person_content maps_autofill_container">
                    <hr/>
                    <div class="row">
                        <div class="col-lg-12">
                            <strong><a class="btn btn-danger btn-sm float-right remove_authorized_buyer_person"><i
                                            class="fa fa-trash"></i> Ukloni</a></strong>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa kupca') }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fNumber($model, "{ID}[{INDEX}][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                        </div>
                    </div>
                </div>
            </div>


        </div>


    </div>

    {{ Form::close() }}
@endsection
