<div class="editable-segment" data-section="parties" data-type="parties">
    @if(!empty($data['additional_sellers']))
        <p>

            <strong>{!! $data['seller_name'] !!},</strong> {!! $data['seller_address'] !!}, {!! $data['seller_postal_code'] !!}
            {!! $data['seller_city'] !!}, {!! $data['seller_country'] !!}, OIB: {!! $data['seller_oib'] !!}@if(!empty($data['authorized_seller_persons']) || $data['seller_cosigner_exists']), kao prodavatelj,@elseif(count($data['additional_sellers'])==1), kao prodavatelj (u daljnjem tekstu: <strong>Prodavatelj {!! $data['seller_name'] !!}</strong>) <br>i @else, kao prodavatelj (u daljnjem tekstu: <strong>Prodavatelj {!! $data['seller_name'] !!}</strong>),@endif

            @if(!empty($data['authorized_seller_persons']))
                @if(count($data['authorized_seller_persons']) == 1)
                    kojeg zastupa
                @else
                    kojeg zastupaju
                @endif

                @foreach($data['authorized_seller_persons'] as $_i => $_authorized_person)
                        {!! $_authorized_person['role'] !!} {!! $_authorized_person['name']  !!}<span></span>@if($_authorized_person['has_entered_address']),
                        {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder !!},
                        {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                        {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder !!}@endif<span></span>@if(!empty($_authorized_person['oib'])), OIB: {!! $_authorized_person['oib'] !!}@endif<span></span>@if($_i < (count($data['authorized_seller_persons'])-1))@if(($_i+2) == count($data['authorized_seller_persons'])) i @else, @endif @elseif($data['seller_cosigner_exists']), @elseif(count($data['additional_sellers']) > 1 ) (u daljnjem tekstu: <strong>Prodavatelj {!! $data['seller_name'] !!}</strong>),@else() (u daljnjem tekstu: <strong>Prodavatelj {!! $data['seller_name'] !!}</strong>) <br>i @endif
                @endforeach

            @endif

            @if($data['seller_cosigner_exists'])
                uz suglasnost koju za sklapanje ovog Ugovora daje {!! mb_strtolower($data['seller_cosigner_type'], 'UTF-8') !!}
                {!! $data["seller_cosigner_name"] !!}, {!! $data["seller_cosigner_address"] !!}, {!! $data["seller_cosigner_postal_code"] !!} {!! $data["seller_cosigner_city"] !!}, {!! $data["seller_cosigner_country"] !!}, OIB: {!! $data["seller_cosigner_oib"] !!}@if(count($data['additional_sellers'])>1) (u daljnjem tekstu: <strong>Prodavatelj {!! $data['seller_name'] !!}</strong>), @else() (u daljnjem tekstu: <strong>Prodavatelj {!! $data['seller_name'] !!}</strong>) <br>i @endif
            @endif

            @foreach($data['additional_sellers'] as $_i => $additional_seller)
                <br>
                {!! !empty($additional_seller['name']) ? "<strong>".$additional_seller['name']."</strong>" : $builder::$placeholder  !!},
                {!! !empty($additional_seller['address']) ? $additional_seller['address'] : $builder::$placeholder !!},
                {!! !empty($additional_seller['postal_code']) ? $additional_seller['postal_code'] : $builder::$placeholder !!}
                {!! !empty($additional_seller['city']) ? $additional_seller['city'] : $builder::$placeholder !!},
                {!! !empty($additional_seller['country']) ? $additional_seller['country'] : $builder::$placeholder !!}, OIB:
                {!! !empty($additional_seller['oib']) ? $additional_seller['oib'] : $builder::$placeholder !!}@if(!empty($data['authorized_additional_seller_persons'][$_i]) || !empty($additional_seller['seller_cosigner_exists'])), kao prodavatelj,@elseif( (count($data['additional_sellers']) - $_i) > 2), kao prodavatelj (u daljnjem tekstu: <strong>Prodavatelj {!! !empty($additional_seller['name']) ? $additional_seller['name'] : $builder::$placeholder  !!}</strong>),@elseif($_i != count($data['additional_sellers'])-1), kao prodavatelj (u daljnjem tekstu: <strong>Prodavatelj {!! !empty($additional_seller['name']) ? $additional_seller['name'] : $builder::$placeholder  !!}</strong>) <br>i @else, kao prodavatelj (u daljnjem tekstu: <strong>Prodavatelj {!! !empty($additional_seller['name']) ? $additional_seller['name'] : $builder::$placeholder  !!}</strong>), @endif
                @if(!empty($data['authorized_additional_seller_persons'][$_i]))
                    @if(count($data['authorized_additional_seller_persons'][$_i]) == 1)
                        kojeg zastupa
                    @else
                        kojeg zastupaju
                    @endif

                    @foreach($data['authorized_additional_seller_persons'][$_i] as $_j => $_authorized_person)
                            {!! $_authorized_person['role'] ?: $builder::$placeholder !!} {!! $_authorized_person['name'] ?: $builder::$placeholder !!}<span></span>@if($_authorized_person['has_entered_address']),
                            {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder !!},
                            {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                            {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder !!}@endif<span></span>@if(!empty($_authorized_person['oib'])), OIB: {!! $_authorized_person['oib'] !!}@endif<span></span>@if($_j < (count($data['authorized_additional_seller_persons'][$_i])-1))@if(($_j+2) == count($data['authorized_additional_seller_persons'][$_i])) i @else, @endif @elseif(isset($data['additional_sellers'][$_i+1]))@if(count($data['additional_sellers']) == $_i+2)@if(empty($additional_seller['seller_cosigner_exists'])) (u daljnjem tekstu: <strong>Prodavatelj {!! !empty($additional_seller['name']) ? $additional_seller['name'] : $builder::$placeholder  !!}</strong>) <br> i @else, @endif @endif	@elseif(empty($additional_seller['seller_cosigner_exists'])) (u daljnjem tekstu: <strong>Prodavatelj {!! !empty($additional_seller['name']) ? $additional_seller['name'] : $builder::$placeholder  !!}</strong>), @else, @endif
                    @endforeach
                @endif

                @if(!empty($additional_seller['seller_cosigner_exists']))
                    uz suglasnost koju za sklapanje ovog Ugovora daje {!! mb_strtolower($additional_seller['seller_cosigner_type'], 'UTF-8') !!}
                    {!! !empty($additional_seller['seller_cosigner_name']) ? $additional_seller['seller_cosigner_name'] : $builder::$placeholder !!}, {!! !empty($additional_seller['seller_cosigner_address']) ? $additional_seller['seller_cosigner_address'] : $builder::$placeholder !!}, {!! !empty($additional_seller['seller_cosigner_postal_code']) ? $additional_seller['seller_cosigner_postal_code'] : $builder::$placeholder !!} {!! !empty($additional_seller['seller_cosigner_city']) ? $additional_seller['seller_cosigner_city'] : $builder::$placeholder !!}, {!! !empty($additional_seller['seller_cosigner_country']) ? $additional_seller['seller_cosigner_country'] : $builder::$placeholder !!}, OIB: {!! !empty($additional_seller['seller_cosigner_oib']) ? $additional_seller['seller_cosigner_oib'] : $builder::$placeholder !!}@if($_i == (count($data['additional_sellers'])-2)) (u daljnjem tekstu: <strong>Prodavatelj {!! !empty($additional_seller['name']) ? $additional_seller['name'] : $builder::$placeholder  !!}</strong>) <br>i  @else() (u daljnjem tekstu: <strong>Prodavatelj {!! !empty($additional_seller['name']) ? $additional_seller['name'] : $builder::$placeholder  !!}</strong>), @endif
                @endif

            @endforeach

            <br><br>(svi prodavatelji u daljnjem tekstu zajednički:  <strong>Prodavatelji</strong>)
        </p>
    @else
        <p>

            <strong>{!! $data['seller_name'] !!},</strong> {!! $data['seller_address'] !!}, {!! $data['seller_postal_code'] !!}
            {!! $data['seller_city'] !!}, {!! $data['seller_country'] !!}, OIB: {!! $data['seller_oib'] !!}@if(!empty($data['authorized_seller_persons']) || $data['seller_cosigner_exists']), kao prodavatelj,@else, kao prodavatelj <br/><br/>(u daljnjem tekstu: <strong>Prodavatelj</strong>)@endif

            @if(!empty($data['authorized_seller_persons']))
                @if(count($data['authorized_seller_persons']) == 1)
                    kojeg zastupa
                @else
                    kojeg zastupaju
                @endif

                @foreach($data['authorized_seller_persons'] as $_i => $_authorized_person)
                        {!! $_authorized_person['role'] !!} {!! $_authorized_person['name']  !!}<span></span>@if($_authorized_person['has_entered_address']),
                        {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder !!},
                        {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                        {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder !!}@endif<span></span>@if(!empty($_authorized_person['oib'])), OIB: {!! $_authorized_person['oib'] !!}@endif<span></span>@if($_i < (count($data['authorized_seller_persons'])-1))@if(($_i+2) == count($data['authorized_seller_persons'])) i @else, @endif @elseif($data['seller_cosigner_exists']), @else(), <br/><br/> (u daljnjem tekstu: <strong>Prodavatelj</strong>) @endif
                @endforeach

            @endif

            @if($data['seller_cosigner_exists'])
                uz suglasnost koju za sklapanje ovog Ugovora daje {!! mb_strtolower($data['seller_cosigner_type'], 'UTF-8') !!}
                {!! $data["seller_cosigner_name"] !!}, {!! $data["seller_cosigner_address"] !!}, {!! $data["seller_cosigner_postal_code"] !!} {!! $data["seller_cosigner_city"] !!}, {!! $data["seller_cosigner_country"] !!}, OIB: {!! $data["seller_cosigner_oib"] !!}, <br/><br/> (u daljnjem tekstu: <strong>Prodavatelj</strong>)
            @endif


        </p>
    @endif


    <p>i</p>

    @if(!empty($data['additional_buyers']))
        <p>

            <strong>{!! $data['buyer_name'] !!},</strong> {!! $data['buyer_address'] !!}, {!! $data['buyer_postal_code'] !!}
            {!! $data['buyer_city'] !!}, {!! $data['buyer_country'] !!}, OIB: {!! $data['buyer_oib'] !!}@if(!empty($data['authorized_buyer_persons'])), @elseif(count($data['additional_buyers'])==1)<br>i @else,@endif

            @if(!empty($data['authorized_buyer_persons']))
                @if(count($data['authorized_buyer_persons']) == 1)
                    kojeg zastupa
                @else
                    kojeg zastupaju
                @endif

                @foreach($data['authorized_buyer_persons'] as $_i => $_authorized_person)
                        {!! $_authorized_person['role'] !!} {!! $_authorized_person['name']  !!}<span></span>@if($_authorized_person['has_entered_address']),
                        {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder !!},
                        {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                        {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder !!}@endif<span></span>@if(!empty($_authorized_person['oib'])), OIB: {!! $_authorized_person['oib'] !!}@endif<span></span>@if($_i < (count($data['authorized_buyer_persons'])-1))@if(($_i+2) == count($data['authorized_buyer_persons'])) i @else, @endif @elseif(count($data['additional_buyers'])>1),@else <br>i @endif
                @endforeach

            @endif

            @foreach($data['additional_buyers'] as $_i => $additional_buyer)
                <br>
                {!! !empty($additional_buyer['name']) ? "<strong>".$additional_buyer['name']."</strong>" : $builder::$placeholder  !!},
                {!! !empty($additional_buyer['address']) ? $additional_buyer['address'] : $builder::$placeholder !!},
                {!! !empty($additional_buyer['postal_code']) ? $additional_buyer['postal_code'] : $builder::$placeholder !!}
                {!! !empty($additional_buyer['city']) ? $additional_buyer['city'] : $builder::$placeholder !!},
                {!! !empty($additional_buyer['country']) ? $additional_buyer['country'] : $builder::$placeholder !!}, OIB:
                {!! !empty($additional_buyer['oib']) ? $additional_buyer['oib'] : $builder::$placeholder !!}@if(!empty($data['authorized_additional_buyer_persons'][$_i])),@elseif( (count($data['additional_buyers']) - $_i) > 2),@elseif($_i != count($data['additional_buyers'])-1) <br>i @else, @endif
                @if(!empty($data['authorized_additional_buyer_persons'][$_i]))
                    @if(count($data['authorized_additional_buyer_persons'][$_i]) == 1)
                        kojeg zastupa
                    @else
                        kojeg zastupaju
                    @endif

                    @foreach($data['authorized_additional_buyer_persons'][$_i] as $_j => $_authorized_person)
                            {!! $_authorized_person['role'] ?: $builder::$placeholder !!} {!! $_authorized_person['name'] ?: $builder::$placeholder !!}<span></span>@if($_authorized_person['has_entered_address']),
                            {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder !!},
                            {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                            {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder !!}@endif<span></span>@if(!empty($_authorized_person['oib'])), OIB: {!! $_authorized_person['oib'] !!}@endif<span></span>@if($_j < (count($data['authorized_additional_buyer_persons'][$_i])-1))@if(($_j+2) == count($data['authorized_additional_buyer_persons'][$_i])) i @else, @endif @elseif(isset($data['additional_buyers'][$_i+1]))@if(count($data['additional_buyers']) == $_i+2) <br>i @else, @endif @else, @endif
                    @endforeach
                @endif

            @endforeach

            kao kupci <br><br>(u daljnjem tekstu zajednički: <strong>Kupac</strong>)
        </p>
    @else
        <p>

            <strong>{!! $data['buyer_name'] !!},</strong> {!! $data['buyer_address'] !!}, {!! $data['buyer_postal_code'] !!}
            {!! $data['buyer_city'] !!}, {!! $data['buyer_country'] !!}, OIB: {!! $data['buyer_oib'] !!},


            @if(!empty($data['authorized_buyer_persons']))
                @if(count($data['authorized_buyer_persons']) == 1)
                    kao kupac kojeg zastupa
                @else
                    kao kupac kojeg zastupaju
                @endif

                @foreach($data['authorized_buyer_persons'] as $_i => $_authorized_person)
                        {!! $_authorized_person['role'] !!} {!! $_authorized_person['name']  !!}<span></span>@if($_authorized_person['has_entered_address']),
                        {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder !!},
                        {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                        {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder !!}@endif<span></span>@if(!empty($_authorized_person['oib'])), OIB: {!! $_authorized_person['oib'] !!}@endif<span></span>@if($_i < (count($data['authorized_buyer_persons'])-1))@if(($_i+2) == count($data['authorized_buyer_persons'])) i @else, @endif @else, @endif
                @endforeach
                <br><br>(u daljnjem tekstu: <strong>Kupac</strong>)
            @else
                kao kupac <br><br>(u daljnjem tekstu: <strong>Kupac</strong>)
            @endif


        </p>
    @endif


    <p>
        sklopili su sljedeći:
    </p>
</div>

<h2 class="text-center editable-segment" data-type="title">
    UGOVOR O KUPOPRODAJI MOTORNOG VOZILA
</h2>


<div class="editable-segment" data-section="vehicle_information" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(1, true) }}. Motorno vozilo koje je predmet kupoprodaje
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Predmet ovog Ugovora je kupoprodaja motornog vozila u vlasništvu Prodavatelja, koje ima sljedeće značajke:
        </p>

        <table class="table">
            <tbody>
            <tr>
                <td class="strong">Registracijska oznaka vozila:</td>
                <td>{!! $data['registration'] !!}</td>
                <td class="strong">Vrsta vozila:</td>
                <td>{!! $data['vehicle_type'] !!}</td>
            </tr>
            <tr>
                <td class="strong">Marka vozila:</td>
                <td>{!! $data['brand'] !!}</td>
                <td class="strong">Tip vozila:</td>
                <td>{!! $data['model'] !!}</td>
            </tr>
            <tr>
                <td class="strong">Model vozila:</td>
                <td>{!! $data['model_2'] !!}</td>
                <td class="strong">Boja vozila:</td>
                <td>{!! $data['color'] !!}</td>
            </tr>
            <tr>
                <td class="strong">Broj šasije:</td>
                <td>{!! $data['vehicle_identification_number'] !!}</td>
                <td class="strong">Oblik karoserije:</td>
                <td>{!! $data['body_type'] !!}</td>
            </tr>
            <tr>
                <td class="strong">Država proizvodnje:</td>
                <td>{!! $data['manufactured_in_country'] !!}</td>
                <td class="strong">Proizvođač:</td>
                <td>{!! $data['manufacturer'] !!}</td>
            </tr>
            <tr>
                <td class="strong">Godina proizvodnje:</td>
                <td>{!! $data['manufactured_year'] !!}</td>
                <td class="strong">Datum prve registracije:</td>
                <td>{!! $data['first_registration_date'] !!}</td>
            </tr>
            <tr>
                <td class="strong">Osnovna namjena:</td>
                <td>{!! $data['main_purpose'] !!}</td>
                <td class="strong">Vrsta motora:</td>
                <td>{!! $data['engine_type'] !!}</td>
            </tr>
            <tr>
                <td class="strong">Snaga motora u kW:</td>
                <td>{!! $data['engine_power'] !!}</td>
                <td class="strong">Radni obujam motora u cm3:</td>
                <td>{!! $data['engine_volume'] !!}</td>
            </tr>
            </tbody>
        </table>

        <p>
            (u daljnjem tekstu: Vozilo).
        </p>
    </div>
</div>

<div class="editable-segment" data-section="financial_information"  data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(2, true) }}. Kupoprodajna cijena i uvjeti plaćanja
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Kupac se za Vozilo iz članka <span class="article-reference">{{ $builder->getArticleIndex(1) }}</span>. ovog Ugovora obvezuje Prodavatelju platiti kupoprodajnu cijenu u ukupnom iznosu od {!! StringHelper::amountToText($data['total_price']) !!} (u daljnjem tekstu: Cijena).
        </p>

        @if($data['price_payment_type'] == "in_full")
            @if($data['price_payment_time'] == "when_contract_signed_date")
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Ugovorne stranke suglasno utvrđuju da je u trenutku potpisivanja ovog Ugovora Kupac platio Prodavatelju Cijenu u cijelosti i to @if($data['payment_method'] == "cash") u gotovini. @elseif($data['payment_method'] == "iban") na IBAN {!! $data['payment_method_iban'] !!}.@endif
                </p>
            @elseif($data['price_payment_time'] == "paid_on_date")
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Ugovorne stranke suglasno utvrđuju da je dana {!! $data['price_payment_paid_on_date'] !!} Kupac platio Prodavatelju Cijenu u cijelosti i to @if($data['payment_method'] == "cash") u gotovini. @elseif($data['payment_method'] == "iban") na IBAN {!! $data['payment_method_iban'] !!}.@endif
                </p>
            @elseif($data['price_payment_time'] == "paid_by_latest_date")
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Kupac se obvezuje platiti Prodavatelju Cijenu u cijelosti najkasnije dana {!! $data['price_payment_paid_by_latest_date'] !!} i to @if($data['payment_method'] == "cash") u gotovini. @elseif($data['payment_method'] == "iban") na IBAN {!! $data['payment_method_iban'] !!}.@endif
                </p>
            @endif
        @elseif($data['price_payment_type'] == "installments")
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ugovorne stranke suglasno utvrđuju sljedeću dinamiku plaćanja Cijene:
            </p>

            <ul>
                @if(!empty($data['price_payment_installments']))
                    @foreach($data['price_payment_installments']  as $_i => $_installment)
                        <li>
                            {{ $_i+1 }}. dio Cijene u iznosu od {!! !empty($_installment['amount']) ? StringHelper::amountToText($_installment['amount']) : ($builder::$placeholder." EUR") !!}
                            @if($_installment['time'] == 'when_contract_signed_date')
                                Kupac je Prodavatelju platio @if($data['payment_method'] == "cash") u gotovini @elseif($data['payment_method'] == "iban") na IBAN {!! $data['payment_method_iban'] !!}@endif u trenutku potpisivanja ovog Ugovora, što Prodavatelj svojim potpisom na ovom Ugovoru potvrđuje.
                            @elseif($_installment['time'] == 'paid_on_date')
                                Kupac je Prodavatelju platio @if($data['payment_method'] == "cash") u gotovini @elseif($data['payment_method'] == "iban") na IBAN {!! $data['payment_method_iban'] !!}@endif dana {!! $_installment['paid_on_date'] !!}, što Prodavatelj svojim potpisom na ovom Ugovoru potvrđuje.
                            @elseif($_installment['time'] == 'paid_by_latest_date')
                                Kupac se obvezuje platiti Prodavatelju @if($data['payment_method'] == "cash") u gotovini @elseif($data['payment_method'] == "iban") na IBAN {!! $data['payment_method_iban'] !!}@endif najkasnije dana {!! $_installment['paid_by_latest_date'] !!}
                            @endif
                        </li>
                    @endforeach
                @else
                    {!! $builder::$placeholder !!}
                @endif
            </ul>
        @endif
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(3, true) }}. Porezi, pristojbe i troškovi
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            {{ $data['ownership_transfer_expenses_person'] == 'seller' ? 'Prodavatelj' : "Kupac" }} se obvezuje snositi sve poreze, pristojbe i troškove vezane uz ovaj ugovor i prijepis vlasništva Vozila s Prodavatelja na Kupca.
        </p>
    </div>
</div>

<div class="editable-segment" data-section="ownership_transfer" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(4, true) }}. Odgovornost za nedostatke
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        @if($data['seller_accepts_responsibility_for_defects'])
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ugovorne stranke suglasno utvrđuju da Prodavatelj, uz pretpostavke koje su propisane važećim Zakonom o obveznim odnosima, odgovara Kupcu za materijalne nedostatke koje je Vozilo imalo u trenutku prijelaza rizika na Kupca, kao i za one materijalne nedostatke koji se pojave nakon prijelaza rizika na Kupca ako su posljedica uzroka koji je postojao prije toga.
            </p>
        @else
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ugovorne stranke suglasno utvrđuju da Kupac kupuje Vozilo u stanju u kakvom se nalazi po načelu viđeno-kupljeno, što znači da se Kupac odriče prava na isticanje bilo kakvih prigovora Prodavatelju zbog eventualnih materijalnih nedostataka na Vozilu te da Prodavatelj ne odgovara Kupcu za eventualne materijalne nedostatke na Vozilu.    </p>
        @endif

        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Prodavatelj izjavljuje i jamči Kupcu da na Vozilu ne postoji nikakvo pravo trećega koje isključuje, umanjuje ili ograničuje prava Kupca. Između ostalog, Prodavatelj jamči Kupcu da je Vozilo njegovo isključivo vlasništvo te da nije opterećeno nikakvim teretima. Isto tako, Prodavatelj jamči Kupcu da Vozilo nije predmet spora u parničnom, ovršnom, upravnom i/ili drugom postupku na temelju kojih bi mogla biti ograničena prava Kupca.
        </p>

        @if(!$data['any_seller_cosigner_exists'])
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Prodavatelj izjavljuje i jamči Kupcu da Vozilo nije dio bračne, izvanbračne ili partnerske stečevine Prodavatelja.
            </p>
        @endif
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(5, true) }}. Stupanje u posjed
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Prodavatelj se obvezuje predati Vozilo Kupcu u posjed
            @if($data['ownership_transfer_time'] == 'when_paid_in_full')
                odmah po isplati Cijene u cijelosti.
            @elseif($data['ownership_transfer_time'] == 'when_contract_signed')
                odmah po sklapanju ovog Ugovora.
            @elseif($data['ownership_transfer_time'] == 'custom')
                {!! $data['ownership_transfer_time_custom'] !!}.
            @else
                odmah po isplati Cijene u cijelosti.
            @endif
        </p>
    </div>
</div>


@php $dynamic_index = 6; @endphp

@if(!empty($data['custom_provisions']))
    @foreach($data['custom_provisions'] as $_i => $_custom_provision)
        @if(!empty(trim($_custom_provision['provision'])))
            <div class="editable-segment" data-type="article">
                <p class="article-header">
                    Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. {{ $_custom_provision['title'] }}
                </p>
                <div class="article-body">
                    @php
                        $p_index = 1;

                        $_custom_provision_content = App\Helpers\StringHelper::wordpressContent($_custom_provision['provision']);

                        foreach(explode("<p>", $_custom_provision_content) as $_p) {
                            if(!empty(trim($_p))){
                                echo "<p> ".$builder->getCurrentArticleIndex(). "." .$p_index++.". $_p";
                            }
                        }
                    @endphp
                </div>
            </div>
        @endif
    @endforeach
@endif

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Ništetnost
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ako se bilo koja odredba ovog Ugovora pokaže ništetnom, ostale odredbe ovog Ugovora u cijelosti ostaju na snazi.
            U slučaju ništetnosti jedne ili više odredaba ovog Ugovora, ugovorne stranke se obvezuju odmah pristupiti zamjeni ništetnih odredaba drugima,
            vodeći pri tome računa da se izmijenjenim odredbama postigne isti stupanj zadovoljenja interesa ugovornih stranaka, ali na način koji je dopušten.
        </p>
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Izmjene ugovora
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da bilo kakva izmjena, dopuna ili dodatak ovom Ugovoru moraju biti sastavljeni u pisanom obliku,
            valjano potpisani i odobreni od svih ugovornih stranaka, a eventualni usmeni dogovori moraju biti pisano potvrđeni.
        </p>
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Mjerodavno pravo
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da se na sva pitanja koja proizlaze iz ili u vezi s ovim Ugovorom
            koja nisu izrijekom uređena u njemu primjenjuju odgovarajući propisi na snazi u Republici Hrvatskoj.
        </p>
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Rješavanje sporova
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da će sve sporove koji nastanu iz ili u vezi s ovim Ugovorom koje ne mogu riješiti mirnim putem
            rješavati stvarno i mjesno nadležni sud.
        </p>
    </div>
</div>

<div class="editable-segment" data-section="final_provisions" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Broj primjeraka ugovora
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ovaj Ugovor sklapa se u {!! $data['contract_copy_count'] !!} {{ StringHelper::getPrimjerakaString($data['contract_copy_count']) }}, od kojih {!! $data['seller_contract_copy_count'] !!} zadržava Prodavatelj,
            a {!! $data['buyer_contract_copy_count'] !!} zadržava Kupac.
        </p>
    </div>
</div>


@include('layouts.document.partials.signatures', [
  'data' => $data,
  'builder' => $builder,
  'dynamic_index' => $dynamic_index,
  'text' => 'Ugovorne stranke su suglasne da je u odredbama ovog Ugovora sadržana njihova prava i stvarna volja te ga u znak prihvata prava i obveza koje iz Ugovora proizlaze vlastoručno potpisuju.',
  'left_parties' => $builder->getParties()->where('side', 'left')->values()->toArray(),
  'right_parties' => $builder->getParties()->where('side', 'right')->values()->toArray(),
  'default_party_label_left' => 'Prodavatelj',
  'default_party_label_right' => 'Kupac',
])
