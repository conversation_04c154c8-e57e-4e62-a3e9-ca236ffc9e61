@extends($builder->getLayout())
@section('content')
@php

$data["donor_name"] = $builder->get("donor_name");
$data["donor_address"] = $builder->get("donor_address");
$data["donor_city"] = $builder->get("donor_city");
$data["donor_postal_code"] = $builder->get("donor_postal_code");
$data["donor_country"] = $builder->get("donor_country");
$data["donor_oib"] = $builder->get("donor_oib");

$data['donor_cosigner_exists'] = $builder->get('donor_cosigner_exists', false);

$donor_cosigner_type = $builder->get("donor_cosigner_type", false);

$data["donor_cosigner_type"] = "Bračni drug";
if($donor_cosigner_type == 1){
    $data["donor_cosigner_type"] = "Izvanbračni drug";
}
elseif($donor_cosigner_type == 2){
    $data["donor_cosigner_type"] = "Životni partner";
}

$data["donor_cosigner_name"] = $builder->get("donor_cosigner_name");
$data["donor_cosigner_address"] = $builder->get("donor_cosigner_address");
$data["donor_cosigner_city"] = $builder->get("donor_cosigner_city");
$data["donor_cosigner_postal_code"] = $builder->get("donor_cosigner_postal_code");
$data["donor_cosigner_country"] = $builder->get("donor_cosigner_country");
$data["donor_cosigner_oib"] = $builder->get("donor_cosigner_oib");

$data["additional_donors"] = $builder->get("additional_donors", false);
$data["authorized_donor_persons"] = $builder->get("authorized_donor_persons", false);
$data["authorized_additional_donor_persons"] = $builder->get("authorized_additional_donor_persons", false);

// standardize array keys for parties
if(!empty($data['authorized_donor_persons'])){
    $data['authorized_donor_persons'] = array_values($data['authorized_donor_persons']);
	
    // default case
    foreach($data['authorized_donor_persons'] as &$_authorized_donor_person){
        if(empty($_authorized_donor_person['name'])) $_authorized_donor_person['name'] = $builder->get('placeholder');
        if(empty($_authorized_donor_person['role'])) $_authorized_donor_person['role'] = $builder->get('placeholder');
    
        $_authorized_donor_person['has_entered_address'] = !empty($_authorized_donor_person['address'])
        || !empty($_authorized_donor_person['city'])
        || !empty($_authorized_donor_person['postal_code']);
    }

	unset($_authorized_donor_person);
}

$_new_i = 0;
if(!empty($data['additional_donors'])){

    foreach($data['additional_donors'] as $_i => $_additional_donor){

        if($_new_i != $_i){
            $data['additional_donors'][$_new_i] = $data['additional_donors'][$_i];
            unset($data['additional_donors'][$_i]);
        }

        if(!empty($_additional_donor['donor_cosigner_exists'])){
            $donor_cosigner_type = $_additional_donor['donor_cosigner_type'];

            $data['additional_donors'][$_new_i]['donor_cosigner_type'] = "Bračni drug";

            if($donor_cosigner_type == 1){
                $data['additional_donors'][$_new_i]['donor_cosigner_type'] = "Izvanbračni drug";
            }
            elseif($donor_cosigner_type == 2){
                $data['additional_donors'][$_new_i]['donor_cosigner_type'] = "Životni partner";
            }

        }

        if(isset($data['authorized_additional_donor_persons'][$_i])){

            if($_new_i != $_i){
                $data['authorized_additional_donor_persons'][$_new_i] = $data['authorized_additional_donor_persons'][$_i];
                unset($data['authorized_additional_donor_persons'][$_i]);
            }

            $_new_j = 0;

            foreach($data['authorized_additional_donor_persons'][$_new_i] as $_j => $_person){

                $data['authorized_additional_donor_persons'][$_new_i][$_j]['has_entered_address'] = !empty($_person['address'])
                || !empty($_person['city'])
                || !empty($_person['postal_code']);

                if($_new_j != $_j){
                    $data['authorized_additional_donor_persons'][$_new_i][$_new_j] = $data['authorized_additional_donor_persons'][$_new_i][$_j];
                    unset($data['authorized_additional_donor_persons'][$_new_i][$_j]);
                }

                $_new_j++;
            }
        }
        $_new_i++;
    }
}

$data["beneficiary_name"] = $builder->get("beneficiary_name");
$data["beneficiary_address"] = $builder->get("beneficiary_address");
$data["beneficiary_city"] = $builder->get("beneficiary_city");
$data["beneficiary_postal_code"] = $builder->get("beneficiary_postal_code");
$data["beneficiary_country"] = $builder->get("beneficiary_country");
$data["beneficiary_oib"] = $builder->get("beneficiary_oib");

$data["authorized_beneficiary_persons"] = $builder->get("authorized_beneficiary_persons", false);
$data["additional_beneficiaries"] = $builder->get("additional_beneficiaries", false);
$data["authorized_additional_beneficiary_persons"] = $builder->get("authorized_additional_beneficiary_persons", false);

// standardize array keys for parties
if(!empty($data['authorized_beneficiary_persons'])){
    $data['authorized_beneficiary_persons'] = array_values($data['authorized_beneficiary_persons']);
	
    // default case
    foreach($data['authorized_beneficiary_persons'] as &$_authorized_beneficiary_person){
        if(empty($_authorized_beneficiary_person['name'])) $_authorized_beneficiary_person['name'] = $builder->get('placeholder');
        if(empty($_authorized_beneficiary_person['role'])) $_authorized_beneficiary_person['role'] = $builder->get('placeholder');
    
        $_authorized_beneficiary_person['has_entered_address'] = !empty($_authorized_beneficiary_person['address'])
        || !empty($_authorized_beneficiary_person['city'])
        || !empty($_authorized_beneficiary_person['postal_code']);
    }

	// always unset when looping by reference!
	unset($_authorized_beneficiary_person);
}

$_new_i = 0;
if(!empty($data['additional_beneficiaries'])){

    foreach($data['additional_beneficiaries'] as $_i => $_additional_beneficiary){

        if($_new_i != $_i){
            $data['additional_beneficiaries'][$_new_i] =  $data['additional_beneficiaries'][$_i];
            unset($data['additional_beneficiaries'][$_i]);
        }

        if(isset($data['authorized_additional_beneficiary_persons'][$_i])){

            if($_new_i != $_i){
                $data['authorized_additional_beneficiary_persons'][$_new_i] = $data['authorized_additional_beneficiary_persons'][$_i];
                unset($data['authorized_additional_beneficiary_persons'][$_i]);
            }

            $_new_j = 0;

            foreach($data['authorized_additional_beneficiary_persons'][$_new_i] as $_j => $_person){

                $data['authorized_additional_beneficiary_persons'][$_new_i][$_j]['has_entered_address'] = !empty($_person['address'])
                || !empty($_person['city'])
                || !empty($_person['postal_code']);

                if($_new_j != $_j){
                    $data['authorized_additional_beneficiary_persons'][$_new_i][$_new_j] = $data['authorized_additional_beneficiary_persons'][$_new_i][$_j];
                    unset($data['authorized_additional_beneficiary_persons'][$_new_i][$_j]);
                }

                $_new_j++;
            }
        }
        $_new_i++;
    }
}


$data["additional_realestates"] = $builder->get("additional_realestates", false);

// standardize array keys for additional_realestates
if(!empty($data['additional_realestates'])){

    // standardize array keys
    $data['additional_realestates'] = array_values($data['additional_realestates']);

    foreach($data['additional_realestates'] as &$_additional_realestate){

        // many beneficiaries, one donor
        if(!empty($data['additional_beneficiaries']) && empty($data['additional_donors'])) {
            $_additional_realestate['beneficiaries_ownership_of_donor_realestate_ratio_numerator'] = array_values(isset($_additional_realestate['beneficiaries_ownership_of_donor_realestate_ratio_numerator']) ? $_additional_realestate['beneficiaries_ownership_of_donor_realestate_ratio_numerator'] : []);
            $_additional_realestate['beneficiaries_ownership_of_donor_realestate_ratio_denominator'] = array_values(isset($_additional_realestate['beneficiaries_ownership_of_donor_realestate_ratio_denominator']) ? $_additional_realestate['beneficiaries_ownership_of_donor_realestate_ratio_denominator'] : []);
        }
        // one beneficiary, many donors
        elseif(empty($data['additional_beneficiaries']) && !empty($data['additional_donors'])) {
            $_additional_realestate['donors_realestate_ownership_ratio_numerator'] = array_values(isset($_additional_realestate['donors_realestate_ownership_ratio_numerator']) ? $_additional_realestate['donors_realestate_ownership_ratio_numerator'] : []);
            $_additional_realestate['donors_realestate_ownership_ratio_denominator'] = array_values(isset($_additional_realestate['donors_realestate_ownership_ratio_denominator']) ? $_additional_realestate['donors_realestate_ownership_ratio_denominator'] : []);

            $_additional_realestate['beneficiary_acquires_full_ownership_of_donors_realestate'] = array_values(isset($_additional_realestate['beneficiary_acquires_full_ownership_of_donors_realestate']) ? $_additional_realestate['beneficiary_acquires_full_ownership_of_donors_realestate'] : []);
            $_additional_realestate['beneficiary_ownership_of_donors_realestate_ratio_numerator'] = array_values(isset($_additional_realestate['beneficiary_ownership_of_donors_realestate_ratio_numerator']) ? $_additional_realestate['beneficiary_ownership_of_donors_realestate_ratio_numerator'] : []);
            $_additional_realestate['beneficiary_ownership_of_donors_realestate_ratio_denominator'] = array_values(isset($_additional_realestate['beneficiary_ownership_of_donors_realestate_ratio_denominator']) ? $_additional_realestate['beneficiary_ownership_of_donors_realestate_ratio_denominator'] : []);
        }
        // many beneficiaries, many donors
        elseif(!empty($data['additional_beneficiaries']) && !empty($data['additional_donors'])) {
            $_additional_realestate['donors_realestate_ownership_ratio_numerator'] = array_values(isset($_additional_realestate['donors_realestate_ownership_ratio_numerator']) ? $_additional_realestate['donors_realestate_ownership_ratio_numerator'] : []);
            $_additional_realestate['donors_realestate_ownership_ratio_denominator'] = array_values(isset($_additional_realestate['donors_realestate_ownership_ratio_denominator']) ? $_additional_realestate['donors_realestate_ownership_ratio_denominator'] : []);

            $_additional_realestate['beneficiaries_acquire_full_ownership_of_donors_realestate'] = array_values(isset($_additional_realestate['beneficiaries_acquire_full_ownership_of_donors_realestate']) ? $_additional_realestate['beneficiaries_acquire_full_ownership_of_donors_realestate'] : []);

            foreach($_additional_realestate['beneficiaries_acquire_full_ownership_of_donors_realestate'] as &$_item){
                $_item = array_values($_item);
            }

            $_additional_realestate['beneficiaries_ownership_of_donors_realestate_ratio_numerator'] = array_values(isset($_additional_realestate['beneficiaries_ownership_of_donors_realestate_ratio_numerator']) ? $_additional_realestate['beneficiaries_ownership_of_donors_realestate_ratio_numerator'] : []);
            $_additional_realestate['beneficiaries_ownership_of_donors_realestate_ratio_denominator'] = array_values(isset($_additional_realestate['beneficiaries_ownership_of_donors_realestate_ratio_denominator']) ? $_additional_realestate['beneficiaries_ownership_of_donors_realestate_ratio_denominator'] : []);

            foreach($_additional_realestate['beneficiaries_ownership_of_donors_realestate_ratio_numerator'] as &$_item){
                $_item = array_values($_item);
            }

            foreach($_additional_realestate['beneficiaries_ownership_of_donors_realestate_ratio_denominator'] as &$_item){
                $_item = array_values($_item);
            }

        }

        if(!empty($_additional_realestate['type_particular_additional_land_plots'])){
            $_additional_realestate['type_particular_additional_land_plots'] = array_values($_additional_realestate['type_particular_additional_land_plots']);
        }

        if(!empty($_additional_realestate['type_other_additional_land_plots'])){
            $_additional_realestate['type_other_additional_land_plots'] = array_values($_additional_realestate['type_other_additional_land_plots']);

            foreach($_additional_realestate['type_other_additional_land_plots'] as &$_additional_land_plot) {
                if(!empty($_additional_land_plot['type_other_additional_possession_area_and_identification'])) {
                    $_additional_land_plot['type_other_additional_possession_area_and_identification'] = array_values($_additional_land_plot['type_other_additional_possession_area_and_identification']);
                }
            }

            unset($_additional_land_plot);
        }

        if(!empty($_additional_realestate['type_other_additional_possession_area_and_identification'])){
            $_additional_realestate['type_other_additional_possession_area_and_identification'] = array_values($_additional_realestate['type_other_additional_possession_area_and_identification']);
        }

    }

    unset($_additional_realestate); // always unset if looping by reference!
}

$data["realestate_type"] = $builder->get("realestate_type", false) ?: 'particular';
$data["type_particular_municipal_court"] = $builder->get("type_particular_municipal_court");
$data["type_particular_land_registry"] = $builder->get("type_particular_land_registry");
$data["type_particular_cadastral_municipality"] = $builder->get("type_particular_cadastral_municipality");
$data["type_particular_land_registry_folio_number"] = $builder->get("type_particular_land_registry_folio_number");
$data["type_particular_possession_number"] = $builder->get("type_particular_possession_number");
$data["type_particular_possession_area"] = $builder->get("type_particular_possession_area");
$data["type_particular_possession_area_type"] = $builder->get("type_particular_possession_area_type", null);
$data["type_particular_possession_identification"] = $builder->get("type_particular_possession_identification");
$data["type_particular_ownership_sheet_data"] = $builder->get("type_particular_ownership_sheet_data");
$data["type_particular_has_encumbrances_sheet_data"] = $builder->get("type_particular_has_encumbrances_sheet_data", false);
$data["type_particular_encumbrances_sheet_data"] = $builder->get("type_particular_encumbrances_sheet_data");
$data["type_particular_additional_realestate_data"] = $builder->get("type_particular_additional_realestate_data", false);

$data["type_particular_additional_land_plots"] = $builder->get("type_particular_additional_land_plots", false);
if(!empty($data['type_particular_additional_land_plots'])){
    $data['type_particular_additional_land_plots'] = array_values($data['type_particular_additional_land_plots']);
}

$data["type_flat_municipal_court"] = $builder->get("type_flat_municipal_court");
$data["type_flat_land_registry"] = $builder->get("type_flat_land_registry");
$data["type_flat_deposited_contracts_book_name"] = $builder->get("type_flat_deposited_contracts_book_name");
$data["type_flat_subfolio_number"] = $builder->get("type_flat_subfolio_number");
$data["type_flat_folio_number"] = $builder->get("type_flat_folio_number");
$data["type_flat_has_encumbrances_sheet_data"] = $builder->get("type_flat_has_encumbrances_sheet_data", false);
$data["type_flat_encumbrances_sheet_data"] = $builder->get("type_flat_encumbrances_sheet_data");
$data["type_flat_possession_sheet_section_one_data"] = $builder->get("type_flat_possession_sheet_section_one_data");
$data["type_flat_possession_sheet_section_two_data"] = $builder->get("type_flat_possession_sheet_section_two_data");
$data["type_flat_additional_realestate_data"] = $builder->get("type_flat_additional_realestate_data", false);

$data["type_other_municipal_court"] = $builder->get("type_other_municipal_court");
$data["type_other_land_registry"] = $builder->get("type_other_land_registry");
$data["type_other_cadastral_municipality"] = $builder->get("type_other_cadastral_municipality");
$data["type_other_land_registry_folio_number"] = $builder->get("type_other_land_registry_folio_number");
$data["type_other_possession_number"] = $builder->get("type_other_possession_number");
$data["type_other_possession_area"] = $builder->get("type_other_possession_area");
$data["type_other_possession_area_type"] = $builder->get("type_other_possession_area_type", null);
$data["type_other_possession_identification"] = $builder->get("type_other_possession_identification");
$data["type_other_has_encumbrances_sheet_data"] = $builder->get("type_other_has_encumbrances_sheet_data", false);
$data["type_other_encumbrances_sheet_data"] = $builder->get("type_other_encumbrances_sheet_data");
$data["type_other_additional_realestate_data"] = $builder->get("type_other_additional_realestate_data", false);


$data["type_other_additional_possession_area_and_identification"] = $builder->get("type_other_additional_possession_area_and_identification", false);
if(!empty($data['type_other_additional_possession_area_and_identification'])){
    $data['type_other_additional_possession_area_and_identification'] = array_values($data['type_other_additional_possession_area_and_identification']);
}

$data["type_other_additional_land_plots"] = $builder->get("type_other_additional_land_plots", false);
if(!empty($data['type_other_additional_land_plots'])){
    $data['type_other_additional_land_plots'] = array_values($data['type_other_additional_land_plots']);

    foreach($data['type_other_additional_land_plots'] as &$_additional_land_plot) {
        if(!empty($_additional_land_plot['type_other_additional_possession_area_and_identification'])) {
            $_additional_land_plot['type_other_additional_possession_area_and_identification'] = array_values($_additional_land_plot['type_other_additional_possession_area_and_identification']);
        }
    }

    unset($_additional_land_plot);
}

// one beneficiary, one donor
if(empty($data['additional_beneficiaries']) && empty($data['additional_donors'])){
    $data["donor_has_full_realestate_ownership"] = $builder->get("donor_has_full_realestate_ownership", false);
    $data["donor_realestate_ownership_ratio_numerator"] = $builder->get("donor_realestate_ownership_ratio_numerator", false);
    $data["donor_realestate_ownership_ratio_denominator"] = $builder->get("donor_realestate_ownership_ratio_denominator", false);

    $data["beneficiary_acquires_full_ownership_of_donor_realestate"] = $builder->get("beneficiary_acquires_full_ownership_of_donor_realestate", false);
    $data["beneficiary_ownership_of_donor_realestate_ratio_numerator"] = $builder->get("beneficiary_ownership_of_donor_realestate_ratio_numerator", false);
    $data["beneficiary_ownership_of_donor_realestate_ratio_denominator"] = $builder->get("beneficiary_ownership_of_donor_realestate_ratio_denominator", false);
}
// many beneficiaries, one donor
elseif(!empty($data['additional_beneficiaries']) && empty($data['additional_donors'])) {
    $data["donor_has_full_realestate_ownership"] = $builder->get("donor_has_full_realestate_ownership", false);
    $data["donor_realestate_ownership_ratio_numerator"] = $builder->get("donor_realestate_ownership_ratio_numerator", false);
    $data["donor_realestate_ownership_ratio_denominator"] = $builder->get("donor_realestate_ownership_ratio_denominator", false);

    $data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'] = array_values($builder->get('beneficiaries_ownership_of_donor_realestate_ratio_numerator', false) ?: []);
    $data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'] = array_values($builder->get('beneficiaries_ownership_of_donor_realestate_ratio_denominator', false) ?: []);
}
// one beneficiary, many donors
elseif(empty($data['additional_beneficiaries']) && !empty($data['additional_donors'])) {
    $data['donors_realestate_ownership_ratio_numerator'] = array_values($builder->get('donors_realestate_ownership_ratio_numerator', false) ?: []);
    $data['donors_realestate_ownership_ratio_denominator'] = array_values($builder->get('donors_realestate_ownership_ratio_denominator', false) ?: []);

    $data['beneficiary_acquires_full_ownership_of_donors_realestate'] = array_values($builder->get('beneficiary_acquires_full_ownership_of_donors_realestate', false) ?: []);
    $data['beneficiary_ownership_of_donors_realestate_ratio_numerator'] = array_values($builder->get('beneficiary_ownership_of_donors_realestate_ratio_numerator', false) ?: []);
    $data['beneficiary_ownership_of_donors_realestate_ratio_denominator'] = array_values($builder->get('beneficiary_ownership_of_donors_realestate_ratio_denominator', false) ?: []);
}
// many beneficiaries, many donors
elseif(!empty($data['additional_beneficiaries']) && !empty($data['additional_donors'])) {
    $data['donors_realestate_ownership_ratio_numerator'] = array_values($builder->get('donors_realestate_ownership_ratio_numerator', false) ?: []);
    $data['donors_realestate_ownership_ratio_denominator'] = array_values($builder->get('donors_realestate_ownership_ratio_denominator', false) ?: []);

    $data['beneficiaries_acquire_full_ownership_of_donors_realestate'] = array_values($builder->get('beneficiaries_acquire_full_ownership_of_donors_realestate', false) ?: []);

    foreach($data['beneficiaries_acquire_full_ownership_of_donors_realestate'] as &$_item){
        $_item = array_values($_item);
    }

    $data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'] = array_values($builder->get('beneficiaries_ownership_of_donors_realestate_ratio_numerator', false) ?: []);
    $data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'] = array_values($builder->get('beneficiaries_ownership_of_donors_realestate_ratio_denominator', false) ?: []);

    foreach($data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'] as &$_item){
        $_item = array_values($_item);
    }

    foreach($data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'] as &$_item){
        $_item = array_values($_item);
    }

}

$data["realestate_value"] = $builder->get("realestate_value");
$data["tax_payer"] = $builder->get("tax_payer", false);
$data["beneficiary_has_responsibility_to_fulfill"] = $builder->get("beneficiary_has_responsibility_to_fulfill", false);
$data["beneficiary_responsibility"] = $builder->get("beneficiary_responsibility");
$data["ownership_transfer_time"] = $builder->get("ownership_transfer_time", false);
$data["ownership_transfer_time_custom"] = $builder->get("ownership_transfer_time_custom");
$data["has_occupancy_right"] = $builder->get("has_occupancy_right", false);
$data["occupancy_right_duration"] = $builder->get("occupancy_right_duration", false);
$data["occupancy_right_duration_custom"] = $builder->get("occupancy_right_duration_custom");
$data["has_usus_right"] = $builder->get("has_usus_right", false);
$data["usus_right_duration"] = $builder->get("usus_right_duration", false);
$data["usus_right_duration_custom"] = $builder->get("usus_right_duration_custom");
$data["has_ususfructus_right"] = $builder->get("has_ususfructus_right", false);
$data["ususfructus_right_duration"] = $builder->get("ususfructus_right_duration", false);
$data["ususfructus_right_duration_custom"] = $builder->get("ususfructus_right_duration_custom");


$data["contract_copy_count"] = $builder->get("contract_copy_count");
$data["donor_contract_copy_count"] = $builder->get("donor_contract_copy_count");

$data["additional_donor_contract_copy_count"] = $builder->get("additional_donor_contract_copy_count", false);
if(!empty($data["additional_donor_contract_copy_count"])){
    $data["additional_donor_contract_copy_count"] = array_values($data["additional_donor_contract_copy_count"]);
}

$data["additional_beneficiary_contract_copy_count"] = $builder->get("additional_beneficiary_contract_copy_count", false);
if(!empty($data["additional_beneficiary_contract_copy_count"])){
    $data["additional_beneficiary_contract_copy_count"] = array_values($data["additional_beneficiary_contract_copy_count"]);
}

$data["beneficiary_contract_copy_count"] = $builder->get("beneficiary_contract_copy_count");
$data["contract_date"] = $builder->get("contract_date");
$data["contract_place"] = $builder->get("contract_place");
$data["custom_provisions"] = $builder->get("custom_provisions", false);

@endphp

@include($builder->render_view, ['builder' => $builder, 'data' => $data])

@endsection
