<p>
    {{ $article_index }}.{{ $singleton->p_index++ }}.
    @if(empty($additional_donors))
        Ugovorne stranke suglasno utvrđuju da je Darovatelj vlasnik
        @if($donor_has_full_realestate_ownership)
            vlasničkog dijela 1/1
        @else
            suvlasničkog dijela {!! !empty($data['donor_realestate_ownership_ratio_numerator']) ? $data['donor_realestate_ownership_ratio_numerator'] : $builder::$placeholder !!}/{!! !empty($data['donor_realestate_ownership_ratio_denominator']) ? $data['donor_realestate_ownership_ratio_denominator'] : $builder::$placeholder !!}
        @endif
    @else
        Ugovorne stranke suglasno utvrđuju da je Darovatelj {!! $donor_name !!} vlasnik suvlasničkog dijela {!! !empty($data['donors_realestate_ownership_ratio_numerator'][0]) ? $data['donors_realestate_ownership_ratio_numerator'][0] : $builder::$placeholder !!}/{!! !empty($data['donors_realestate_ownership_ratio_denominator'][0]) ? $data['donors_realestate_ownership_ratio_denominator'][0] : $builder::$placeholder !!}@if(count($additional_donors) == 1) i da je @else, da je @endif
        @foreach($additional_donors as $_ad_i => $_additional_donor)
            Darovatelj {!! $_additional_donor['name'] ?: $builder::$placeholder !!} vlasnik suvlasničkog dijela {!! !empty($data['donors_realestate_ownership_ratio_numerator'][$_ad_i+1]) ? $data['donors_realestate_ownership_ratio_numerator'][$_ad_i+1] : $builder::$placeholder !!}/{!! !empty($data['donors_realestate_ownership_ratio_denominator'][$_ad_i+1]) ? $data['donors_realestate_ownership_ratio_denominator'][$_ad_i+1] : $builder::$placeholder !!}@if($_ad_i < count($additional_donors)-1)@if($_ad_i == (count($additional_donors)-2)) i da je @else, da je @endif @endif
        @endforeach
    @endif

@if($data['realestate_type'] == 'particular')
        nekretnine upisane u zemljišnoj knjizi koju vodi {!! $data['type_particular_municipal_court'] ?: $builder::$placeholder !!}, {!! $data['type_particular_land_registry'] ?: $builder::$placeholder !!},
        katastarska općina {!! $data['type_particular_cadastral_municipality'] ?: $builder::$placeholder !!}, u zemljišnoknjižni uložak {!! $data['type_particular_land_registry_folio_number'] ?: $builder::$placeholder !!},
        k. č. br. {!! $data['type_particular_possession_number'] ?: $builder::$placeholder !!}, površine {!! StringHelper::getPossessionAreaString($data['type_particular_possession_area'], $data['type_particular_possession_area_type'] ?? null) !!}, oznake zemljišta {!! $data['type_particular_possession_identification'] ?: $builder::$placeholder !!}@if(!empty($data['type_particular_additional_land_plots']) && count($data['type_particular_additional_land_plots']) == 1) i @else, @endif
        @if(!empty($data['type_particular_additional_land_plots']))
            @foreach($data['type_particular_additional_land_plots'] as $_alp_i => $_type_particular_additional_land_plot)
                k. č. br. {!! $_type_particular_additional_land_plot['type_particular_possession_number'] ?: $builder::$placeholder !!}, površine {!! StringHelper::getPossessionAreaString($_type_particular_additional_land_plot['type_particular_possession_area'], $_type_particular_additional_land_plot['type_particular_possession_area_type'] ?? null) !!}, oznake zemljišta {!! $_type_particular_additional_land_plot['type_particular_possession_identification'] ?: $builder::$placeholder !!}@if($_alp_i == (count($data['type_particular_additional_land_plots'])-2)) i @else, @endif
            @endforeach
        @endif
        {!! $data['type_particular_ownership_sheet_data'] ?: $builder::$placeholder !!} (u daljnjem tekstu: {{$realestate_index_nominativ}}).
</p>

    @if($data['type_particular_has_encumbrances_sheet_data'])
        <p>
            {{ $article_index }}.{{ $singleton->p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da je u teretovnici (listu C) za {{$realestate_index_akuzativ}} izvršen sljedeći upis: <br/> <br/>
            <i>"{!! $data['type_particular_encumbrances_sheet_data'] ? nl2br($data['type_particular_encumbrances_sheet_data']) : $builder::$placeholder !!}."</i>
        </p>
    @endif

    @if(!empty($data['type_particular_additional_realestate_data']) && !ctype_space($data['type_particular_additional_realestate_data']))
        <p>
            {{ $article_index }}.{{ $singleton->p_index++ }}.
            {!! nl2br($data['type_particular_additional_realestate_data']) !!}
        </p>
    @endif


@elseif($data['realestate_type'] == 'flat')
        nekretnine upisane u knjizi položenih ugovora {!! $data['type_flat_deposited_contracts_book_name'] ?: $builder::$placeholder !!} koju vodi
        {!! $data['type_flat_municipal_court'] ?: $builder::$placeholder !!}, {!! $data['type_flat_land_registry'] ?: $builder::$placeholder !!},
        poduložak {!! $data['type_flat_subfolio_number'] ?: $builder::$placeholder !!}, zemljišnoknjižni uložak {!! $data['type_flat_folio_number'] ?: $builder::$placeholder !!},
         {!! $data['type_flat_possession_sheet_section_one_data'] ?: $builder::$placeholder !!},
        {!! $data['type_flat_possession_sheet_section_two_data'] ?: $builder::$placeholder !!} (u daljnjem tekstu: {{$realestate_index_nominativ}}).
    </p>

    @if($data['type_flat_has_encumbrances_sheet_data'])
        <p>
            {{ $article_index }}.{{ $singleton->p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da je u teretovnici (listu C) za {{$realestate_index_akuzativ}} izvršen sljedeći upis: <br/> <br/>
            <i>"{!! $data['type_flat_encumbrances_sheet_data'] ? nl2br($data['type_flat_encumbrances_sheet_data']) : $builder::$placeholder !!}."</i>
        </p>
    @endif

    @if(!empty($data['type_flat_additional_realestate_data']))
        <p>
            {{ $article_index }}.{{ $singleton->p_index++ }}.
            {!! nl2br($data['type_flat_additional_realestate_data']) !!}
        </p>
    @endif

@elseif($data['realestate_type'] == 'other')

    @if(empty($data['type_other_additional_land_plots']))
            nekretnine upisane u zemljišnoj knjizi koju vodi {!! $data['type_other_municipal_court'] ?: $builder::$placeholder !!}, {!! $data['type_other_land_registry'] ?: $builder::$placeholder !!},
            katastarska općina {!! $data['type_other_cadastral_municipality'] ?: $builder::$placeholder !!}, u zemljišnoknjižni uložak {!! $data['type_other_land_registry_folio_number'] ?: $builder::$placeholder !!},
            k. č. br. {!! $data['type_other_possession_number'] ?: $builder::$placeholder !!}, površine {!! StringHelper::getPossessionAreaString($data['type_other_possession_area'], $data['type_other_possession_area_type'] ?? null) !!}, oznake zemljišta {!! $data['type_other_possession_identification'] ?: $builder::$placeholder !!}
            @if(!empty($data['type_other_additional_possession_area_and_identification']))
                (koju čine: @foreach($data['type_other_additional_possession_area_and_identification'] as $_i => $_additional) {!! $_additional['identification'] ?: $builder::$placeholder !!} površine {!! StringHelper::getPossessionAreaString($_additional['area'], $_additional['area_type'] ?? null) !!}@if($_i < (count($data['type_other_additional_possession_area_and_identification'])-1))@if(($_i+2) == count($data['type_other_additional_possession_area_and_identification'])) i @else, @endif @else{{ ')' }} @endif @endforeach
            @endif
            (u daljnjem tekstu: {{$realestate_index_nominativ}}).
        </p>
    @else
            nekretnine upisane u zemljišnoj knjizi koju vodi {!! $data['type_other_municipal_court'] ?: $builder::$placeholder !!}, {!! $data['type_other_land_registry'] ?: $builder::$placeholder !!},
            katastarska općina {!! $data['type_other_cadastral_municipality'] ?: $builder::$placeholder !!}, u zemljišnoknjižni uložak {!! $data['type_other_land_registry_folio_number'] ?: $builder::$placeholder !!}, kako slijedi:
            <ul>
                <li>
                    k. č. br. {!! $data['type_other_possession_number'] ?: $builder::$placeholder !!}, površine {!! StringHelper::getPossessionAreaString($data['type_other_possession_area'], $data['type_other_possession_area_type'] ?? null) !!}, oznake zemljišta {!! $data['type_other_possession_identification'] ?: $builder::$placeholder !!}
                    @if(!empty($data['type_other_additional_possession_area_and_identification']))
                        {{ StringHelper::getAdditionalLandAreaAndIdentificationPrefix($data['type_other_additional_possession_area_and_identification']) }} @foreach($data['type_other_additional_possession_area_and_identification'] as $_i => $_additional) {!! $_additional['identification'] ?: $builder::$placeholder !!} površine {!! StringHelper::getPossessionAreaString($_additional['area'], $_additional['area_type'] ?? null) !!}@if($_i < (count($data['type_other_additional_possession_area_and_identification'])-1))@if(($_i+2) == count($data['type_other_additional_possession_area_and_identification'])) i @else, @endif @else{{ ')' }}@endif @endforeach
                    @endif
                </li>
                @foreach($data['type_other_additional_land_plots'] as $_type_other_additional_land_plot)
                    <li>
                        k. č. br. {!! $_type_other_additional_land_plot['type_other_possession_number'] ?: $builder::$placeholder !!}, površine {!! StringHelper::getPossessionAreaString($_type_other_additional_land_plot['type_other_possession_area'], $_type_other_additional_land_plot['type_other_possession_area_type'] ?? null) !!}, oznake zemljišta {!! $_type_other_additional_land_plot['type_other_possession_identification'] ?: $builder::$placeholder !!}
                        @if(!empty($_type_other_additional_land_plot['type_other_additional_possession_area_and_identification']))
                            {{ StringHelper::getAdditionalLandAreaAndIdentificationPrefix($_type_other_additional_land_plot['type_other_additional_possession_area_and_identification']) }} @foreach($_type_other_additional_land_plot['type_other_additional_possession_area_and_identification'] as $_i => $_additional) {!! $_additional['identification'] ?: $builder::$placeholder !!} površine {!! StringHelper::getPossessionAreaString($_additional['area'], $_additional['area_type'] ?? null) !!}@if($_i < (count($_type_other_additional_land_plot['type_other_additional_possession_area_and_identification'])-1))@if(($_i+2) == count($_type_other_additional_land_plot['type_other_additional_possession_area_and_identification'])) i @else, @endif @else{{ ')' }}@endif @endforeach
                        @endif
                    </li>
                @endforeach
            </ul>
            (u daljnjem tekstu: {{$realestate_index_nominativ}}).
        </p>
    @endif

    @if($data['type_other_has_encumbrances_sheet_data'])
        <p>
            {{ $article_index }}.{{ $singleton->p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da je u teretovnici (listu C) za {{$realestate_index_akuzativ}} izvršen sljedeći upis: <br/> <br/>
            <i>"{!! $data['type_other_encumbrances_sheet_data'] ? nl2br($data['type_other_encumbrances_sheet_data']) : $builder::$placeholder !!}."</i>
        </p>
    @endif

    @if(!empty($data['type_other_additional_realestate_data']))
        <p>
            {{ $article_index }}.{{ $singleton->p_index++ }}.
            {!! nl2br($data['type_other_additional_realestate_data']) !!}
        </p>
    @endif

@endif

@if(empty($additional_beneficiaries) && empty($additional_donors))

    @if(!isset($donor_has_full_realestate_ownership) || $donor_has_full_realestate_ownership)
        @if(!isset($data['beneficiary_acquires_full_ownership_of_donor_realestate']) || $data['beneficiary_acquires_full_ownership_of_donor_realestate'])
            <p>
                {{ $article_index }}.{{ $singleton->p_index++ }}.
                Darovatelj daruje, a Obdarenik sa zahvalnošću prihvaća na dar vlasnički dio 1/1 {{$realestate_index_genitiv}}, čime Obdarenik stječe vlasništvo cijele {{$realestate_index_genitiv}}.
            </p>
        @else
            <p>
                {{ $article_index }}.{{ $singleton->p_index++ }}.
                Darovatelj daruje, a Obdarenik sa zahvalnošću prihvaća na dar suvlasnički dio {!! !empty($data['beneficiary_ownership_of_donor_realestate_ratio_numerator']) ? $data['beneficiary_ownership_of_donor_realestate_ratio_numerator'] : $builder::$placeholder !!}/{!! !empty($data['beneficiary_ownership_of_donor_realestate_ratio_denominator']) ? $data['beneficiary_ownership_of_donor_realestate_ratio_denominator'] : $builder::$placeholder !!} {{$realestate_index_genitiv}},
                čime Obdarenik stječe vlasništvo idealnog dijela {!! !empty($data['beneficiary_ownership_of_donor_realestate_ratio_numerator']) ? $data['beneficiary_ownership_of_donor_realestate_ratio_numerator'] : $builder::$placeholder !!}/{!! !empty($data['beneficiary_ownership_of_donor_realestate_ratio_denominator']) ? $data['beneficiary_ownership_of_donor_realestate_ratio_denominator'] : $builder::$placeholder !!} {{$realestate_index_genitiv}}.
            </p>
        @endif
    @else
        @if(!isset($data['beneficiary_acquires_full_ownership_of_donor_realestate']) || $data['beneficiary_acquires_full_ownership_of_donor_realestate'])
            <p>
                {{ $article_index }}.{{ $singleton->p_index++ }}.
                Darovatelj daruje, a Obdarenik sa zahvalnošću prihvaća na dar suvlasnički dio {{$realestate_index_genitiv}} koji je u vlasništvu Darovatelja u cijelosti,
                čime Obdarenik stječe vlasništvo idealnog dijela {!! !empty($data['donor_realestate_ownership_ratio_numerator']) ? $data['donor_realestate_ownership_ratio_numerator'] : $builder::$placeholder !!}/{!! !empty($data['donor_realestate_ownership_ratio_denominator']) ? $data['donor_realestate_ownership_ratio_denominator'] : $builder::$placeholder !!} {{$realestate_index_genitiv}}.
            </p>
        @else
            <p>
                {{ $article_index }}.{{ $singleton->p_index++ }}.
                Darovatelj daruje, a Obdarenik sa zahvalnošću prihvaća na dar {!! !empty($data['beneficiary_ownership_of_donor_realestate_ratio_numerator']) ? $data['beneficiary_ownership_of_donor_realestate_ratio_numerator'] : $builder::$placeholder !!}/{!! !empty($data['beneficiary_ownership_of_donor_realestate_ratio_denominator']) ? $data['beneficiary_ownership_of_donor_realestate_ratio_denominator'] : $builder::$placeholder !!} suvlasničkog dijela {{$realestate_index_genitiv}} koji je u vlasništvu Darovatelja,
                čime Obdarenik stječe vlasništvo idealnog dijela
                @if(!empty($data['beneficiary_ownership_of_donor_realestate_ratio_numerator']) && !empty($data['beneficiary_ownership_of_donor_realestate_ratio_denominator']) && !empty($data['donor_realestate_ownership_ratio_numerator']) && !empty($data['donor_realestate_ownership_ratio_denominator']))
                    {!! StringHelper::calculateOwnershipRatio($data['beneficiary_ownership_of_donor_realestate_ratio_numerator'], $data['beneficiary_ownership_of_donor_realestate_ratio_denominator'], $data['donor_realestate_ownership_ratio_numerator'], $data['donor_realestate_ownership_ratio_denominator'] ) !!}
                @else
                    {!! $builder::$placeholder !!}/{!! $builder::$placeholder !!}
                @endif
                {{$realestate_index_genitiv}}.
            </p>
        @endif
    @endif

@elseif(!empty($additional_beneficiaries) && empty($additional_donors))

    @if(!isset($donor_has_full_realestate_ownership) || $donor_has_full_realestate_ownership)
        <p>
            {{ $article_index }}.{{ $singleton->p_index++ }}.
            Darovatelj daruje, a Obdarenik
            {!! $beneficiary_name !!}
            sa zahvalnošću prihvaća na dar
            suvlasnički dio {!! !empty($data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][0]) ? $data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][0] : $builder::$placeholder !!}/{!! !empty($data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][0]) ? $data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][0] : $builder::$placeholder !!} {{$realestate_index_genitiv}},
            čime Obdarenik {!! $beneficiary_name !!} stječe vlasništvo idealnog dijela
            {!! !empty($data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][0]) ? $data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][0] : $builder::$placeholder !!}/{!! !empty($data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][0]) ? $data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][0] : $builder::$placeholder !!}
            {{$realestate_index_genitiv}}@if(count($additional_beneficiaries)==1), te @else,@endif
            @foreach($additional_beneficiaries as $_ab_i => $_additional_beneficiary)
                Obdarenik {!! $_additional_beneficiary['name'] ?: $builder::$placeholder !!} sa zahvalnošću prihvaća na dar suvlasnički dio {!! !empty($data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][$_ab_i+1]) ? $data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][$_ab_i+1] : $builder::$placeholder !!}/{!! !empty($data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][$_ab_i+1]) ? $data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][$_ab_i+1] : $builder::$placeholder !!} {{$realestate_index_genitiv}}, čime Obdarenik {!! $_additional_beneficiary['name'] ?: $builder::$placeholder !!} stječe vlasništvo idealnog dijela {!! !empty($data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][$_ab_i+1]) ? $data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][$_ab_i+1] : $builder::$placeholder !!}/{!! !empty($data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][$_ab_i+1]) ? $data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][$_ab_i+1] : $builder::$placeholder !!} {{$realestate_index_genitiv}}{{ StringHelper::countableConjunction(count($additional_beneficiaries), $_ab_i) }}
            @endforeach
        </p>
    @else
        <p>
            {{ $article_index }}.{{ $singleton->p_index++ }}.
            Darovatelj daruje, a Obdarenik
            {!! $beneficiary_name !!}
            sa zahvalnošću prihvaća na dar
            {!! !empty($data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][0]) ? $data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][0] : $builder::$placeholder !!}/{!! !empty($data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][0]) ? $data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][0] : $builder::$placeholder !!}
            suvlasničkog dijela {{$realestate_index_genitiv}} koji je u vlasništvu Darovatelja,
            čime Obdarenik {!! $beneficiary_name !!} stječe vlasništvo idealnog dijela
            @if(!empty($data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][0]) && !empty($data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][0]) && !empty($data['donor_realestate_ownership_ratio_numerator']) && !empty($data['donor_realestate_ownership_ratio_denominator']))
                {!! StringHelper::calculateOwnershipRatio($data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][0], $data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][0], $data['donor_realestate_ownership_ratio_numerator'], $data['donor_realestate_ownership_ratio_denominator'] ) !!}
            @else
                {!! $builder::$placeholder !!}/{!! $builder::$placeholder !!}
            @endif
            {{$realestate_index_genitiv}}@if(count($additional_beneficiaries)==1), te @else, @endif
            @foreach($additional_beneficiaries as $_ab_i => $_additional_beneficiary)
                Obdarenik {!! $_additional_beneficiary['name'] ?: $builder::$placeholder !!} sa zahvalnošću prihvaća na dar {!! !empty($data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][$_ab_i+1]) ? $data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][$_ab_i+1] : $builder::$placeholder !!}/{!! !empty($data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][$_ab_i+1]) ? $data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][$_ab_i+1] : $builder::$placeholder !!} suvlasničkog dijela {{$realestate_index_genitiv}} koji je u vlasništvu Darovatelja,
                čime Obdarenik {!! $_additional_beneficiary['name'] ?: $builder::$placeholder !!} stječe vlasništvo idealnog dijela
                @if(!empty($data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][$_ab_i+1]) && !empty($data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][$_ab_i+1]) && !empty($data['donor_realestate_ownership_ratio_numerator']) && !empty($data['donor_realestate_ownership_ratio_denominator']))
                    {!! StringHelper::calculateOwnershipRatio($data['beneficiaries_ownership_of_donor_realestate_ratio_numerator'][$_ab_i+1], $data['beneficiaries_ownership_of_donor_realestate_ratio_denominator'][$_ab_i+1], $data['donor_realestate_ownership_ratio_numerator'], $data['donor_realestate_ownership_ratio_denominator'] ) !!}
                @else
                    {!! $builder::$placeholder !!}/{!! $builder::$placeholder !!}
                @endif
                {{$realestate_index_genitiv}}{{ StringHelper::countableConjunction(count($additional_beneficiaries), $_ab_i) }}
            @endforeach
        </p>
    @endif

@elseif(empty($additional_beneficiaries) && !empty($additional_donors))

    @if(isset($data['beneficiary_acquires_full_ownership_of_donors_realestate'][0]) && $data['beneficiary_acquires_full_ownership_of_donors_realestate'][0] == 1)
        <p>
            {{ $article_index }}.{{ $singleton->p_index++ }}.
            Darovatelj {!! $donor_name !!} daruje, a Obdarenik sa zahvalnošću prihvaća na dar suvlasnički dio {!! $realestate_index_genitiv !!} koji je u vlasništvu Darovatelja {!! $donor_name !!} u cijelosti,
            čime Obdarenik stječe vlasništvo idealnog dijela {!! !empty($data['donors_realestate_ownership_ratio_numerator'][0]) ? $data['donors_realestate_ownership_ratio_numerator'][0] : $builder::$placeholder !!}/{!! !empty($data['donors_realestate_ownership_ratio_denominator'][0]) ? $data['donors_realestate_ownership_ratio_denominator'][0] : $builder::$placeholder !!} {!! $realestate_index_genitiv !!}.
        </p>
    @elseif(empty($data['beneficiary_acquires_full_ownership_of_donors_realestate'][0]))
        <p>
            {{ $article_index }}.{{ $singleton->p_index++ }}.
            Darovatelj {!! $donor_name !!} daruje, a Obdarenik sa zahvalnošću prihvaća na dar {!! !empty($data['beneficiary_ownership_of_donors_realestate_ratio_numerator'][0]) ? $data['beneficiary_ownership_of_donors_realestate_ratio_numerator'][0] : $builder::$placeholder !!}/{!! !empty($data['beneficiary_ownership_of_donors_realestate_ratio_denominator'][0]) ? $data['beneficiary_ownership_of_donors_realestate_ratio_denominator'][0] : $builder::$placeholder !!} suvlasničkog dijela {!! $realestate_index_genitiv !!} koji je u vlasništvu Darovatelja {!! $donor_name !!},
            čime Obdarenik stječe vlasništvo idealnog dijela
            @if(!empty($data['beneficiary_ownership_of_donors_realestate_ratio_numerator'][0]) && !empty($data['beneficiary_ownership_of_donors_realestate_ratio_denominator'][0]) && !empty($data['donors_realestate_ownership_ratio_numerator'][0]) && !empty($data['donors_realestate_ownership_ratio_denominator'][0]))
                {!! StringHelper::calculateOwnershipRatio($data['beneficiary_ownership_of_donors_realestate_ratio_numerator'][0], $data['beneficiary_ownership_of_donors_realestate_ratio_denominator'][0], $data['donors_realestate_ownership_ratio_numerator'][0], $data['donors_realestate_ownership_ratio_denominator'][0] ) !!}
            @else
                {!! $builder::$placeholder !!}/{!! $builder::$placeholder !!}
            @endif
            {{ $realestate_index_genitiv }}.
        </p>
    @endif

    @foreach($additional_donors as $_ad_i => $_additional_donor)
        @if(isset($data['beneficiary_acquires_full_ownership_of_donors_realestate'][$_ad_i+1]) && $data['beneficiary_acquires_full_ownership_of_donors_realestate'][$_ad_i+1] == 1)
            <p>
                {{ $article_index }}.{{ $singleton->p_index++ }}.
                Darovatelj {!! $_additional_donor['name'] ?: $builder::$placeholder !!} daruje, a Obdarenik sa zahvalnošću prihvaća na dar suvlasnički dio {!! $realestate_index_genitiv !!} koji je u vlasništvu Darovatelja {!! $_additional_donor['name'] ?: $builder::$placeholder !!} u cijelosti,
                čime Obdarenik stječe vlasništvo idealnog dijela {!! !empty($data['donors_realestate_ownership_ratio_numerator'][$_ad_i+1]) ? $data['donors_realestate_ownership_ratio_numerator'][$_ad_i+1] : $builder::$placeholder !!}/{!! !empty($data['donors_realestate_ownership_ratio_denominator'][$_ad_i+1]) ? $data['donors_realestate_ownership_ratio_denominator'][$_ad_i+1] : $builder::$placeholder !!} {!! $realestate_index_genitiv !!}.
            </p>
        @elseif(empty($data['beneficiary_acquires_full_ownership_of_donors_realestate'][$_ad_i+1]))
            <p>
                {{ $article_index }}.{{ $singleton->p_index++ }}.
                Darovatelj {!! $_additional_donor['name'] ?: $builder::$placeholder !!} daruje, a Obdarenik sa zahvalnošću prihvaća na dar {!! !empty($data['beneficiary_ownership_of_donors_realestate_ratio_numerator'][$_ad_i+1]) ? $data['beneficiary_ownership_of_donors_realestate_ratio_numerator'][$_ad_i+1] : $builder::$placeholder !!}/{!! !empty($data['beneficiary_ownership_of_donors_realestate_ratio_denominator'][$_ad_i+1]) ? $data['beneficiary_ownership_of_donors_realestate_ratio_denominator'][$_ad_i+1] : $builder::$placeholder !!} suvlasničkog dijela {!! $realestate_index_genitiv !!} koji je u vlasništvu Darovatelja {!! $_additional_donor['name'] ?: $builder::$placeholder !!},
                čime Obdarenik stječe vlasništvo idealnog dijela
                @if(!empty($data['beneficiary_ownership_of_donors_realestate_ratio_numerator'][$_ad_i+1]) && !empty($data['beneficiary_ownership_of_donors_realestate_ratio_denominator'][$_ad_i+1]) && !empty($data['donors_realestate_ownership_ratio_numerator'][$_ad_i+1]) && !empty($data['donors_realestate_ownership_ratio_denominator'][$_ad_i+1]))
                    {!! StringHelper::calculateOwnershipRatio($data['beneficiary_ownership_of_donors_realestate_ratio_numerator'][$_ad_i+1], $data['beneficiary_ownership_of_donors_realestate_ratio_denominator'][$_ad_i+1], $data['donors_realestate_ownership_ratio_numerator'][$_ad_i+1], $data['donors_realestate_ownership_ratio_denominator'][$_ad_i+1] ) !!}
                @else
                    {!! $builder::$placeholder !!}/{!! $builder::$placeholder !!}
                @endif
                {{ $realestate_index_genitiv }}.
            </p>
        @endif
    @endforeach

@elseif(!empty($additional_beneficiaries) && !empty($additional_donors))

    @if(isset($data['beneficiaries_acquire_full_ownership_of_donors_realestate'][0][0]) && $data['beneficiaries_acquire_full_ownership_of_donors_realestate'][0][0] == 1)
        <p>
            {{ $article_index }}.{{ $singleton->p_index++ }}.
            Darovatelj {!! $donor_name !!} daruje, a Obdarenik {!! $beneficiary_name !!} sa zahvalnošću prihvaća na dar suvlasnički dio {!! $realestate_index_genitiv !!} koji je u vlasništvu Darovatelja {!! $donor_name !!} u cijelosti,
            čime Obdarenik {!! $beneficiary_name !!} stječe vlasništvo idealnog dijela {!! !empty($data['donors_realestate_ownership_ratio_numerator'][0]) ? $data['donors_realestate_ownership_ratio_numerator'][0] : $builder::$placeholder !!}/{!! !empty($data['donors_realestate_ownership_ratio_denominator'][0]) ? $data['donors_realestate_ownership_ratio_denominator'][0] : $builder::$placeholder !!} {!! $realestate_index_genitiv !!}.
        </p>
    @elseif(empty($data['beneficiaries_acquire_full_ownership_of_donors_realestate'][0][0]))
        <p>
            {{ $article_index }}.{{ $singleton->p_index++ }}.
            Darovatelj {!! $donor_name !!} daruje, a Obdarenik {!! $beneficiary_name !!} sa zahvalnošću prihvaća na dar {!! !empty($data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][0][0]) ? $data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][0][0] : $builder::$placeholder !!}/{!! !empty($data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][0][0]) ? $data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][0][0] : $builder::$placeholder !!} suvlasničkog dijela {!! $realestate_index_genitiv !!} koji je u vlasništvu Darovatelja {!! $donor_name !!},
            čime Obdarenik {!! $beneficiary_name !!} stječe vlasništvo idealnog dijela
            @if(!empty($data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][0][0]) && !empty($data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][0][0]) && !empty($data['donors_realestate_ownership_ratio_numerator'][0]) && !empty($data['donors_realestate_ownership_ratio_denominator'][0]))
                {!! StringHelper::calculateOwnershipRatio($data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][0][0], $data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][0][0], $data['donors_realestate_ownership_ratio_numerator'][0], $data['donors_realestate_ownership_ratio_denominator'][0] ) !!}
            @else
                {!! $builder::$placeholder !!}/{!! $builder::$placeholder !!}
            @endif
            {{ $realestate_index_genitiv }}.
        </p>
    @endif

    @foreach($additional_beneficiaries as $_ab_i => $_additional_beneficiary)
        @if(isset($data['beneficiaries_acquire_full_ownership_of_donors_realestate'][$_ab_i+1][0]) && $data['beneficiaries_acquire_full_ownership_of_donors_realestate'][$_ab_i+1][0] == 1)
            <p>
                {{ $article_index }}.{{ $singleton->p_index++ }}.
                Darovatelj {!! $donor_name !!} daruje, a Obdarenik {!! $_additional_beneficiary['name'] ?: $builder::$placeholder !!} sa zahvalnošću prihvaća na dar suvlasnički dio {!! $realestate_index_genitiv !!} koji je u vlasništvu Darovatelja {!! $donor_name !!} u cijelosti,
                čime Obdarenik {!! $_additional_beneficiary['name'] ?: $builder::$placeholder !!} stječe vlasništvo idealnog dijela {!! !empty($data['donors_realestate_ownership_ratio_numerator'][0]) ? $data['donors_realestate_ownership_ratio_numerator'][0] : $builder::$placeholder !!}/{!! !empty($data['donors_realestate_ownership_ratio_denominator'][0]) ? $data['donors_realestate_ownership_ratio_denominator'][0] : $builder::$placeholder !!} {!! $realestate_index_genitiv !!}.
            </p>
        @elseif(empty($data['beneficiaries_acquire_full_ownership_of_donors_realestate'][$_ab_i+1][0]))
            <p>
                {{ $article_index }}.{{ $singleton->p_index++ }}.
                Darovatelj {!! $donor_name !!} daruje, a Obdarenik {!! $_additional_beneficiary['name'] ?: $builder::$placeholder !!} sa zahvalnošću prihvaća na dar {!! !empty($data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][$_ab_i+1][0]) ? $data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][$_ab_i+1][0] : $builder::$placeholder !!}/{!! !empty($data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][$_ab_i+1][0]) ? $data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][$_ab_i+1][0] : $builder::$placeholder !!} suvlasničkog dijela {!! $realestate_index_genitiv !!} koji je u vlasništvu Darovatelja {!! $donor_name !!},
                čime Obdarenik {!! $_additional_beneficiary['name'] ?: $builder::$placeholder !!} stječe vlasništvo idealnog dijela
                @if(!empty($data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][$_ab_i+1][0]) && !empty($data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][$_ab_i+1][0]) && !empty($data['donors_realestate_ownership_ratio_numerator'][0]) && !empty($data['donors_realestate_ownership_ratio_denominator'][0]))
                    {!! StringHelper::calculateOwnershipRatio($data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][$_ab_i+1][0], $data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][$_ab_i+1][0], $data['donors_realestate_ownership_ratio_numerator'][0], $data['donors_realestate_ownership_ratio_denominator'][0] ) !!}
                @else
                    {!! $builder::$placeholder !!}/{!! $builder::$placeholder !!}
                @endif
                {{ $realestate_index_genitiv }}.
            </p>
        @endif
    @endforeach

    @foreach($additional_donors as $_ad_i => $_additional_donor)

        @if(isset($data['beneficiaries_acquire_full_ownership_of_donors_realestate'][0][$_ad_i+1]) && $data['beneficiaries_acquire_full_ownership_of_donors_realestate'][0][$_ad_i+1] == 1)
            <p>
                {{ $article_index }}.{{ $singleton->p_index++ }}.
                Darovatelj {!! $_additional_donor['name'] ?: $builder::$placeholder !!} daruje, a Obdarenik {!! $beneficiary_name !!} sa zahvalnošću prihvaća na dar suvlasnički dio {!! $realestate_index_genitiv !!} koji je u vlasništvu Darovatelja {!! $_additional_donor['name'] ?: $builder::$placeholder !!} u cijelosti,
                čime Obdarenik {!! $beneficiary_name !!} stječe vlasništvo idealnog dijela {!! !empty($data['donors_realestate_ownership_ratio_numerator'][$_ad_i+1]) ? $data['donors_realestate_ownership_ratio_numerator'][$_ad_i+1] : $builder::$placeholder !!}/{!! !empty($data['donors_realestate_ownership_ratio_denominator'][$_ad_i+1]) ? $data['donors_realestate_ownership_ratio_denominator'][$_ad_i+1] : $builder::$placeholder !!} {!! $realestate_index_genitiv !!}.
            </p>
        @elseif(empty($data['beneficiaries_acquire_full_ownership_of_donors_realestate'][0][$_ad_i+1]))
            <p>
                {{ $article_index }}.{{ $singleton->p_index++ }}.
                Darovatelj {!! $_additional_donor['name'] ?: $builder::$placeholder !!} daruje, a Obdarenik {!! $beneficiary_name !!} sa zahvalnošću prihvaća na dar {!! !empty($data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][0][$_ad_i+1]) ? $data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][0][$_ad_i+1] : $builder::$placeholder !!}/{!! !empty($data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][0][$_ad_i+1]) ? $data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][0][$_ad_i+1] : $builder::$placeholder !!} suvlasničkog dijela {!! $realestate_index_genitiv !!} koji je u vlasništvu Darovatelja {!! $_additional_donor['name'] ?: $builder::$placeholder !!},
                čime Obdarenik {!! $beneficiary_name !!} stječe vlasništvo idealnog dijela
                @if(!empty($data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][0][$_ad_i+1]) && !empty($data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][0][$_ad_i+1]) && !empty($data['donors_realestate_ownership_ratio_numerator'][$_ad_i+1]) && !empty($data['donors_realestate_ownership_ratio_denominator'][$_ad_i+1]))
                    {!! StringHelper::calculateOwnershipRatio($data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][0][$_ad_i+1], $data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][0][$_ad_i+1], $data['donors_realestate_ownership_ratio_numerator'][$_ad_i+1], $data['donors_realestate_ownership_ratio_denominator'][$_ad_i+1] ) !!}
                @else
                    {!! $builder::$placeholder !!}/{!! $builder::$placeholder !!}
                @endif
                {{ $realestate_index_genitiv }}.
            </p>
        @endif

        @foreach($additional_beneficiaries as $_ab_i => $_additional_beneficiary)
            @if(isset($data['beneficiaries_acquire_full_ownership_of_donors_realestate'][$_ab_i+1][$_ad_i+1]) && $data['beneficiaries_acquire_full_ownership_of_donors_realestate'][$_ab_i+1][$_ad_i+1] == 1)
                <p>
                    {{ $article_index }}.{{ $singleton->p_index++ }}.
                    Darovatelj {!! $_additional_donor['name'] ?: $builder::$placeholder !!} daruje, a Obdarenik {!! $_additional_beneficiary['name'] ?: $builder::$placeholder !!} sa zahvalnošću prihvaća na dar suvlasnički dio {!! $realestate_index_genitiv !!} koji je u vlasništvu Darovatelja {!! $_additional_donor['name'] ?: $builder::$placeholder !!} u cijelosti,
                    čime Obdarenik {!! $_additional_beneficiary['name'] ?: $builder::$placeholder !!} stječe vlasništvo idealnog dijela {!! !empty($data['donors_realestate_ownership_ratio_numerator'][$_ad_i+1]) ? $data['donors_realestate_ownership_ratio_numerator'][$_ad_i+1] : $builder::$placeholder !!}/{!! !empty($data['donors_realestate_ownership_ratio_denominator'][$_ad_i+1]) ? $data['donors_realestate_ownership_ratio_denominator'][$_ad_i+1] : $builder::$placeholder !!} {!! $realestate_index_genitiv !!}.
                </p>
            @elseif(empty($data['beneficiaries_acquire_full_ownership_of_donors_realestate'][$_ab_i+1][$_ad_i+1]))
                <p>
                    {{ $article_index }}.{{ $singleton->p_index++ }}.
                    Darovatelj {!! $_additional_donor['name'] ?: $builder::$placeholder !!} daruje, a Obdarenik {!! $_additional_beneficiary['name'] ?: $builder::$placeholder !!} sa zahvalnošću prihvaća na dar {!! !empty($data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][$_ab_i+1][$_ad_i+1]) ? $data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][$_ab_i+1][$_ad_i+1] : $builder::$placeholder !!}/{!! !empty($data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][$_ab_i+1][$_ad_i+1]) ? $data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][$_ab_i+1][$_ad_i+1] : $builder::$placeholder !!} suvlasničkog dijela {!! $realestate_index_genitiv !!} koji je u vlasništvu Darovatelja {!! $_additional_donor['name'] ?: $builder::$placeholder !!},
                    čime Obdarenik {!! $_additional_beneficiary['name'] ?: $builder::$placeholder !!} stječe vlasništvo idealnog dijela
                    @if(!empty($data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][$_ab_i+1][$_ad_i+1]) && !empty($data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][$_ab_i+1][$_ad_i+1]) && !empty($data['donors_realestate_ownership_ratio_numerator'][$_ad_i+1]) && !empty($data['donors_realestate_ownership_ratio_denominator'][$_ad_i+1]))
                        {!! StringHelper::calculateOwnershipRatio($data['beneficiaries_ownership_of_donors_realestate_ratio_numerator'][$_ab_i+1][$_ad_i+1], $data['beneficiaries_ownership_of_donors_realestate_ratio_denominator'][$_ab_i+1][$_ad_i+1], $data['donors_realestate_ownership_ratio_numerator'][$_ad_i+1], $data['donors_realestate_ownership_ratio_denominator'][$_ad_i+1] ) !!}
                    @else
                        {!! $builder::$placeholder !!}/{!! $builder::$placeholder !!}
                    @endif
                    {{ $realestate_index_genitiv }}.
                </p>
            @endif
        @endforeach


    @endforeach

@endif
