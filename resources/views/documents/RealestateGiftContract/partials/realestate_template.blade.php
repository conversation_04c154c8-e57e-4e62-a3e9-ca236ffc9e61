<div id="realestate_template" class="d-none">
    <div class="realestate_content">
        <div class="card mb-4">
            <div class="card-header">
                Nekretnina <span class="realestate_index">{REALESTATE_INDEX}</span>
                <strong><a class="btn btn-danger btn-sm float-right remove_realestate"><i
                                class="fa fa-trash"></i> Ukloni</a></strong>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="form-group col-lg-12">
                        {{  Form::fLabel($model, 'additional_realestates[{INDEX}][realestate_type]', '1. Koja vrsta nekretnine je predmet darovanja?')  }}
                        {{  Form::fRadio($model, 'additional_realestates[{INDEX}][realestate_type]', 'particular', 'Posebni dio nekretnine (etažno vlasništvo) upisan u glavnu zemljišnu knjigu (npr. stan, poslovni prostor, parkirno mje<PERSON> i slično)', ['class' => 'form-check-input realestate_type_particular', 'checked' => true])  }}
                        {{  Form::fRadio($model, 'additional_realestates[{INDEX}][realestate_type]', 'flat', 'Stan upisan u knjigu položenih ugovora', ['class' => 'form-check-input realestate_type_flat'])  }}
                        {{  Form::fRadio($model, 'additional_realestates[{INDEX}][realestate_type]', 'other', 'Druga vrsta nekretnine upisana u glavnu zemljišnu knjigu (npr. zgrada, kuća, livada, šuma i slično)', ['class' => 'form-check-input realestate_type_other'])  }}
                    </div>
                </div>

                <div class="realestate_type_particular_container">
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'additional_realestates[{INDEX}][type_particular_municipal_court]', '2. Upiši općinski sud i zemljišnoknjižni odjel koji vodi zemljišnu knjigu u koju je upisana nekretnina', 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/particular/2.jpeg') }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fDropdown($model, 'additional_realestates[{INDEX}][type_particular_municipal_court]', null, [null=>null, 'Općinski sud u Bjelovaru' => 'Općinski sud u Bjelovaru', 'Općinski sud u Crikvenici' => 'Općinski sud u Crikvenici', 'Općinski sud u Čakovcu' => 'Općinski sud u Čakovcu', 'Općinski sud u Dubrovniku' => 'Općinski sud u Dubrovniku', 'Općinski sud u Đakovu' => 'Općinski sud u Đakovu', 'Općinski sud u Gospiću' => 'Općinski sud u Gospiću', 'Općinski sud u Karlovcu' => 'Općinski sud u Karlovcu', 'Općinski sud u Koprivnici' => 'Općinski sud u Koprivnici', 'Općinski sud u Kutini' => 'Općinski sud u Kutini', 'Općinski sud u Makarskoj' => 'Općinski sud u Makarskoj', 'Općinski sud u Metkoviću' => 'Općinski sud u Metkoviću', 'Općinski sud u Novom Zagrebu' => 'Općinski sud u Novom Zagrebu', 'Općinski sud u Osijeku' => 'Općinski sud u Osijeku', 'Općinski sud u Pazinu' => 'Općinski sud u Pazinu', 'Općinski sud u Požegi' => 'Općinski sud u Požegi', 'Općinski sud u Puli-Pola' => 'Općinski sud u Puli-Pola', 'Općinski sud u Rijeci' => 'Općinski sud u Rijeci', 'Općinski sud u Sesvetama' => 'Općinski sud u Sesvetama', 'Općinski sud u Sisku' => 'Općinski sud u Sisku', 'Općinski sud u Slavonskom Brodu' => 'Općinski sud u Slavonskom Brodu', 'Općinski sud u Splitu' => 'Općinski sud u Splitu', 'Općinski sud u Šibeniku' => 'Općinski sud u Šibeniku', 'Općinski sud u Varaždinu' => 'Općinski sud u Varaždinu', 'Općinski sud u Velikoj Gorici' => 'Općinski sud u Velikoj Gorici', 'Općinski sud u Vinkovcima' => 'Općinski sud u Vinkovcima', 'Općinski sud u Virovitici' => 'Općinski sud u Virovitici', 'Općinski sud u Vukovaru' => 'Općinski sud u Vukovaru', 'Općinski sud u Zadru' => 'Općinski sud u Zadru', 'Općinski građanski sud u Zagrebu' => 'Općinski građanski sud u Zagrebu', 'Općinski sud u Zlataru' => 'Općinski sud u Zlataru'], 'Općinski sud', ['class' => 'form-control court-select2']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fDropdown($model, 'additional_realestates[{INDEX}][type_particular_land_registry]', null, [null=>null, 'Zemljišnoknjižni odjel Beli Manastir' => 'Zemljišnoknjižni odjel Beli Manastir', 'Zemljišnoknjižni odjel Benkovac' => 'Zemljišnoknjižni odjel Benkovac', 'Zemljišnoknjižni odjel Biograd na Moru' => 'Zemljišnoknjižni odjel Biograd na Moru', 'Zemljišnoknjižni odjel Bjelovar' => 'Zemljišnoknjižni odjel Bjelovar', 'Zemljišnoknjižni odjel Blato' => 'Zemljišnoknjižni odjel Blato', 'Zemljišnoknjižni odjel Buje-Buie' => 'Zemljišnoknjižni odjel Buje-Buie', 'Zemljišnoknjižni odjel Buzet' => 'Zemljišnoknjižni odjel Buzet', 'Zemljišnoknjižni odjel Crikvenica' => 'Zemljišnoknjižni odjel Crikvenica', 'Zemljišnoknjižni odjel Čabar' => 'Zemljišnoknjižni odjel Čabar', 'Zemljišnoknjižni odjel Čakovec' => 'Zemljišnoknjižni odjel Čakovec', 'Zemljišnoknjižni odjel Čazma' => 'Zemljišnoknjižni odjel Čazma', 'Zemljišnoknjižni odjel Daruvar' => 'Zemljišnoknjižni odjel Daruvar', 'Zemljišnoknjižni odjel Delnice' => 'Zemljišnoknjižni odjel Delnice', 'Zemljišnoknjižni odjel Donja Stubica' => 'Zemljišnoknjižni odjel Donja Stubica', 'Zemljišnoknjižni odjel Donji Lapac' => 'Zemljišnoknjižni odjel Donji Lapac', 'Zemljišnoknjižni odjel Donji Miholjac' => 'Zemljišnoknjižni odjel Donji Miholjac', 'Zemljišnoknjižni odjel Drniš' => 'Zemljišnoknjižni odjel Drniš', 'Zemljišnoknjižni odjel Dubrovnik' => 'Zemljišnoknjižni odjel Dubrovnik', 'Zemljišnoknjižni odjel Dugo Selo' => 'Zemljišnoknjižni odjel Dugo Selo', 'Zemljišnoknjižni odjel Dvor' => 'Zemljišnoknjižni odjel Dvor', 'Zemljišnoknjižni odjel Đakovo' => 'Zemljišnoknjižni odjel Đakovo', 'Zemljišnoknjižni odjel Đurđevac' => 'Zemljišnoknjižni odjel Đurđevac', 'Zemljišnoknjižni odjel Garešnica' => 'Zemljišnoknjižni odjel Garešnica', 'Zemljišnoknjižni odjel Glina' => 'Zemljišnoknjižni odjel Glina', 'Zemljišnoknjižni odjel Gospić' => 'Zemljišnoknjižni odjel Gospić', 'Zemljišnoknjižni odjel Gračac' => 'Zemljišnoknjižni odjel Gračac', 'Zemljišnoknjižni odjel Gvozd' => 'Zemljišnoknjižni odjel Gvozd', 'Zemljišnoknjižni odjel Hrvatska Kostajnica' => 'Zemljišnoknjižni odjel Hrvatska Kostajnica', 'Zemljišnoknjižni odjel Ilok' => 'Zemljišnoknjižni odjel Ilok', 'Zemljišnoknjižni odjel Imotski' => 'Zemljišnoknjižni odjel Imotski', 'Zemljišnoknjižni odjel Ivanec' => 'Zemljišnoknjižni odjel Ivanec', 'Zemljišnoknjižni odjel Ivanić Grad' => 'Zemljišnoknjižni odjel Ivanić Grad', 'Zemljišnoknjižni odjel Jastrebarsko' => 'Zemljišnoknjižni odjel Jastrebarsko', 'Zemljišnoknjižni odjel Karlovac' => 'Zemljišnoknjižni odjel Karlovac', 'Zemljišnoknjižni odjel Kaštel Lukšić' => 'Zemljišnoknjižni odjel Kaštel Lukšić', 'Zemljišnoknjižni odjel Klanjec' => 'Zemljišnoknjižni odjel Klanjec', 'Zemljišnoknjižni odjel Knin' => 'Zemljišnoknjižni odjel Knin', 'Zemljišnoknjižni odjel Koprivnica' => 'Zemljišnoknjižni odjel Koprivnica', 'Zemljišnoknjižni odjel Korčula' => 'Zemljišnoknjižni odjel Korčula', 'Zemljišnoknjižni odjel Korenica' => 'Zemljišnoknjižni odjel Korenica', 'Zemljišnoknjižni odjel Krapina' => 'Zemljišnoknjižni odjel Krapina', 'Zemljišnoknjižni odjel Križevci' => 'Zemljišnoknjižni odjel Križevci', 'Zemljišnoknjižni odjel Krk' => 'Zemljišnoknjižni odjel Krk', 'Zemljišnoknjižni odjel Kutina' => 'Zemljišnoknjižni odjel Kutina', 'Zemljišnoknjižni odjel Labin' => 'Zemljišnoknjižni odjel Labin', 'Zemljišnoknjižni odjel Ludbreg' => 'Zemljišnoknjižni odjel Ludbreg', 'Zemljišnoknjižni odjel Makarska' => 'Zemljišnoknjižni odjel Makarska', 'Zemljišnoknjižni odjel Mali Lošinj' => 'Zemljišnoknjižni odjel Mali Lošinj', 'Zemljišnoknjižni odjel Metković' => 'Zemljišnoknjižni odjel Metković', 'Zemljišnoknjižni odjel Našice' => 'Zemljišnoknjižni odjel Našice', 'Zemljišnoknjižni odjel Nova Gradiška' => 'Zemljišnoknjižni odjel Nova Gradiška', 'Zemljišnoknjižni odjel Novi Marof' => 'Zemljišnoknjižni odjel Novi Marof', 'Zemljišnoknjižni odjel Novi Vinodolski' => 'Zemljišnoknjižni odjel Novi Vinodolski', 'Zemljišnoknjižni odjel Novi Zagreb' => 'Zemljišnoknjižni odjel Novi Zagreb', 'Zemljišnoknjižni odjel Novska' => 'Zemljišnoknjižni odjel Novska', 'Zemljišnoknjižni odjel Obrovac' => 'Zemljišnoknjižni odjel Obrovac', 'Zemljišnoknjižni odjel Ogulin' => 'Zemljišnoknjižni odjel Ogulin', 'Zemljišnoknjižni odjel Omiš' => 'Zemljišnoknjižni odjel Omiš', 'Zemljišnoknjižni odjel Opatija' => 'Zemljišnoknjižni odjel Opatija', 'Zemljišnoknjižni odjel Orahovica' => 'Zemljišnoknjižni odjel Orahovica', 'Zemljišnoknjižni odjel Osijek' => 'Zemljišnoknjižni odjel Osijek', 'Zemljišnoknjižni odjel Otočac' => 'Zemljišnoknjižni odjel Otočac', 'Zemljišnoknjižni odjel Otok' => 'Zemljišnoknjižni odjel Otok', 'Zemljišnoknjižni odjel Ozalj' => 'Zemljišnoknjižni odjel Ozalj', 'Zemljišnoknjižni odjel Pag' => 'Zemljišnoknjižni odjel Pag', 'Zemljišnoknjižni odjel Pakrac' => 'Zemljišnoknjižni odjel Pakrac', 'Zemljišnoknjižni odjel Pazin' => 'Zemljišnoknjižni odjel Pazin', 'Zemljišnoknjižni odjel Petrinja' => 'Zemljišnoknjižni odjel Petrinja', 'Zemljišnoknjižni odjel Pitomača' => 'Zemljišnoknjižni odjel Pitomača', 'Zemljišnoknjižni odjel Ploče' => 'Zemljišnoknjižni odjel Ploče', 'Zemljišnoknjižni odjel Poreč-Parenzo' => 'Zemljišnoknjižni odjel Poreč-Parenzo', 'Zemljišnoknjižni odjel Požega' => 'Zemljišnoknjižni odjel Požega', 'Zemljišnoknjižni odjel Pregrada' => 'Zemljišnoknjižni odjel Pregrada', 'Zemljišnoknjižni odjel Prelog' => 'Zemljišnoknjižni odjel Prelog', 'Zemljišnoknjižni odjel Pula' => 'Zemljišnoknjižni odjel Pula', 'Zemljišnoknjižni odjel Rab' => 'Zemljišnoknjižni odjel Rab', 'Zemljišnoknjižni odjel Rijeka' => 'Zemljišnoknjižni odjel Rijeka', 'Zemljišnoknjižni odjel Rovinj-Rovigno' => 'Zemljišnoknjižni odjel Rovinj-Rovigno', 'Zemljišnoknjižni odjel Samobor' => 'Zemljišnoknjižni odjel Samobor', 'Zemljišnoknjižni odjel Senj' => 'Zemljišnoknjižni odjel Senj', 'Zemljišnoknjižni odjel Sesvete' => 'Zemljišnoknjižni odjel Sesvete', 'Zemljišnoknjižni odjel Sinj' => 'Zemljišnoknjižni odjel Sinj', 'Zemljišnoknjižni odjel Sisak' => 'Zemljišnoknjižni odjel Sisak', 'Zemljišnoknjižni odjel Slatina' => 'Zemljišnoknjižni odjel Slatina', 'Zemljišnoknjižni odjel Slavonski Brod' => 'Zemljišnoknjižni odjel Slavonski Brod', 'Zemljišnoknjižni odjel Slunj' => 'Zemljišnoknjižni odjel Slunj', 'Zemljišnoknjižni odjel Solin' => 'Zemljišnoknjižni odjel Solin', 'Zemljišnoknjižni odjel Split' => 'Zemljišnoknjižni odjel Split', 'Zemljišnoknjižni odjel Stari Grad' => 'Zemljišnoknjižni odjel Stari Grad', 'Zemljišnoknjižni odjel Supetar' => 'Zemljišnoknjižni odjel Supetar', 'Zemljišnoknjižni odjel Sveti Ivan Zelina' => 'Zemljišnoknjižni odjel Sveti Ivan Zelina', 'Zemljišnoknjižni odjel Šibenik' => 'Zemljišnoknjižni odjel Šibenik', 'Zemljišnoknjižni odjel Tisno' => 'Zemljišnoknjižni odjel Tisno', 'Zemljišnoknjižni odjel Trogir' => 'Zemljišnoknjižni odjel Trogir', 'Zemljišnoknjižni odjel Valpovo' => 'Zemljišnoknjižni odjel Valpovo', 'Zemljišnoknjižni odjel Varaždin' => 'Zemljišnoknjižni odjel Varaždin', 'Zemljišnoknjižni odjel Velika Gorica' => 'Zemljišnoknjižni odjel Velika Gorica', 'Zemljišnoknjižni odjel Vinkovci' => 'Zemljišnoknjižni odjel Vinkovci', 'Zemljišnoknjižni odjel Virovitica' => 'Zemljišnoknjižni odjel Virovitica', 'Zemljišnoknjižni odjel Vojnić' => 'Zemljišnoknjižni odjel Vojnić', 'Zemljišnoknjižni odjel Vrbovec' => 'Zemljišnoknjižni odjel Vrbovec', 'Zemljišnoknjižni odjel Vrbovsko' => 'Zemljišnoknjižni odjel Vrbovsko', 'Zemljišnoknjižni odjel Vukovar' => 'Zemljišnoknjižni odjel Vukovar', 'Zemljišnoknjižni odjel Zabok' => 'Zemljišnoknjižni odjel Zabok', 'Zemljišnoknjižni odjel Zadar' => 'Zemljišnoknjižni odjel Zadar', 'Zemljišnoknjižni odjel Zagreb' => 'Zemljišnoknjižni odjel Zagreb', 'Zemljišnoknjižni odjel Zaprešić' => 'Zemljišnoknjižni odjel Zaprešić', 'Zemljišnoknjižni odjel Zlatar' => 'Zemljišnoknjižni odjel Zlatar', 'Zemljišnoknjižni odjel Županja' => 'Zemljišnoknjižni odjel Županja'], 'Zemljišnoknjižni odjel', ['class' => 'form-control landRegistry-select2']) }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fText($model, 'additional_realestates[{INDEX}][type_particular_cadastral_municipality]', null, '3. Upiši oznaku i naziv katastarske općine u kojoj se nekretnina nalazi', ['placeholder' => 'Npr: 999901, GRAD ZAGREB'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/particular/3.jpeg') }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fText($model, 'additional_realestates[{INDEX}][type_particular_land_registry_folio_number]', null, '4. Upiši broj zemljišnoknjižnog uloška u koji su upisani podaci vezani uz nekretninu', ['placeholder' => 'Npr: 12345'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/particular/4.jpeg') }}
                        </div>
                    </div>
                    <hr/>

                    <div class="mb-3">
                        <div class="card">
                            <div class="card-header">
                                Katastarska čestica
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fLabel($model, 'additional_realestates[{INDEX}][type_particular_possession_sheet_data]', 'Upiši podatke iz posjedovnice (lista A) koji se odnose na katastarsku česticu odnosno zemljište') }}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-lg-6">
                                        {{ Form::fText($model, 'additional_realestates[{INDEX}][type_particular_possession_number]', null, 'Broj zemljišta/katastarske čestice', ['placeholder' => 'Npr: 1234/1'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/particular/5A.jpeg') }}
                                    </div>
                                    <div class="form-group col-lg-6">
                                        {{ Form::fDropdownText($model ,'Površina zemljišta', 'additional_realestates[{INDEX}][type_particular_possession_area]', null, 'additional_realestates[{INDEX}][type_particular_possession_area_type]', null, ["m2" => "m2", "čvh" => "čvh", "jutro" => "jutro"], ['placeholder' => 'Npr: 1100'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/particular/5C.jpeg', 'Pazi da ovdje ne upišeš površinu posebnog dijela nekretnine, nego površinu cijele katastarske čestice! Primjerice, ako se radi o prodaji stana u etažiranoj stambenoj zgradi, ovdje ne upisuješ površinu stana, nego površinu cjelokupne katastarske čestice na kojoj je etažirana stambena zgrada izgrađena.') }}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12">
                                        {{ Form::fTextArea($model, 'additional_realestates[{INDEX}][type_particular_possession_identification]', null, 'Oznaka zemljišta', ['rows' => 2, 'placeholder' => 'Npr: KUĆA BROJ 1 I DVORIŠTE U RIJEČKOJ ULICI'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/particular/5B.jpeg') }}
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="type_particular_additional_land_plot_container">
                    </div>

                    <a data-field-name="additional_realestates[{INDEX}][type_particular_additional_land_plots]" class="btn btn-info btn-block add_type_particular_additional_land_plot">Dodaj katastarsku česticu</a>
                    <div class="button-helper text-center">
                        <a href="/documents/RealestateGiftContract/tooltips/other/help-2.jpeg" data-caption='Ponekad je u istom zemljišnoknjižnom ulošku upisano više zemljišta odnosno katastarskih čestica. Ako je više katastarskih čestica iz istog zemljišnoknjižnog uloška predmet ugovora o darovanju nekretnine, za upis tih katastarskih čestica u ugovor klikni na "Dodaj katastarsku česticu".' class="fancybox"><i class="fa fa-question-circle"></i> Objašnjenje</a>
                    </div>

                    <hr/>
                    <div class="row">
                        <div class="col-lg-12 form-group">
                            {{ Form::fTextArea($model, 'additional_realestates[{INDEX}][type_particular_ownership_sheet_data]', null, '5. Upiši podatke iz vlastovnice (lista B) koji se odnose na posebni dio nekretnine (etažno vlasništvo)', ['placeholder' => 'Npr: 11. Suvlasnički dio: 111/11111 ETAŽNO VLASNIŠTVO (E-11), stan broj 11 - STAMBENA JEDINICA "D" (u elaboratu označen ružičastom bojom) - trosobni stan u prizemlju (sa stubišta ulaz desno) ukupne neto korisne površine 111,11 čm'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/particular/6.jpeg', 'Ovdje upisuješ samo oznaku i opis posebnog dijela nekretnine (etaže) iz izvatka iz zemljišne knjige! Pazi da ovdje ne upišeš podatke o vlasniku (ili suvlasnicima) posebnog dijela nekretnine. Za upis podataka o vlasniku (ili suvlasnicima) predviđena su posebna polja na kraju ovog koraka upitnika.') }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="col-lg-12">
                            {{ Form::fLabel($model, 'additional_realestates[{INDEX}][type_particular_has_encumbrances_sheet_data]', '6. Želiš li u ugovor upisati podatke iz teretovnice (lista C) o teretima koji su upisani na posebnom dijelu nekretnine?') }}
                            {{ Form::fRadio($model, 'additional_realestates[{INDEX}][type_particular_has_encumbrances_sheet_data]', 0, 'Ne', ['class' => 'form-check-input type_particular_has_not_encumbrances_sheet_data', 'checked' => true]) }}
                            {{ Form::fRadio($model, 'additional_realestates[{INDEX}][type_particular_has_encumbrances_sheet_data]', 1, 'Da', ['class' => 'form-check-input type_particular_has_encumbrances_sheet_data',]) }}
                        </div>
                    </div>
                    <div class="type_particular_encumbrances_sheet_data_container" style="display:none;">
                        <hr/>
                        <div class="row">
                            <div class="col-lg-12">
                                {{ Form::fTextArea($model, 'additional_realestates[{INDEX}][type_particular_encumbrances_sheet_data]', null, '<span class="dot"></span> Upiši podatke iz teretovnice (lista C) o teretima koji su upisani na posebnom dijelu nekretnine', ['placeholder' => 'Npr: Na suvlasnički dio: 11 (111/11111), 1.1 Primljeno: 11. studenog 2011. Z-1111/11. Na temelju ugovora o namjenskom kreditu broj 11/11 od 11. listopada 2011. soleminiziranog istog dana po javnom bilježniku Apoloniju Treperoviću pod brojem OU - 111/11., uknjižuje se pravo zaloga za kredit u iznosu od 111.111,00 €, uz kamatu koja je godišnje promjenjiva, a na dan zaključenja Ugovora iznosi 5,11%, s rokom otplate od 11 godina, te prema ostalim uvjetima iz Ugovora za korist: PRVA BANKA D.D., Glavni trg 11, Zagreb'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/particular/7DA.jpeg') }}
                            </div>
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="col-lg-12 form-group">
                            {{ Form::fTextArea($model, 'additional_realestates[{INDEX}][type_particular_additional_realestate_data]', null, '7. Ovdje možeš upisati dodatne podatke o nekretnini koje mi nismo predvidjeli <small>(opcionalno)</small>', ['placeholder' => 'Npr: Na dan potpisivanja ovog Ugovora, na zemljišnoknjižnom ulošku u koji je upisana nekretnina postoji aktivna plomba koja se odnosi na...', 'data-force-start-case' => 'upper']) }}
                        </div>
                    </div>

                </div>

                <div class="realestate_type_flat_container" style="display:none;">
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, "additional_realestates[{INDEX}][type_flat_municipal_court]", '2. Upiši općinski sud i zemljišnoknjižni odjel koji vodi knjigu položenih ugovora u koju je upisan stan', 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/flat/2.jpeg') }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fDropdown($model, 'additional_realestates[{INDEX}][type_flat_municipal_court]', null, [null=>null, 'Općinski sud u Bjelovaru' => 'Općinski sud u Bjelovaru', 'Općinski sud u Crikvenici' => 'Općinski sud u Crikvenici', 'Općinski sud u Čakovcu' => 'Općinski sud u Čakovcu', 'Općinski sud u Dubrovniku' => 'Općinski sud u Dubrovniku', 'Općinski sud u Đakovu' => 'Općinski sud u Đakovu', 'Općinski sud u Gospiću' => 'Općinski sud u Gospiću', 'Općinski sud u Karlovcu' => 'Općinski sud u Karlovcu', 'Općinski sud u Koprivnici' => 'Općinski sud u Koprivnici', 'Općinski sud u Kutini' => 'Općinski sud u Kutini', 'Općinski sud u Makarskoj' => 'Općinski sud u Makarskoj', 'Općinski sud u Metkoviću' => 'Općinski sud u Metkoviću', 'Općinski sud u Novom Zagrebu' => 'Općinski sud u Novom Zagrebu', 'Općinski sud u Osijeku' => 'Općinski sud u Osijeku', 'Općinski sud u Pazinu' => 'Općinski sud u Pazinu', 'Općinski sud u Požegi' => 'Općinski sud u Požegi', 'Općinski sud u Puli-Pola' => 'Općinski sud u Puli-Pola', 'Općinski sud u Rijeci' => 'Općinski sud u Rijeci', 'Općinski sud u Sesvetama' => 'Općinski sud u Sesvetama', 'Općinski sud u Sisku' => 'Općinski sud u Sisku', 'Općinski sud u Slavonskom Brodu' => 'Općinski sud u Slavonskom Brodu', 'Općinski sud u Splitu' => 'Općinski sud u Splitu', 'Općinski sud u Šibeniku' => 'Općinski sud u Šibeniku', 'Općinski sud u Varaždinu' => 'Općinski sud u Varaždinu', 'Općinski sud u Velikoj Gorici' => 'Općinski sud u Velikoj Gorici', 'Općinski sud u Vinkovcima' => 'Općinski sud u Vinkovcima', 'Općinski sud u Virovitici' => 'Općinski sud u Virovitici', 'Općinski sud u Vukovaru' => 'Općinski sud u Vukovaru', 'Općinski sud u Zadru' => 'Općinski sud u Zadru', 'Općinski građanski sud u Zagrebu' => 'Općinski građanski sud u Zagrebu', 'Općinski sud u Zlataru' => 'Općinski sud u Zlataru'], 'Općinski sud', ['class' => 'form-control court-select2']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fDropdown($model, 'additional_realestates[{INDEX}][type_flat_land_registry]', null, [null=>null, 'Zemljišnoknjižni odjel Beli Manastir' => 'Zemljišnoknjižni odjel Beli Manastir', 'Zemljišnoknjižni odjel Benkovac' => 'Zemljišnoknjižni odjel Benkovac', 'Zemljišnoknjižni odjel Biograd na Moru' => 'Zemljišnoknjižni odjel Biograd na Moru', 'Zemljišnoknjižni odjel Bjelovar' => 'Zemljišnoknjižni odjel Bjelovar', 'Zemljišnoknjižni odjel Blato' => 'Zemljišnoknjižni odjel Blato', 'Zemljišnoknjižni odjel Buje-Buie' => 'Zemljišnoknjižni odjel Buje-Buie', 'Zemljišnoknjižni odjel Buzet' => 'Zemljišnoknjižni odjel Buzet', 'Zemljišnoknjižni odjel Crikvenica' => 'Zemljišnoknjižni odjel Crikvenica', 'Zemljišnoknjižni odjel Čabar' => 'Zemljišnoknjižni odjel Čabar', 'Zemljišnoknjižni odjel Čakovec' => 'Zemljišnoknjižni odjel Čakovec', 'Zemljišnoknjižni odjel Čazma' => 'Zemljišnoknjižni odjel Čazma', 'Zemljišnoknjižni odjel Daruvar' => 'Zemljišnoknjižni odjel Daruvar', 'Zemljišnoknjižni odjel Delnice' => 'Zemljišnoknjižni odjel Delnice', 'Zemljišnoknjižni odjel Donja Stubica' => 'Zemljišnoknjižni odjel Donja Stubica', 'Zemljišnoknjižni odjel Donji Lapac' => 'Zemljišnoknjižni odjel Donji Lapac', 'Zemljišnoknjižni odjel Donji Miholjac' => 'Zemljišnoknjižni odjel Donji Miholjac', 'Zemljišnoknjižni odjel Drniš' => 'Zemljišnoknjižni odjel Drniš', 'Zemljišnoknjižni odjel Dubrovnik' => 'Zemljišnoknjižni odjel Dubrovnik', 'Zemljišnoknjižni odjel Dugo Selo' => 'Zemljišnoknjižni odjel Dugo Selo', 'Zemljišnoknjižni odjel Dvor' => 'Zemljišnoknjižni odjel Dvor', 'Zemljišnoknjižni odjel Đakovo' => 'Zemljišnoknjižni odjel Đakovo', 'Zemljišnoknjižni odjel Đurđevac' => 'Zemljišnoknjižni odjel Đurđevac', 'Zemljišnoknjižni odjel Garešnica' => 'Zemljišnoknjižni odjel Garešnica', 'Zemljišnoknjižni odjel Glina' => 'Zemljišnoknjižni odjel Glina', 'Zemljišnoknjižni odjel Gospić' => 'Zemljišnoknjižni odjel Gospić', 'Zemljišnoknjižni odjel Gračac' => 'Zemljišnoknjižni odjel Gračac', 'Zemljišnoknjižni odjel Gvozd' => 'Zemljišnoknjižni odjel Gvozd', 'Zemljišnoknjižni odjel Hrvatska Kostajnica' => 'Zemljišnoknjižni odjel Hrvatska Kostajnica', 'Zemljišnoknjižni odjel Ilok' => 'Zemljišnoknjižni odjel Ilok', 'Zemljišnoknjižni odjel Imotski' => 'Zemljišnoknjižni odjel Imotski', 'Zemljišnoknjižni odjel Ivanec' => 'Zemljišnoknjižni odjel Ivanec', 'Zemljišnoknjižni odjel Ivanić Grad' => 'Zemljišnoknjižni odjel Ivanić Grad', 'Zemljišnoknjižni odjel Jastrebarsko' => 'Zemljišnoknjižni odjel Jastrebarsko', 'Zemljišnoknjižni odjel Karlovac' => 'Zemljišnoknjižni odjel Karlovac', 'Zemljišnoknjižni odjel Kaštel Lukšić' => 'Zemljišnoknjižni odjel Kaštel Lukšić', 'Zemljišnoknjižni odjel Klanjec' => 'Zemljišnoknjižni odjel Klanjec', 'Zemljišnoknjižni odjel Knin' => 'Zemljišnoknjižni odjel Knin', 'Zemljišnoknjižni odjel Koprivnica' => 'Zemljišnoknjižni odjel Koprivnica', 'Zemljišnoknjižni odjel Korčula' => 'Zemljišnoknjižni odjel Korčula', 'Zemljišnoknjižni odjel Korenica' => 'Zemljišnoknjižni odjel Korenica', 'Zemljišnoknjižni odjel Krapina' => 'Zemljišnoknjižni odjel Krapina', 'Zemljišnoknjižni odjel Križevci' => 'Zemljišnoknjižni odjel Križevci', 'Zemljišnoknjižni odjel Krk' => 'Zemljišnoknjižni odjel Krk', 'Zemljišnoknjižni odjel Kutina' => 'Zemljišnoknjižni odjel Kutina', 'Zemljišnoknjižni odjel Labin' => 'Zemljišnoknjižni odjel Labin', 'Zemljišnoknjižni odjel Ludbreg' => 'Zemljišnoknjižni odjel Ludbreg', 'Zemljišnoknjižni odjel Makarska' => 'Zemljišnoknjižni odjel Makarska', 'Zemljišnoknjižni odjel Mali Lošinj' => 'Zemljišnoknjižni odjel Mali Lošinj', 'Zemljišnoknjižni odjel Metković' => 'Zemljišnoknjižni odjel Metković', 'Zemljišnoknjižni odjel Našice' => 'Zemljišnoknjižni odjel Našice', 'Zemljišnoknjižni odjel Nova Gradiška' => 'Zemljišnoknjižni odjel Nova Gradiška', 'Zemljišnoknjižni odjel Novi Marof' => 'Zemljišnoknjižni odjel Novi Marof', 'Zemljišnoknjižni odjel Novi Vinodolski' => 'Zemljišnoknjižni odjel Novi Vinodolski', 'Zemljišnoknjižni odjel Novi Zagreb' => 'Zemljišnoknjižni odjel Novi Zagreb', 'Zemljišnoknjižni odjel Novska' => 'Zemljišnoknjižni odjel Novska', 'Zemljišnoknjižni odjel Obrovac' => 'Zemljišnoknjižni odjel Obrovac', 'Zemljišnoknjižni odjel Ogulin' => 'Zemljišnoknjižni odjel Ogulin', 'Zemljišnoknjižni odjel Omiš' => 'Zemljišnoknjižni odjel Omiš', 'Zemljišnoknjižni odjel Opatija' => 'Zemljišnoknjižni odjel Opatija', 'Zemljišnoknjižni odjel Orahovica' => 'Zemljišnoknjižni odjel Orahovica', 'Zemljišnoknjižni odjel Osijek' => 'Zemljišnoknjižni odjel Osijek', 'Zemljišnoknjižni odjel Otočac' => 'Zemljišnoknjižni odjel Otočac', 'Zemljišnoknjižni odjel Otok' => 'Zemljišnoknjižni odjel Otok', 'Zemljišnoknjižni odjel Ozalj' => 'Zemljišnoknjižni odjel Ozalj', 'Zemljišnoknjižni odjel Pag' => 'Zemljišnoknjižni odjel Pag', 'Zemljišnoknjižni odjel Pakrac' => 'Zemljišnoknjižni odjel Pakrac', 'Zemljišnoknjižni odjel Pazin' => 'Zemljišnoknjižni odjel Pazin', 'Zemljišnoknjižni odjel Petrinja' => 'Zemljišnoknjižni odjel Petrinja', 'Zemljišnoknjižni odjel Pitomača' => 'Zemljišnoknjižni odjel Pitomača', 'Zemljišnoknjižni odjel Ploče' => 'Zemljišnoknjižni odjel Ploče', 'Zemljišnoknjižni odjel Poreč-Parenzo' => 'Zemljišnoknjižni odjel Poreč-Parenzo', 'Zemljišnoknjižni odjel Požega' => 'Zemljišnoknjižni odjel Požega', 'Zemljišnoknjižni odjel Pregrada' => 'Zemljišnoknjižni odjel Pregrada', 'Zemljišnoknjižni odjel Prelog' => 'Zemljišnoknjižni odjel Prelog', 'Zemljišnoknjižni odjel Pula' => 'Zemljišnoknjižni odjel Pula', 'Zemljišnoknjižni odjel Rab' => 'Zemljišnoknjižni odjel Rab', 'Zemljišnoknjižni odjel Rijeka' => 'Zemljišnoknjižni odjel Rijeka', 'Zemljišnoknjižni odjel Rovinj-Rovigno' => 'Zemljišnoknjižni odjel Rovinj-Rovigno', 'Zemljišnoknjižni odjel Samobor' => 'Zemljišnoknjižni odjel Samobor', 'Zemljišnoknjižni odjel Senj' => 'Zemljišnoknjižni odjel Senj', 'Zemljišnoknjižni odjel Sesvete' => 'Zemljišnoknjižni odjel Sesvete', 'Zemljišnoknjižni odjel Sinj' => 'Zemljišnoknjižni odjel Sinj', 'Zemljišnoknjižni odjel Sisak' => 'Zemljišnoknjižni odjel Sisak', 'Zemljišnoknjižni odjel Slatina' => 'Zemljišnoknjižni odjel Slatina', 'Zemljišnoknjižni odjel Slavonski Brod' => 'Zemljišnoknjižni odjel Slavonski Brod', 'Zemljišnoknjižni odjel Slunj' => 'Zemljišnoknjižni odjel Slunj', 'Zemljišnoknjižni odjel Solin' => 'Zemljišnoknjižni odjel Solin', 'Zemljišnoknjižni odjel Split' => 'Zemljišnoknjižni odjel Split', 'Zemljišnoknjižni odjel Stari Grad' => 'Zemljišnoknjižni odjel Stari Grad', 'Zemljišnoknjižni odjel Supetar' => 'Zemljišnoknjižni odjel Supetar', 'Zemljišnoknjižni odjel Sveti Ivan Zelina' => 'Zemljišnoknjižni odjel Sveti Ivan Zelina', 'Zemljišnoknjižni odjel Šibenik' => 'Zemljišnoknjižni odjel Šibenik', 'Zemljišnoknjižni odjel Tisno' => 'Zemljišnoknjižni odjel Tisno', 'Zemljišnoknjižni odjel Trogir' => 'Zemljišnoknjižni odjel Trogir', 'Zemljišnoknjižni odjel Valpovo' => 'Zemljišnoknjižni odjel Valpovo', 'Zemljišnoknjižni odjel Varaždin' => 'Zemljišnoknjižni odjel Varaždin', 'Zemljišnoknjižni odjel Velika Gorica' => 'Zemljišnoknjižni odjel Velika Gorica', 'Zemljišnoknjižni odjel Vinkovci' => 'Zemljišnoknjižni odjel Vinkovci', 'Zemljišnoknjižni odjel Virovitica' => 'Zemljišnoknjižni odjel Virovitica', 'Zemljišnoknjižni odjel Vojnić' => 'Zemljišnoknjižni odjel Vojnić', 'Zemljišnoknjižni odjel Vrbovec' => 'Zemljišnoknjižni odjel Vrbovec', 'Zemljišnoknjižni odjel Vrbovsko' => 'Zemljišnoknjižni odjel Vrbovsko', 'Zemljišnoknjižni odjel Vukovar' => 'Zemljišnoknjižni odjel Vukovar', 'Zemljišnoknjižni odjel Zabok' => 'Zemljišnoknjižni odjel Zabok', 'Zemljišnoknjižni odjel Zadar' => 'Zemljišnoknjižni odjel Zadar', 'Zemljišnoknjižni odjel Zagreb' => 'Zemljišnoknjižni odjel Zagreb', 'Zemljišnoknjižni odjel Zaprešić' => 'Zemljišnoknjižni odjel Zaprešić', 'Zemljišnoknjižni odjel Zlatar' => 'Zemljišnoknjižni odjel Zlatar', 'Zemljišnoknjižni odjel Županja' => 'Zemljišnoknjižni odjel Županja'], 'Zemljišnoknjižni odjel', ['class' => 'form-control landRegistry-select2']) }}
                        </div>
                    </div>

                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fText($model, "additional_realestates[{INDEX}][type_flat_deposited_contracts_book_name]", null, '3. Upiši naziv knjige položenih ugovora u kojoj je upisan stan', ['placeholder' => 'Npr: Vrapče staro'], 'Knjiga PU', 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/flat/3.jpeg') }}
                        </div>
                    </div>

                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, "additional_realestates[{INDEX}][type_flat_folio_number]", '4. Upiši broj poduloška i zk uloška u koji su upisani podaci vezani uz stan', 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/flat/4.jpeg') }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "additional_realestates[{INDEX}][type_flat_subfolio_number]", null, 'Broj poduloška', ['placeholder' => 'Npr: 11']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "additional_realestates[{INDEX}][type_flat_folio_number]", null, 'Broj zk uloška', ['placeholder' => 'Npr: 12345']) }}
                        </div>
                    </div>


                    <hr/>
                    <div class="row">
                        <div class="col-lg-12">
                            {{ Form::fTextArea($model, "additional_realestates[{INDEX}][type_flat_possession_sheet_section_one_data]", null, '5. Upiši podatke iz prvog odjeljka posjedovnice (lista A) koji se odnose stambenu zgradu u kojoj se nalazi stan', ['placeholder' => 'Npr: Stambena zgrada Riječka ulica 1, Rijeka, sagrađena na čest. br. 1111/11, po novoj izmjeri čest. br. 1234/1 k.o. PLASE'], 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/flat/5.jpeg') }}
                        </div>
                    </div>

                    <hr/>
                    <div class="row">
                        <div class="col-lg-12">
                            {{ Form::fTextArea($model, "additional_realestates[{INDEX}][type_flat_possession_sheet_section_two_data]", null, '6. Upiši podatke iz drugog odjeljka posjedovnice (lista A) koji se odnose na stan', ['placeholder' => 'Npr: stan na II (drugom) katu desno, koji se sastoji od dvije sobe i sporednih prostorija u površini od 61,11'], 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/flat/6.jpeg') }}
                        </div>
                    </div>

                    <hr/>
                    <div class="row">
                        <div class="col-lg-12">
                            {{ Form::fLabel($model, "additional_realestates[{INDEX}][type_flat_has_encumbrances_sheet_data]", '7. Želiš li u ugovor upisati podatke iz teretovnice (lista C) o teretima koji su upisani na stanu?') }}
                            {{ Form::fRadio($model, "additional_realestates[{INDEX}][type_flat_has_encumbrances_sheet_data]", 0, 'Ne', ['class' => 'form-check-input type_flat_has_not_encumbrances_sheet_data', 'checked' => true]) }}
                            {{ Form::fRadio($model, "additional_realestates[{INDEX}][type_flat_has_encumbrances_sheet_data]", 1, 'Da', ['class' => 'form-check-input type_flat_has_encumbrances_sheet_data']) }}
                        </div>
                    </div>
                    <div class="type_flat_encumbrances_sheet_data_container"
                         style="display:none;">
                        <hr/>
                        <div class="row">
                            <div class="col-lg-12">
                                {{ Form::fTextArea($model, "additional_realestates[{INDEX}][type_flat_encumbrances_sheet_data]", null, '<span class="dot"></span> Upiši podatke iz teretovnice (lista C) o teretima koji su upisani na stanu', ['placeholder' => 'Npr: Zaprimljeno 11. studenog 1998. br. Zs-1111/98. Na temelju ugovora o kupoprodaji od 11. lipnja 1993. br. 111-11-11-11111/1-93 upisuje se hipoteka na stan upisan u AII (dva) za iznos od 11.111.111,00 HRD, a kao nositelj tog prava upisuje se: REPUBLIKA HRVATSKA'], 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/flat/7DA.jpeg') }}
                            </div>
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="col-lg-12 form-group">
                            {{ Form::fTextArea($model, "additional_realestates[{INDEX}][type_flat_additional_realestate_data]", null, '8. Ovdje možeš upisati dodatne podatke o nekretnini koje mi nismo predvidjeli <small>(opcionalno)</small>', ['placeholder' => 'Npr: Na dan potpisivanja ovog Ugovora, na zemljišnoknjižnom ulošku u koji je upisana nekretnina postoji aktivna plomba koja se odnosi na...', 'data-force-start-case' => 'upper']) }}
                        </div>
                    </div>
                </div>

                <div class="realestate_type_other_container" style="display:none;">
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, "additional_realestates[{INDEX}][type_other_municipal_court]", '2. Upiši općinski sud i zemljišnoknjižni odjel koji vodi zemljišnu knjigu u koju je upisana nekretnina', 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/other/2.jpeg') }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fDropdown($model, 'additional_realestates[{INDEX}][type_other_municipal_court]', null, [null=>null, 'Općinski sud u Bjelovaru' => 'Općinski sud u Bjelovaru', 'Općinski sud u Crikvenici' => 'Općinski sud u Crikvenici', 'Općinski sud u Čakovcu' => 'Općinski sud u Čakovcu', 'Općinski sud u Dubrovniku' => 'Općinski sud u Dubrovniku', 'Općinski sud u Đakovu' => 'Općinski sud u Đakovu', 'Općinski sud u Gospiću' => 'Općinski sud u Gospiću', 'Općinski sud u Karlovcu' => 'Općinski sud u Karlovcu', 'Općinski sud u Koprivnici' => 'Općinski sud u Koprivnici', 'Općinski sud u Kutini' => 'Općinski sud u Kutini', 'Općinski sud u Makarskoj' => 'Općinski sud u Makarskoj', 'Općinski sud u Metkoviću' => 'Općinski sud u Metkoviću', 'Općinski sud u Novom Zagrebu' => 'Općinski sud u Novom Zagrebu', 'Općinski sud u Osijeku' => 'Općinski sud u Osijeku', 'Općinski sud u Pazinu' => 'Općinski sud u Pazinu', 'Općinski sud u Požegi' => 'Općinski sud u Požegi', 'Općinski sud u Puli-Pola' => 'Općinski sud u Puli-Pola', 'Općinski sud u Rijeci' => 'Općinski sud u Rijeci', 'Općinski sud u Sesvetama' => 'Općinski sud u Sesvetama', 'Općinski sud u Sisku' => 'Općinski sud u Sisku', 'Općinski sud u Slavonskom Brodu' => 'Općinski sud u Slavonskom Brodu', 'Općinski sud u Splitu' => 'Općinski sud u Splitu', 'Općinski sud u Šibeniku' => 'Općinski sud u Šibeniku', 'Općinski sud u Varaždinu' => 'Općinski sud u Varaždinu', 'Općinski sud u Velikoj Gorici' => 'Općinski sud u Velikoj Gorici', 'Općinski sud u Vinkovcima' => 'Općinski sud u Vinkovcima', 'Općinski sud u Virovitici' => 'Općinski sud u Virovitici', 'Općinski sud u Vukovaru' => 'Općinski sud u Vukovaru', 'Općinski sud u Zadru' => 'Općinski sud u Zadru', 'Općinski građanski sud u Zagrebu' => 'Općinski građanski sud u Zagrebu', 'Općinski sud u Zlataru' => 'Općinski sud u Zlataru'], 'Općinski sud', ['class' => 'form-control court-select2']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fDropdown($model, 'additional_realestates[{INDEX}][type_other_land_registry]', null, [null=>null, 'Zemljišnoknjižni odjel Beli Manastir' => 'Zemljišnoknjižni odjel Beli Manastir', 'Zemljišnoknjižni odjel Benkovac' => 'Zemljišnoknjižni odjel Benkovac', 'Zemljišnoknjižni odjel Biograd na Moru' => 'Zemljišnoknjižni odjel Biograd na Moru', 'Zemljišnoknjižni odjel Bjelovar' => 'Zemljišnoknjižni odjel Bjelovar', 'Zemljišnoknjižni odjel Blato' => 'Zemljišnoknjižni odjel Blato', 'Zemljišnoknjižni odjel Buje-Buie' => 'Zemljišnoknjižni odjel Buje-Buie', 'Zemljišnoknjižni odjel Buzet' => 'Zemljišnoknjižni odjel Buzet', 'Zemljišnoknjižni odjel Crikvenica' => 'Zemljišnoknjižni odjel Crikvenica', 'Zemljišnoknjižni odjel Čabar' => 'Zemljišnoknjižni odjel Čabar', 'Zemljišnoknjižni odjel Čakovec' => 'Zemljišnoknjižni odjel Čakovec', 'Zemljišnoknjižni odjel Čazma' => 'Zemljišnoknjižni odjel Čazma', 'Zemljišnoknjižni odjel Daruvar' => 'Zemljišnoknjižni odjel Daruvar', 'Zemljišnoknjižni odjel Delnice' => 'Zemljišnoknjižni odjel Delnice', 'Zemljišnoknjižni odjel Donja Stubica' => 'Zemljišnoknjižni odjel Donja Stubica', 'Zemljišnoknjižni odjel Donji Lapac' => 'Zemljišnoknjižni odjel Donji Lapac', 'Zemljišnoknjižni odjel Donji Miholjac' => 'Zemljišnoknjižni odjel Donji Miholjac', 'Zemljišnoknjižni odjel Drniš' => 'Zemljišnoknjižni odjel Drniš', 'Zemljišnoknjižni odjel Dubrovnik' => 'Zemljišnoknjižni odjel Dubrovnik', 'Zemljišnoknjižni odjel Dugo Selo' => 'Zemljišnoknjižni odjel Dugo Selo', 'Zemljišnoknjižni odjel Dvor' => 'Zemljišnoknjižni odjel Dvor', 'Zemljišnoknjižni odjel Đakovo' => 'Zemljišnoknjižni odjel Đakovo', 'Zemljišnoknjižni odjel Đurđevac' => 'Zemljišnoknjižni odjel Đurđevac', 'Zemljišnoknjižni odjel Garešnica' => 'Zemljišnoknjižni odjel Garešnica', 'Zemljišnoknjižni odjel Glina' => 'Zemljišnoknjižni odjel Glina', 'Zemljišnoknjižni odjel Gospić' => 'Zemljišnoknjižni odjel Gospić', 'Zemljišnoknjižni odjel Gračac' => 'Zemljišnoknjižni odjel Gračac', 'Zemljišnoknjižni odjel Gvozd' => 'Zemljišnoknjižni odjel Gvozd', 'Zemljišnoknjižni odjel Hrvatska Kostajnica' => 'Zemljišnoknjižni odjel Hrvatska Kostajnica', 'Zemljišnoknjižni odjel Ilok' => 'Zemljišnoknjižni odjel Ilok', 'Zemljišnoknjižni odjel Imotski' => 'Zemljišnoknjižni odjel Imotski', 'Zemljišnoknjižni odjel Ivanec' => 'Zemljišnoknjižni odjel Ivanec', 'Zemljišnoknjižni odjel Ivanić Grad' => 'Zemljišnoknjižni odjel Ivanić Grad', 'Zemljišnoknjižni odjel Jastrebarsko' => 'Zemljišnoknjižni odjel Jastrebarsko', 'Zemljišnoknjižni odjel Karlovac' => 'Zemljišnoknjižni odjel Karlovac', 'Zemljišnoknjižni odjel Kaštel Lukšić' => 'Zemljišnoknjižni odjel Kaštel Lukšić', 'Zemljišnoknjižni odjel Klanjec' => 'Zemljišnoknjižni odjel Klanjec', 'Zemljišnoknjižni odjel Knin' => 'Zemljišnoknjižni odjel Knin', 'Zemljišnoknjižni odjel Koprivnica' => 'Zemljišnoknjižni odjel Koprivnica', 'Zemljišnoknjižni odjel Korčula' => 'Zemljišnoknjižni odjel Korčula', 'Zemljišnoknjižni odjel Korenica' => 'Zemljišnoknjižni odjel Korenica', 'Zemljišnoknjižni odjel Krapina' => 'Zemljišnoknjižni odjel Krapina', 'Zemljišnoknjižni odjel Križevci' => 'Zemljišnoknjižni odjel Križevci', 'Zemljišnoknjižni odjel Krk' => 'Zemljišnoknjižni odjel Krk', 'Zemljišnoknjižni odjel Kutina' => 'Zemljišnoknjižni odjel Kutina', 'Zemljišnoknjižni odjel Labin' => 'Zemljišnoknjižni odjel Labin', 'Zemljišnoknjižni odjel Ludbreg' => 'Zemljišnoknjižni odjel Ludbreg', 'Zemljišnoknjižni odjel Makarska' => 'Zemljišnoknjižni odjel Makarska', 'Zemljišnoknjižni odjel Mali Lošinj' => 'Zemljišnoknjižni odjel Mali Lošinj', 'Zemljišnoknjižni odjel Metković' => 'Zemljišnoknjižni odjel Metković', 'Zemljišnoknjižni odjel Našice' => 'Zemljišnoknjižni odjel Našice', 'Zemljišnoknjižni odjel Nova Gradiška' => 'Zemljišnoknjižni odjel Nova Gradiška', 'Zemljišnoknjižni odjel Novi Marof' => 'Zemljišnoknjižni odjel Novi Marof', 'Zemljišnoknjižni odjel Novi Vinodolski' => 'Zemljišnoknjižni odjel Novi Vinodolski', 'Zemljišnoknjižni odjel Novi Zagreb' => 'Zemljišnoknjižni odjel Novi Zagreb', 'Zemljišnoknjižni odjel Novska' => 'Zemljišnoknjižni odjel Novska', 'Zemljišnoknjižni odjel Obrovac' => 'Zemljišnoknjižni odjel Obrovac', 'Zemljišnoknjižni odjel Ogulin' => 'Zemljišnoknjižni odjel Ogulin', 'Zemljišnoknjižni odjel Omiš' => 'Zemljišnoknjižni odjel Omiš', 'Zemljišnoknjižni odjel Opatija' => 'Zemljišnoknjižni odjel Opatija', 'Zemljišnoknjižni odjel Orahovica' => 'Zemljišnoknjižni odjel Orahovica', 'Zemljišnoknjižni odjel Osijek' => 'Zemljišnoknjižni odjel Osijek', 'Zemljišnoknjižni odjel Otočac' => 'Zemljišnoknjižni odjel Otočac', 'Zemljišnoknjižni odjel Otok' => 'Zemljišnoknjižni odjel Otok', 'Zemljišnoknjižni odjel Ozalj' => 'Zemljišnoknjižni odjel Ozalj', 'Zemljišnoknjižni odjel Pag' => 'Zemljišnoknjižni odjel Pag', 'Zemljišnoknjižni odjel Pakrac' => 'Zemljišnoknjižni odjel Pakrac', 'Zemljišnoknjižni odjel Pazin' => 'Zemljišnoknjižni odjel Pazin', 'Zemljišnoknjižni odjel Petrinja' => 'Zemljišnoknjižni odjel Petrinja', 'Zemljišnoknjižni odjel Pitomača' => 'Zemljišnoknjižni odjel Pitomača', 'Zemljišnoknjižni odjel Ploče' => 'Zemljišnoknjižni odjel Ploče', 'Zemljišnoknjižni odjel Poreč-Parenzo' => 'Zemljišnoknjižni odjel Poreč-Parenzo', 'Zemljišnoknjižni odjel Požega' => 'Zemljišnoknjižni odjel Požega', 'Zemljišnoknjižni odjel Pregrada' => 'Zemljišnoknjižni odjel Pregrada', 'Zemljišnoknjižni odjel Prelog' => 'Zemljišnoknjižni odjel Prelog', 'Zemljišnoknjižni odjel Pula' => 'Zemljišnoknjižni odjel Pula', 'Zemljišnoknjižni odjel Rab' => 'Zemljišnoknjižni odjel Rab', 'Zemljišnoknjižni odjel Rijeka' => 'Zemljišnoknjižni odjel Rijeka', 'Zemljišnoknjižni odjel Rovinj-Rovigno' => 'Zemljišnoknjižni odjel Rovinj-Rovigno', 'Zemljišnoknjižni odjel Samobor' => 'Zemljišnoknjižni odjel Samobor', 'Zemljišnoknjižni odjel Senj' => 'Zemljišnoknjižni odjel Senj', 'Zemljišnoknjižni odjel Sesvete' => 'Zemljišnoknjižni odjel Sesvete', 'Zemljišnoknjižni odjel Sinj' => 'Zemljišnoknjižni odjel Sinj', 'Zemljišnoknjižni odjel Sisak' => 'Zemljišnoknjižni odjel Sisak', 'Zemljišnoknjižni odjel Slatina' => 'Zemljišnoknjižni odjel Slatina', 'Zemljišnoknjižni odjel Slavonski Brod' => 'Zemljišnoknjižni odjel Slavonski Brod', 'Zemljišnoknjižni odjel Slunj' => 'Zemljišnoknjižni odjel Slunj', 'Zemljišnoknjižni odjel Solin' => 'Zemljišnoknjižni odjel Solin', 'Zemljišnoknjižni odjel Split' => 'Zemljišnoknjižni odjel Split', 'Zemljišnoknjižni odjel Stari Grad' => 'Zemljišnoknjižni odjel Stari Grad', 'Zemljišnoknjižni odjel Supetar' => 'Zemljišnoknjižni odjel Supetar', 'Zemljišnoknjižni odjel Sveti Ivan Zelina' => 'Zemljišnoknjižni odjel Sveti Ivan Zelina', 'Zemljišnoknjižni odjel Šibenik' => 'Zemljišnoknjižni odjel Šibenik', 'Zemljišnoknjižni odjel Tisno' => 'Zemljišnoknjižni odjel Tisno', 'Zemljišnoknjižni odjel Trogir' => 'Zemljišnoknjižni odjel Trogir', 'Zemljišnoknjižni odjel Valpovo' => 'Zemljišnoknjižni odjel Valpovo', 'Zemljišnoknjižni odjel Varaždin' => 'Zemljišnoknjižni odjel Varaždin', 'Zemljišnoknjižni odjel Velika Gorica' => 'Zemljišnoknjižni odjel Velika Gorica', 'Zemljišnoknjižni odjel Vinkovci' => 'Zemljišnoknjižni odjel Vinkovci', 'Zemljišnoknjižni odjel Virovitica' => 'Zemljišnoknjižni odjel Virovitica', 'Zemljišnoknjižni odjel Vojnić' => 'Zemljišnoknjižni odjel Vojnić', 'Zemljišnoknjižni odjel Vrbovec' => 'Zemljišnoknjižni odjel Vrbovec', 'Zemljišnoknjižni odjel Vrbovsko' => 'Zemljišnoknjižni odjel Vrbovsko', 'Zemljišnoknjižni odjel Vukovar' => 'Zemljišnoknjižni odjel Vukovar', 'Zemljišnoknjižni odjel Zabok' => 'Zemljišnoknjižni odjel Zabok', 'Zemljišnoknjižni odjel Zadar' => 'Zemljišnoknjižni odjel Zadar', 'Zemljišnoknjižni odjel Zagreb' => 'Zemljišnoknjižni odjel Zagreb', 'Zemljišnoknjižni odjel Zaprešić' => 'Zemljišnoknjižni odjel Zaprešić', 'Zemljišnoknjižni odjel Zlatar' => 'Zemljišnoknjižni odjel Zlatar', 'Zemljišnoknjižni odjel Županja' => 'Zemljišnoknjižni odjel Županja'], 'Zemljišnoknjižni odjel', ['class' => 'form-control landRegistry-select2']) }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fText($model, "additional_realestates[{INDEX}][type_other_cadastral_municipality]", null, '3. Upiši oznaku i naziv katastarske općine u kojoj se nekretnina nalazi', ['placeholder' => 'Npr: 999901, GRAD ZAGREB'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/other/3.jpeg') }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fText($model, "additional_realestates[{INDEX}][type_other_land_registry_folio_number]", null, '4. Upiši broj zemljišnoknjižnog uloška u koji su upisani podaci vezani uz nekretninu', ['placeholder' => 'Npr: 12345'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/other/4.jpeg') }}
                        </div>
                    </div>

                    <hr/>

                    <div class="mb-3">
                        <div class="card">
                            <div class="card-header">
                                Katastarska čestica
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fText($model, "additional_realestates[{INDEX}][type_other_possession_number]", null, 'Upiši podatke iz posjedovnice (lista A) koji se odnose na broj katastarske čestice odnosno zemljište', ['placeholder' => 'Npr: 1111/1'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/other/5A.jpeg') }}
                                    </div>
                                </div>
                                <hr/>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fDropdownText($model ,'Površina zemljišta', 'additional_realestates[{INDEX}][type_other_possession_area]', null, 'additional_realestates[{INDEX}][type_other_possession_area_type]', null, ["m2" => "m2", "čvh" => "čvh", "jutro" => "jutro"], ['placeholder' => 'Npr: 1100'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/other/5C.jpeg') }}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12">
                                        {{ Form::fTextArea($model, "additional_realestates[{INDEX}][type_other_possession_identification]", null, 'Oznaka zemljišta', ['rows' => 2, 'placeholder' => 'Npr: KUĆA BROJ 1 I DVORIŠTE U RIJEČKOJ ULICI'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/other/5B.jpeg') }}
                                    </div>
                                </div>

                                <div class="type_other_additional_possession_area_and_identification_container">

                                </div>

                                <hr/>
                                <div class="row mt-3">
                                    <div class="col-lg-12">
                                        <a data-field-name="additional_realestates[{INDEX}][type_other_additional_possession_area_and_identification]"  class="btn btn-default add_type_other_additional_possession_area_and_identification">+ Dodaj oznaku i površinu </a>

                                        <div class="button-helper">
                                            <a href="/documents/RealestateGiftContract/tooltips/other/help-1.jpeg" data-caption='Ponekad su u izvatku iz zemljišne knjige ispod retka koji se odnosi na oznaku cijelog zemljišta i njegovu površinu (obično prvi redak stupaca "Oznaka zemljišta" i "Površina" ispisan masnim slovima), upisani i dodatni retci u kojima se upisuju oznake sastavnih dijelova cijelog zemljišta i upisuje površina tih sastavnih dijelova. Za upis tih dodatnih redaka u ugovor, klikni na "Dodaj oznaku i površinu".' class="fancybox"><i class="fa fa-question-circle"></i> Objašnjenje</a>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="type_other_additional_land_plot_container">
                    </div>

                    <a data-field-name="additional_realestates[{INDEX}][type_other_additional_land_plots]" class="btn btn-info btn-block add_type_other_additional_land_plot">Dodaj katastarsku česticu</a>
                    <div class="button-helper text-center">
                        <a href="/documents/RealestateGiftContract/tooltips/other/help-2.jpeg" data-caption='Ponekad je u istom zemljišnoknjižnom ulošku upisano više zemljišta odnosno katastarskih čestica. Ako je više katastarskih čestica iz istog zemljišnoknjižnog uloška predmet ugovora o darovanju, za upis tih katastarskih čestica u ugovor klikni na "Dodaj katastarsku česticu".' class="fancybox"><i class="fa fa-question-circle"></i> Objašnjenje</a>
                    </div>

                    <hr/>
                    <div class="row">
                        <div class="col-lg-12">
                            {{ Form::fLabel($model, "additional_realestates[{INDEX}][type_other_has_encumbrances_sheet_data]", '5. Je li u teretovnici (listu C) za nekretninu upisan neki teret?') }}
                            {{ Form::fRadio($model, "additional_realestates[{INDEX}][type_other_has_encumbrances_sheet_data]", 0, 'Ne', ['class' => 'form-check-input type_other_has_not_encumbrances_sheet_data', 'checked' => true]) }}
                            {{ Form::fRadio($model, "additional_realestates[{INDEX}][type_other_has_encumbrances_sheet_data]", 1, 'Da', ['class' => 'form-check-input type_other_has_encumbrances_sheet_data',]) }}
                        </div>
                    </div>
                    <div class="type_other_encumbrances_sheet_data_container"
                         style="display:none;">
                        <hr/>
                        <div class="row">
                            <div class="col-lg-12">
                                {{ Form::fTextArea($model, "additional_realestates[{INDEX}][type_other_encumbrances_sheet_data]", null, '<span class="dot"></span> Upiši podatke iz teretovnice (lista C) o teretima koji su upisani na posebnom dijelu nekretnine', ['placeholder' => 'Npr: Zaprimljeno 11. 11. 2011. broj Z-12345/11. Na temelju Ugovora o osnivanju prava služnosti broj SLV-1/11, sklopljenog dana 11. 6. 2011., uknjižuje se pravo služnosti na k.č.br. 1111/11 upisanoj u A, površina služnosti iznosi 111m2, za potrebe postavljanja vodovodnih cijevi, kako je ucrtano u skici izmjere zemljišta koja čini sastavni dio navedenog ugovora, za korist: VODOVOD I VODOOPSRKBA, OIB: 12345678901, Rijeka, Riječka ulica 11.'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestateGiftContract/tooltips/other/7DA.jpeg') }}
                            </div>
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="col-lg-12 form-group">
                            {{ Form::fTextArea($model, "additional_realestates[{INDEX}][type_other_additional_realestate_data]", null, '6. Ovdje možeš upisati dodatne podatke o nekretnini koje mi nismo predvidjeli <small>(opcionalno)</small>', ['placeholder' => 'Npr: Na dan potpisivanja ovog Ugovora, na zemljišnoknjižnom ulošku u koji je upisana nekretnina postoji aktivna plomba koja se odnosi na...', 'data-force-start-case' => 'upper']) }}
                        </div>
                    </div>
                </div>

                <div class="donor_shares_container">

                    @if(empty($model->document->getValue('additional_donors')))
                        <hr/>
                        <div class="row donor_share">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Je li darovatelj vlasnik vlasničkog dijela 1/1 ili suvlasničkog dijela nekretnine?') }}
                                {{ Form::fRadio($model, 'additional_realestates[{INDEX}][donor_has_full_realestate_ownership]', 1, 'Vlasničkog dijela 1/1', ['checked' => true, 'class' => 'form-check-input donor_has_full_realestate_ownership'] , ' ', null, ' ') }}
                                <div class="radio-input">
                                    {{ Form::fRadio($model, 'additional_realestates[{INDEX}][donor_has_full_realestate_ownership]', 0, 'Suvlasničkog dijela:', ['class' => 'form-check-input donor_has_not_full_realestate_ownership'], ' ', null, ' ') }}
                                    {{ Form::number("additional_realestates[{INDEX}][donor_realestate_ownership_ratio_numerator]", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                    /
                                    {{ Form::number("additional_realestates[{INDEX}][donor_realestate_ownership_ratio_denominator]", null, ['placeholder' => 'Npr: 2', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                </div>
                            </div>
                        </div>
                    @else
                        <hr/>
                        <div class="row donor_shares" data-donor-name="{{ $model->document->getValue('donor_name') }}">
                            <div class="col-lg-12 form-group">
                                {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Upiši koji suvlasnički dio pripada darovatelju '.$model->document->getValue('donor_name'), ' ', null, ' ') }}

                                <br/>
                                {{ Form::number("additional_realestates[{INDEX}][donors_realestate_ownership_ratio_numerator][0]", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                /
                                {{ Form::number("additional_realestates[{INDEX}][donors_realestate_ownership_ratio_denominator][0]", null, ['placeholder' => 'Npr: 2', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                            </div>
                        </div>
                        @foreach($model->document->getValue('additional_donors') as $_i => $_additional_donor)
                            <hr/>
                            <div class="row donor_shares" data-donor-name="{{ $_additional_donor['name'] }}">
                                <div class="col-lg-12 form-group">
                                    {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Upiši koji suvlasnički dio pripada darovatelju '.$_additional_donor['name'], ' ', null, ' ') }}

                                    <br/>
                                    {{ Form::number("additional_realestates[{INDEX}][donors_realestate_ownership_ratio_numerator][$_i]", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                    /
                                    {{ Form::number("additional_realestates[{INDEX}][donors_realestate_ownership_ratio_denominator][$_i]", null, ['placeholder' => 'Npr: 2', 'style' => 'width:100px; display: inline-block; text-align:center;;', 'class' => 'form-control', 'min' => 1]) }}
                                </div>
                            </div>
                        @endforeach
                    @endif

                </div>

                @if(empty($model->document->getValue('additional_donors')) && empty($model->document->getValue('additional_beneficiaries')))
                    <div class="single_donor_single_beneficiary" data-donor="0">
                        <hr/>
                        <div class="row" data-beneficiary="0">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Stječe li obdarenik <span class="realestate_ownership_label_nominativ">vlasnički</span> dio darovatelja u cijelosti ili djelomično?') }}
                                {{ Form::fRadio($model, 'additional_realestates[{INDEX}][beneficiary_acquires_full_ownership_of_donor_realestate]', 1, 'U cijelosti', ['checked' => true]) }}
                                <div class="radio-input">
                                    {{ Form::fRadio($model, 'additional_realestates[{INDEX}][beneficiary_acquires_full_ownership_of_donor_realestate]', 0, 'Djelomično i to:', ['class' => 'form-check-input part_ownership_radio']) }}
                                    {{ Form::number("additional_realestates[{INDEX}][beneficiary_ownership_of_donor_realestate_ratio_numerator]", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                    /
                                    {{ Form::number("additional_realestates[{INDEX}][beneficiary_ownership_of_donor_realestate_ratio_denominator]", null, ['placeholder' => 'Npr: 4', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                    dijela koji je u vlasništvu darovatelja
                                </div>
                            </div>
                        </div>
                    </div>
                @elseif(empty($model->document->getValue('additional_donors')) && !empty($model->document->getValue('additional_beneficiaries')))
                    <div class="single_donor_multi_beneficiaries" data-donor="0">
                        <hr/>
                        <div class="row" data-beneficiary="0" data-beneficiary-name="{{ $model->document->getValue('beneficiary_name') }}">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Upiši koji dio <span class="realestate_ownership_label_genitiv">suvlasničkog</span> dijela darovatelja stječe obdarenik '.$model->document->getValue('beneficiary_name')) }}
                                <br/>
                                {{ Form::number("additional_realestates[{INDEX}][beneficiaries_ownership_of_donor_realestate_ratio_numerator][0]", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                /
                                {{ Form::number("additional_realestates[{INDEX}][beneficiaries_ownership_of_donor_realestate_ratio_denominator][0]", null, ['placeholder' => 'Npr: 2', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                            </div>
                        </div>
                        @foreach($model->document->getValue('additional_beneficiaries') as $_i => $_additional_beneficiary)
                            <hr/>
                            <div class="row" data-beneficiary="{{ $_i }}" data-beneficiary-name="{{ $_additional_beneficiary['name'] }}">
                                <div class="form-group col-lg-12">
                                    {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Upiši koji dio <span class="realestate_ownership_label_genitiv">suvlasničkog</span> dijela darovatelja stječe obdarenik '.$_additional_beneficiary['name']) }}
                                    <br/>
                                    {{ Form::number("additional_realestates[{INDEX}][beneficiaries_ownership_of_donor_realestate_ratio_numerator][$_i]", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                    /
                                    {{ Form::number("additional_realestates[{INDEX}][beneficiaries_ownership_of_donor_realestate_ratio_denominator][$_i]", null, ['placeholder' => 'Npr: 2', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                </div>
                            </div>
                        @endforeach
                    </div>
                @elseif(!empty($model->document->getValue('additional_donors')) && empty($model->document->getValue('additional_beneficiaries')))
                    <div class="donor_group" data-donor="0" data-donor-name="{{ $model->document->getValue('donor_name') }}">
                        <hr/>
                        <div class="row beneficiary_group" data-beneficiary="0" data-beneficiary-name="{{ $model->document->getValue('beneficiary_name') }}">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Stječe li obdarenik suvlasnički dio darovatelja '.$model->document->getValue('donor_name').' u cijelosti ili djelomično?') }}
                                {{ Form::fRadio($model, 'additional_realestates[{INDEX}][beneficiary_acquires_full_ownership_of_donors_realestate][0]', 1, 'U cijelosti', ['class' => 'form-check-input full_ownership_radio','checked' => true]) }}

                                <div class="radio-input">
                                    {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiary_acquires_full_ownership_of_donors_realestate][0]", 0, 'Djelomično i to:', ['class' => 'form-check-input part_ownership_radio']) }}
                                    {{ Form::number("additional_realestates[{INDEX}][beneficiary_ownership_of_donors_realestate_ratio_numerator][0]", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                    /
                                    {{ Form::number("additional_realestates[{INDEX}][beneficiary_ownership_of_donors_realestate_ratio_denominator][0]", null, ['placeholder' => 'Npr: 4', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                    ovog suvlasničkog dijela
                                </div>
                            </div>
                        </div>
                    </div>
                    @foreach($model->document->getValue('additional_donors') as $_i => $_additional_donor)
                        <div class="donor_group" data-donor="{{$_i}}" data-donor-name="{{ $_additional_donor['name'] }}">
                            <hr/>
                            <div class="row beneficiary_group" data-beneficiary="0" data-beneficiary-name="{{ $model->document->getValue('beneficiary_name') }}">
                                <div class="form-group col-lg-12">
                                    {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Stječe li obdarenik suvlasnički dio darovatelja '.$_additional_donor['name'].' u cijelosti ili djelomično?') }}
                                    {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiary_acquires_full_ownership_of_donors_realestate][$_i]", 1, 'U cijelosti', ['class' => 'form-check-input full_ownership_radio','checked' => true]) }}

                                    <div class="radio-input">
                                        {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiary_acquires_full_ownership_of_donors_realestate][$_i]", 0, 'Djelomično i to:', ['class' => 'form-check-input part_ownership_radio']) }}
                                        {{ Form::number("additional_realestates[{INDEX}][beneficiary_ownership_of_donors_realestate_ratio_numerator][$_i]", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                        /
                                        {{ Form::number("additional_realestates[{INDEX}][beneficiary_ownership_of_donors_realestate_ratio_denominator][$_i]", null, ['placeholder' => 'Npr: 4', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                        ovog suvlasničkog dijela
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @elseif(!empty($model->document->getValue('additional_donors')) && !empty($model->document->getValue('additional_beneficiaries')))

                    <div class="donor_group" data-donor="0"
                         data-donor-name="{{ $model->document->getValue('donor_name') }}">
                        <hr/>
                        <div class="row beneficiary_group" data-beneficiary="0"
                             data-beneficiary-name="{{ $model->document->getValue('beneficiary_name') }}">
                            <div class="col-lg-12 form-group">
                                {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Stječe li obdarenik '.$model->document->getValue('beneficiary_name').' suvlasnički dio darovatelja '.$model->document->getValue('donor_name').' u cijelosti ili djelomično ili ga ne stječe?') }}
                                {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiaries_acquire_full_ownership_of_donors_realestate][0][0]", 3, 'Obdarenik '.$model->document->getValue('beneficiary_name').' ne stječe ovaj suvlasnički dio', ['class' => 'form-check-input no_ownership_radio']) }}
                                {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiaries_acquire_full_ownership_of_donors_realestate][0][0]", 1, 'U cijelosti', ['class' => 'form-check-input full_ownership_radio']) }}
                                <div class="radio-input">
                                    {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiaries_acquire_full_ownership_of_donors_realestate][0][0]", 0, 'Djelomično i to:', ['class' => 'form-check-input part_ownership_radio', 'checked' => true]) }}
                                    {{ Form::number("additional_realestates[{INDEX}][beneficiaries_ownership_of_donors_realestate_ratio_numerator][0][0]", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                    /
                                    {{ Form::number("additional_realestates[{INDEX}][beneficiaries_ownership_of_donors_realestate_ratio_denominator][0][0]", null, ['placeholder' => 'Npr: 4', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                    ovog suvlasničkog dijela
                                </div>
                            </div>
                        </div>

                        @foreach($model->document->getValue('additional_beneficiaries') as $_i => $_additional_beneficiary)
                            <hr/>
                            <div class="row beneficiary_group" data-beneficiary="{{ $_i }}"
                                 data-beneficiary-name="{{ $_additional_beneficiary['name'] }}">
                                <div class="col-lg-12 form-group">
                                    {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Stječe li obdarenik '.$_additional_beneficiary['name'].' suvlasnički dio darovatelja '.$model->document->getValue('donor_name').' u cijelosti ili djelomično ili ga ne stječe?') }}
                                    {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiaries_acquire_full_ownership_of_donors_realestate][$_i][0]", 3, 'Obdarenik '.$_additional_beneficiary['name'].' ne stječe ovaj suvlasnički dio', ['class' => 'form-check-input no_ownership_radio']) }}
                                    {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiaries_acquire_full_ownership_of_donors_realestate][$_i][0]", 1, 'U cijelosti', ['class' => 'form-check-input full_ownership_radio']) }}
                                    <div class="radio-input">
                                        {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiaries_acquire_full_ownership_of_donors_realestate][$_i][0]", 0, 'Djelomično i to:', ['class' => 'form-check-input part_ownership_radio', 'checked' => true]) }}
                                        {{ Form::number("additional_realestates[{INDEX}][beneficiaries_ownership_of_donors_realestate_ratio_numerator][$_i][0]", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                        /
                                        {{ Form::number("additional_realestates[{INDEX}][beneficiaries_ownership_of_donors_realestate_ratio_denominator][$_i][0]", null, ['placeholder' => 'Npr: 4', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                        ovog suvlasničkog dijela
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    @foreach($model->document->getValue('additional_donors') as $_i => $_additional_donor)
                        <div class="donor_group" data-donor="{{ $_i }}"
                             data-donor-name="{{ $_additional_donor['name'] }}">
                            <hr/>
                            <div class="row beneficiary_group" data-beneficiary="0"
                                 data-beneficiary-name="{{ $model->document->getValue('beneficiary_name') }}">
                                <div class="col-lg-12 form-group">
                                    {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Stječe li obdarenik '.$model->document->getValue('beneficiary_name').' suvlasnički dio darovatelja '.$_additional_donor['name'].' u cijelosti ili djelomično ili ga ne stječe?') }}
                                    {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiaries_acquire_full_ownership_of_donors_realestate][0][$_i]", 3, 'Obdarenik '.$model->document->getValue('beneficiary_name').' ne stječe ovaj suvlasnički dio', ['class' => 'form-check-input no_ownership_radio']) }}
                                    {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiaries_acquire_full_ownership_of_donors_realestate][0][$_i]", 1, 'U cijelosti', ['class' => 'form-check-input full_ownership_radio']) }}

                                    <div class="radio-input">
                                        {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiaries_acquire_full_ownership_of_donors_realestate][0][$_i]", 0, 'Djelomično i to:', ['class' => 'form-check-input part_ownership_radio', 'checked' => true]) }}
                                        {{ Form::number("additional_realestates[{INDEX}][beneficiaries_ownership_of_donors_realestate_ratio_numerator][0][$_i]", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                        /
                                        {{ Form::number("additional_realestates[{INDEX}][beneficiaries_ownership_of_donors_realestate_ratio_denominator][0][$_i]", null, ['placeholder' => 'Npr: 4', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                        ovog suvlasničkog dijela
                                    </div>
                                </div>
                            </div>

                            @foreach($model->document->getValue('additional_beneficiaries') as $_j => $_additional_beneficiary)
                                <hr/>
                                <div class="row beneficiary_group" data-beneficiary="{{ $_j }}"
                                     data-beneficiary-name="{{ $_additional_beneficiary['name'] }}">
                                    <div class="col-lg-12 form-group">
                                        {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Stječe li obdarenik '.$_additional_beneficiary['name'].' suvlasnički dio darovatelja '.$_additional_donor['name'].' u cijelosti ili djelomično ili ga ne stječe?') }}
                                        {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiaries_acquire_full_ownership_of_donors_realestate][$_j][$_i]", 3, 'Obdarenik '.$_additional_beneficiary['name'].' ne stječe ovaj suvlasnički dio', ['class' => 'form-check-input no_ownership_radio']) }}
                                        {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiaries_acquire_full_ownership_of_donors_realestate][$_j][$_i]", 1, 'U cijelosti', ['class' => 'form-check-input full_ownership_radio']) }}

                                        <div class="radio-input">
                                            {{ Form::fRadio($model, "additional_realestates[{INDEX}][beneficiaries_acquire_full_ownership_of_donors_realestate][$_j][$_i]", 0, 'Djelomično i to:', ['class' => 'form-check-input part_ownership_radio', 'checked' => true]) }}
                                            {{ Form::number("additional_realestates[{INDEX}][beneficiaries_ownership_of_donors_realestate_ratio_numerator][$_j][$_i]", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                            /
                                            {{ Form::number("additional_realestates[{INDEX}][beneficiaries_ownership_of_donors_realestate_ratio_denominator][$_j][$_i]", null, ['placeholder' => 'Npr: 4', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                            ovog suvlasničkog dijela
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endforeach
                @endif
            </div>
        </div>
    </div>
</div>
