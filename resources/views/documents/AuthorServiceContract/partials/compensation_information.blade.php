<div class="row">
    <div class="form-group col-lg-12">
        {{ Form::fLabel($model, $compensation.'_type', '<span class="question_index">'.$question_index.'</span>. Ugovara li se autorska naknada'.$suffix.' u fiksnom ili jediničnom iznosu?') }}
        {{ Form::fRadio($model, $compensation.'_type', 'fixed', 'U fiksnom iznosu', ['id' => $compensation.'_type_fixed','checked' => !isset($model->{$compensation.'_type'})]) }}
        {{ Form::fRadio($model, $compensation.'_type', 'unit', 'U jediničnom iznosu', ['id' => $compensation.'_type_unit', ])}}
    </div>
</div>

<div id="{{ $compensation }}_type_fixed_container" style="@if(isset($model->{$compensation.'_type'}) && $model->{$compensation.'_type'} != 'fixed') display:none; @endif">
    <hr/>
    <div class="row">
        <div class="form group col-lg-12">
            {{ Form::fText($model, $compensation.'_gross_amount', null, '<span class="dot"></span> Upiši bruto iznos ukupne autorske naknade'.$suffix.' koju se naručitelj obvezuje platiti autoru', ['placeholder'=>'Npr: 1.000,00', 'data-currency' => 'EUR'], '€') }}
        </div>
    </div>
    <hr/>
    <div class="row">
        <div class="form-group col-lg-12">
            {{ Form::fLabel($model, $compensation.'_in_installments', '<span class="dot"></span> Plaća li naručitelj autoru autorsku naknadu'.$suffix.' odjednom ili u dijelovima?') }}
            {{ Form::fRadio($model, $compensation.'_in_installments', 0, 'Odjednom', ['checked' => !isset($model->{$compensation.'_in_installments'}), 'id' => $compensation.'_in_full_radio']) }}
            {{ Form::fRadio($model, $compensation.'_in_installments', 1, 'U dijelovima', ['id' => $compensation.'_in_installments_radio']) }}
        </div>
    </div>

    <div class="dynamic" id="{{ $compensation }}_in_full_container"
         style="@if(!empty($model->{$compensation.'_in_installments'})) display:none; @endif">
        <hr/>
        <div class="row">
            <div class="form-group col-lg-12">
                {{ Form::fLabel($model, 'fixed_'.$compensation.'_deadline', '<span class="dot"></span> U kojem roku se naručitelj obvezuje platiti autoru '. ($suffix ? "autorsku naknadu $suffix" : "ukupnu autorsku naknadu") .'?') }}
                <div class="form-check mb-1 mt-1">
                    <label class="form-check-label" style="width:100%;">
                        {{ Form::radio('fixed_'.$compensation.'_deadline', 'custom', null, ['class' => 'form-check-input '.$compensation.'_deadline_custom_radio', 'checked' => !isset($model->{'fixed_'.$compensation.'_deadline'})]) }}
                        Upiši rok
                        <br/>
                        {{ Form::fText($model, 'fixed_'.$compensation.'_deadline_custom', null, null, ['class' => 'form-control '.$compensation.'_deadline_custom', 'placeholder' => 'Npr: u roku od 15 dana od isporuke Autorskog djela od strane Autora', 'autocomplete' => 'off']) }}
                    </label>
                </div>
                <div class="form-check">
                    <label class="form-check-label" style="width:100%;">
                        {{ Form::radio('fixed_'.$compensation.'_deadline', 'date', null, ['class' => 'form-check-input '.$compensation.'_deadline_date_radio']) }}
                        Najkasnije dana
                        <br/>
                        {{ Form::fText($model, 'fixed_'.$compensation.'_deadline_date', null, null, ['class' => 'form-control '.$compensation.'_deadline_date', 'data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="dynamic" id="{{ $compensation }}_in_installments_container"
         style="@if(empty($model->{$compensation.'_in_installments'})) display:none; @endif">

        <hr/>

        <div class="row">
            <div class="form-group col-lg-12">
                {{ Form::fDropdown($model, $compensation.'_installments_number', null, array_combine(range(2, 10), range(2, 10)), '<span class="dot"></span> U koliko dijelova će naručitelj platiti autoru '. ($suffix ? "autorsku naknadu $suffix" : "ukupnu autorsku naknadu") .'?', ['id' => $compensation.'_installments_number']) }}
            </div>
        </div>

        <div id="{{ $compensation }}_installment_template" style="display:none;">
            <div class="card mb-4">
                <div class="card-header">
                    {INDEX}. dio naknade
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fText($model, $compensation.'_installments[{KEY}][amount]', null, 'Bruto iznos', ['placeholder' => 'Npr: 300,00', 'data-currency' => 'EUR'], '€') }}
                        </div>
                        <div class="form-group col-lg-12">
                            <hr/>
                            <label>U kojem roku se naručitelj obvezuje platiti autoru {INDEX}. dio autorske naknade{{ $suffix }}?</label>

                            <div class="form-check mb-1 mt-1">
                                <label class="form-check-label" style="width:100%;">
                                    {{ Form::radio($compensation.'_installments[{KEY}][deadline]', 'custom', null, ['class' => 'form-check-input '.$compensation.'_deadline_custom_radio', 'checked' => true]) }}
                                    Upiši rok
                                    <br/>
                                    {{ Form::fText($model, $compensation.'_installments[{KEY}][deadline_custom]', null, null, ['class' => 'form-control '.$compensation.'_deadline_custom', 'placeholder' => 'Npr: u roku od 15 dana od isporuke Autorskog djela od strane Autora', 'autocomplete' => 'off']) }}
                                </label>
                            </div>
                            <div class="form-check">
                                <label class="form-check-label" style="width:100%;">
                                    {{ Form::radio($compensation.'_installments[{KEY}][deadline]', 'date', null, ['class' => 'form-check-input '.$compensation.'_deadline_date_radio']) }}
                                    Najkasnije dana
                                    <br/>
                                    {{ Form::fText($model, $compensation.'_installments[{KEY}][deadline_date]', null, null, ['class' => 'form-control '.$compensation.'_deadline_date','data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                </label>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="{{ $compensation }}_installments">
            @if(empty($model->{$compensation.'_installments'}))
                <div class="card mb-4">
                    <div class="card-header">
                        1. dio naknade
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fText($model, $compensation.'_installments[0][amount]', null, 'Bruto iznos', ['placeholder' => 'Npr: 300,00', 'data-currency' => 'EUR'], '€') }}
                            </div>
                            <div class="form-group col-lg-12">
                                <hr/>
                                <label>U kojem roku se naručitelj obvezuje platiti autoru 1. dio autorske naknade{{ $suffix }}?
                                </label>
                                <div class="form-check mb-1 mt-1">
                                    <label class="form-check-label" style="width:100%;">
                                        {{ Form::radio($compensation.'_installments[0][deadline]', 'custom', null, ['class' => 'form-check-input '.$compensation.'_deadline_custom_radio', 'checked' => true]) }}
                                        Upiši rok
                                        <br/>
                                        {{ Form::fText($model, $compensation.'_installments[0][deadline_custom]', null, null, ['class' => 'form-control '.$compensation.'_deadline_custom', 'placeholder' => 'Npr: kao predujam na dan potpisivanja ovog Ugovora', 'autocomplete' => 'off']) }}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <label class="form-check-label" style="width:100%;">
                                        {{ Form::radio($compensation.'_installments[0][deadline]', 'date', null, ['class' => 'form-check-input '.$compensation.'_deadline_date_radio']) }}
                                        Najkasnije dana
                                        <br/>
                                        {{ Form::fText($model, $compensation.'_installments[0][deadline_date]', null, null, ['class' => 'form-control '.$compensation.'_deadline_date', 'data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        2. dio naknade
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fText($model, $compensation.'_installments[1][amount]', null, 'Bruto iznos', ['placeholder' => 'Npr: 300,00', 'data-currency' => 'EUR'], '€') }}
                            </div>
                            <div class="form-group col-lg-12">
                                <hr/>
                                <label>U kojem roku se naručitelj obvezuje platiti autoru 2. dio autorske naknade{{ $suffix }}?
                                </label>
                                <div class="form-check mb-1 mt-1">
                                    <label class="form-check-label" style="width:100%;">
                                        {{ Form::radio($compensation.'_installments[1][deadline]', 'custom', null, ['class' => 'form-check-input '.$compensation.'_deadline_custom_radio', 'checked' => true]) }}
                                        Upiši rok
                                        <br/>
                                        {{ Form::fText($model, $compensation.'_installments[1][deadline_custom]', null, null, ['class' => 'form-control '.$compensation.'_deadline_custom', 'placeholder' => 'Npr: u roku od 15 dana od isporuke Autorskog djela od strane Autora', 'autocomplete' => 'off']) }}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <label class="form-check-label" style="width:100%;">
                                        {{ Form::radio($compensation.'_installments[1][deadline]', 'date', null, ['class' => 'form-check-input '.$compensation.'_deadline_date_radio']) }}
                                        Najkasnije dana
                                        <br/>
                                        {{ Form::fText($model, $compensation.'_installments[1][deadline_date]', null, null, ['class' => 'form-control '.$compensation.'_deadline_date', 'data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @else
                @foreach($model->{$compensation.'_installments'} as $_key => $_installment)
                    <div class="card mb-4">
                        <div class="card-header">
                            {{$_key+1}}. dio naknade
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{ Form::fText($model, $compensation."_installments[$_key][amount]", null, 'Iznos', ['placeholder' => 'Npr: 25.000,00', 'data-currency' => 'EUR'], '€') }}
                                </div>
                                <div class="form-group col-lg-12">
                                    <hr/>
                                    <label>
                                        U kojem roku se naručitelj obvezuje platiti autoru {{$_key+1}}. dio naknade{{ $suffix }}?
                                    </label>
                                    <div class="form-check mb-1 mt-1">
                                        <label class="form-check-label" style="width:100%;">
                                            {{ Form::radio($compensation."_installments[$_key][deadline]", 'custom', null, ['class' => 'form-check-input '.$compensation.'_deadline_custom_radio']) }}
                                            Upiši rok
                                            <br/>
                                            {{ Form::fText($model, $compensation."_installments[$_key][deadline_custom]", null, null, ['class' => 'form-control '.$compensation.'_deadline_custom', 'placeholder' => 'Npr: u roku od 15 dana od isporuke Autorskog djela od strane Autora', 'autocomplete' => 'off']) }}
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <label class="form-check-label" style="width:100%;">
                                            {{ Form::radio($compensation."_installments[$_key][deadline]", 'date', null, ['class' => 'form-check-input '.$compensation.'_deadline_date_radio']) }}
                                            Najkasnije dana
                                            <br/>
                                            {{ Form::fText($model, $compensation."_installments[$_key][deadline_date]", null, null, ['class' => 'form-control '.$compensation.'_deadline_date', 'data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>
    </div>
</div>

<div id="{{ $compensation }}_type_unit_container" style="@if(!isset($model->{$compensation.'_type'}) || $model->{$compensation.'_type'} != 'unit') display:none; @endif">
    <hr/>
    <div class="row">
        <div class="col-lg-6 form-group">
            {{ Form::fText($model, 'unit_'.$compensation.'_amount', null, '<span class="dot"></span> Upiši bruto jedinični iznos autorske naknade', ['placeholder' => 'Npr: 100,00', 'data-currency' => 'EUR'], '€') }}
        </div>
        <div class="col-lg-6 form-group">
            {{ Form::fText($model, 'unit_'.$compensation.'_unit', null, '<span class="dot"></span> Upiši jedinicu po kojoj se obračunava naknada', ['placeholder' => 'Npr: kartici teksta'], 'po') }}
        </div>
    </div>
    <hr/>
    <div class="row">
        <div class="form-group col-lg-12">
            {{ Form::fLabel($model, $compensation.'_deadline', '<span class="dot"></span> U kojem roku se naručitelj obvezuje platiti autoru '. ($suffix ? "autorsku naknadu $suffix" : "ukupnu autorsku naknadu") .'?') }}
            <div class="form-check mb-1 mt-1">
                <label class="form-check-label" style="width:100%;">
                    {{ Form::radio('unit_'.$compensation.'_deadline', 'custom', null, ['class' => 'form-check-input '.$compensation.'_deadline_custom_radio', 'checked' => !isset($model->{'unit_'.$compensation.'_deadline'})]) }}
                    Upiši rok
                    <br/>
                    {{ Form::fText($model, 'unit_'.$compensation.'_deadline_custom', null, null, ['class' => 'form-control '.$compensation.'_deadline_custom', 'placeholder' => 'Npr: u roku od 15 dana od isporuke Autorskog djela od strane Autora', 'autocomplete' => 'off']) }}
                </label>
            </div>
            <div class="form-check">
                <label class="form-check-label" style="width:100%;">
                    {{ Form::radio('unit_'.$compensation.'_deadline', 'date', null, ['class' => 'form-check-input '.$compensation.'_deadline_date_radio']) }}
                    Najkasnije dana
                    <br/>
                    {{ Form::fText($model, 'unit_'.$compensation.'_deadline_date', null, null, ['class' => 'form-control '.$compensation.'_deadline_date', 'data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                </label>
            </div>
        </div>
    </div>
</div>
