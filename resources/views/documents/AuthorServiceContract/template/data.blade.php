@extends($builder->getLayout())
@section('content')
    @php

        // define variables for rendering view
        $data["client_name"] = $builder->get("client_name");
        $data["client_address"] = $builder->get("client_address");
        $data["client_city"] = $builder->get("client_city");
        $data["client_postal_code"] = $builder->get("client_postal_code");
        $data["client_country"] = $builder->get("client_country");
        $data["client_oib"] = $builder->get("client_oib");
        $data["client_is_business"] = $builder->get("client_is_business", false);

        if($data["client_is_business"] === null){
            $data["client_is_business"] = 1;
        }

        $data["authorized_client_persons"] = $builder->get("authorized_client_persons", false);

        // standardize array keys
        if(!empty($data['authorized_client_persons'])){
            $data['authorized_client_persons'] = array_values($data['authorized_client_persons']);

            // default case
            foreach($data['authorized_client_persons'] as &$_authorized_client_person){
                if(empty($_authorized_client_person['name'])) $_authorized_client_person['name'] = $builder->get('placeholder');
                if(empty($_authorized_client_person['role'])) $_authorized_client_person['role'] = $builder->get('placeholder');

                $_authorized_client_person['has_entered_address'] = !empty($_authorized_client_person['address'])
                || !empty($_authorized_client_person['city'])
                || !empty($_authorized_client_person['postal_code']);
            }

			// always unset when looping by reference!
            unset($_authorized_client_person);
        }

        $data["author_name"] = $builder->get("author_name");
        $data["author_address"] = $builder->get("author_address");
        $data["author_city"] = $builder->get("author_city");
        $data["author_postal_code"] = $builder->get("author_postal_code");
        $data["author_country"] = $builder->get("author_country");
        $data["author_oib"] = $builder->get("author_oib");

		$data["service_description"] = $builder->get("service_description", false);

        $data["service_responsibilities"] = $builder->get("service_responsibilities", false);
		$data['service_responsibilities'] = \App\Helpers\ArrayHelper::standardizeArrayKeys($data['service_responsibilities']);

        $data["service_deadline"] = $builder->get("service_deadline", false);
        $data["service_deadline_date"] = $builder->get("service_deadline_date");
        $data["service_deadline_time_date"] = $builder->get("service_deadline_time_date");
        $data["service_deadline_time_hour"] = $builder->get("service_deadline_time_hour");
        $data["service_deadline_time_minute"] = $builder->get("service_deadline_time_minute");
        $data["service_deadline_custom"] = $builder->get("service_deadline_custom");
        $data["service_use_type_specific"] = $builder->get("service_use_type_specific", false);
        $data["service_use_type_specific_cases"] = $builder->get("service_use_type_specific_cases", false);

        // standardize array keys
        $data['service_use_type_specific_cases'] = \App\Helpers\ArrayHelper::standardizeArrayKeys($data['service_use_type_specific_cases']);

        $data["service_exclusive_rights"] = $builder->get("service_exclusive_rights", false);
        $data["author_maintains_service_rights"] = $builder->get("author_maintains_service_rights", false);
        $data["service_has_content_use_restrictions"] = $builder->get("service_has_content_use_restrictions", false);
        $data["service_content_use_restrictions"] = $builder->get("service_content_use_restrictions", false);

        // standardize array keys
        $data['service_content_use_restrictions'] = \App\Helpers\ArrayHelper::standardizeArrayKeys($data['service_content_use_restrictions']);

        $data["service_has_time_use_restrictions"] = $builder->get("service_has_time_use_restrictions", false);
        $data["service_time_use_restrictions"] = $builder->get("service_time_use_restrictions", false);
        $data["service_time_use_restrictions_from_date"] = $builder->get("service_time_use_restrictions_from_date");
        $data["service_time_use_restrictions_to_date"] = $builder->get("service_time_use_restrictions_to_date");
        $data["service_time_use_restrictions_custom"] = $builder->get("service_time_use_restrictions_custom");

        $data["service_has_location_use_restrictions"] = $builder->get("service_has_location_use_restrictions", false);
        $data["service_location_use_restrictions"] = $builder->get("service_location_use_restrictions", false);
        $data["service_location_use_restrictions_custom"] = $builder->get("service_location_use_restrictions_custom");

        $data["service_has_3rd_party_use_transfer_permission"] = $builder->get("service_has_3rd_party_use_transfer_permission", false);
        $data["service_has_3rd_party_rights_transfer_permission"] = $builder->get("service_has_3rd_party_rights_transfer_permission", false);

        $data["singular_compensation"] = $builder->get("singular_compensation", false);
        if($data["singular_compensation"] === null) $data["singular_compensation"] = 1;

        foreach(['compensation', 'creation_compensation', 'usage_compensation'] as $_compensation) {
            $data[$_compensation."_type"] = $builder->get($_compensation."_type", false) ?: "fixed";
            $data[$_compensation."_gross_amount"] = $builder->get($_compensation."_gross_amount");
            $data[$_compensation."_in_installments"] = $builder->get($_compensation."_in_installments", false);

            $data["fixed_".$_compensation."_deadline"] = $builder->get("fixed_".$_compensation."_deadline", false) ?: "custom";
            $data["fixed_".$_compensation."_deadline_custom"] = $builder->get("fixed_".$_compensation."_deadline_custom");
            $data["fixed_".$_compensation."_deadline_date"] = $builder->get("fixed_".$_compensation."_deadline_date");
            $data[$_compensation."_installments"] = $builder->get($_compensation."_installments", false);

            // standardize array keys
            $data[$_compensation.'_installments'] = \App\Helpers\ArrayHelper::standardizeArrayKeys($data[$_compensation.'_installments']);

            $data["unit_".$_compensation."_amount"] = $builder->get("unit_".$_compensation."_amount");
            $data["unit_".$_compensation."_unit"] = $builder->get("unit_".$_compensation."_unit");
            $data["unit_".$_compensation."_deadline"] = $builder->get("unit_".$_compensation."_deadline", false);
            $data["unit_".$_compensation."_deadline_custom"] = $builder->get("unit_".$_compensation."_deadline_custom");
            $data["unit_".$_compensation."_deadline_date"] = $builder->get("unit_".$_compensation."_deadline_date");
        }

        $data['compensation_data'] = [['compensation' => 'compensation', 'suffix' => null]];

        if(!$data['singular_compensation']) {
                $data['compensation_data'] = [
                    ['compensation' => 'creation_compensation', 'suffix' => ' za stvaranje autorskog djela'],
                    ['compensation' => 'usage_compensation', 'suffix' => ' za iskorištavanje autorskog djela'],
                ];
        }

        $data["author_is_taxpayer"] = $builder->get("author_is_taxpayer", false);
        $data["tax_expenses_responsibility"] = $builder->get("tax_expenses_responsibility");
        $data["compensation_iban"] = $builder->get("compensation_iban");

        $data["is_confidential"] = $builder->get("is_confidential", false);
        $data["has_deadline_penalty"] = $builder->get("has_deadline_penalty", false);
        $data["deadline_penalty"] = $builder->get("deadline_penalty");

        $data["contract_copy_count"] = $builder->get("contract_copy_count");
        $data["client_contract_copy_count"] = $builder->get("client_contract_copy_count");
        $data["author_contract_copy_count"] = $builder->get("author_contract_copy_count");
        $data["contract_date"] = $builder->get("contract_date");
        $data["contract_place"] = $builder->get("contract_place");
        $data["custom_provisions"] = $builder->get("custom_provisions", false);

    @endphp

    @include($builder->render_view, ['builder' => $builder, 'data' => $data])

@endsection
