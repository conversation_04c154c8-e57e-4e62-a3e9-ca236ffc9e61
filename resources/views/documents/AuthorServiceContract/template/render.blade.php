<div class="editable-segment" data-section="parties" data-type="parties">
    <p>

        <strong>{!! $data['client_name'] !!},</strong> {!! $data['client_address'] !!}, {!! $data['client_postal_code'] !!} {!! $data['client_city'] !!}, {!! $data['client_country'] !!}, OIB: {!! $data['client_oib'] !!},


        @if(!empty($data['authorized_client_persons']))
            @if(count($data['authorized_client_persons']) == 1)
                kao naručitelj kojeg zastupa
            @else
                kao naručitelj kojeg zastupaju
            @endif

            @php // using <span></span> elements allows us to chain blade directives without spacing @endphp

            @foreach($data['authorized_client_persons'] as $_i => $_authorized_person)
                {!! $_authorized_person['role'] !!} {!! $_authorized_person['name']  !!}<span></span>@if($_authorized_person['has_entered_address']),
                {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder !!},
                {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder !!}@endif<span></span>@if(!empty($_authorized_person['oib'])), OIB: {!! $_authorized_person['oib'] !!}@endif<span></span>@if($_i < (count($data['authorized_client_persons'])-1))@if(($_i+2) == count($data['authorized_client_persons']))
                    i @else, @endif @else, @endif
            @endforeach
            <br><br>(u daljnjem tekstu: <strong>Naručitelj</strong>)
        @elseif($builder->partiesEmpty())
            kao naručitelj kojeg zastupa {!! $builder::$placeholder !!} {!! $builder::$placeholder !!},
            <br><br>(u daljnjem tekstu: <strong>Naručitelj</strong>)
        @else
            kao naručitelj <br><br>(u daljnjem tekstu: <strong>Naručitelj</strong>)
        @endif


    </p>

    <p>i</p>

    <p>

        <strong>{!! $data['author_name'] !!},</strong> {!! $data['author_address'] !!}, {!! $data['author_postal_code'] !!} {!! $data['author_city'] !!}, {!! $data['author_country'] !!}, OIB: {!! $data['author_oib'] !!},
        kao autor <br><br>(u daljnjem tekstu: <strong>Autor</strong>),
    </p>

    <p>
        sklopili su sljedeći:
    </p>
</div>

<h2 class="text-center editable-segment" data-type="title">
    UGOVOR O STVARANJU AUTORSKOG DJELA PO NARUDŽBI
</h2>

@php $dynamic_index = 1; @endphp

<div class="editable-segment" data-section="service" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Autorsko djelo koje je predmet ugovora
    </p>

    <div class="article-body">
        @php $p_index = 1; @endphp
        @if(!empty($data['service_responsibilities']))
            @if(count($data['service_responsibilities']) == 1)
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Ovim Ugovorom Autor se obvezuje po narudžbi Naručitelja {!! $data['service_responsibilities'][0] !!} (u daljnjem tekstu: Autorsko djelo).
                </p>
            @else
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Ovim Ugovorom Autor se obvezuje po narudžbi Naručitelja:
                </p>
                <ul>
                    @foreach($data['service_responsibilities'] as $_service_responsibility)
                        <li>{!! $_service_responsibility !!}</li>
                    @endforeach
                </ul>
                <p>
                    (u daljnjem tekstu: Autorsko djelo).
                </p>
            @endif
        @else
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}. Ovim Ugovorom Autor se obvezuje po narudžbi Naručitelja {!! $builder::$placeholder !!} (u daljnjem tekstu: Autorsko djelo).
            </p>
        @endif
    </div>

</div>

@if(!empty(trim($data["service_description"])))
    <div class="editable-segment" data-type="article">
        <p class="article-header">
            Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Obilježja naručenog autorskog djela
        </p>
        <div class="article-body">
            @php
                $p_index = 1;

                $_service_description_content = App\Helpers\StringHelper::wordpressContent($data["service_description"]);

                foreach(explode("<p>", $_service_description_content) as $_p) {
                    if(!empty(trim($_p))){
                        echo "<p> ".$builder->getCurrentArticleIndex(). "." .$p_index++.". $_p";
                    }
                }
            @endphp
        </div>
    </div>
@endif

<div class="editable-segment" data-type="article">

    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Rok predaje autorskog djela
    </p>

    <div class="article-body">

        @php $p_index = 1; @endphp
        @if($data['service_deadline'] == 'custom')
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Autor se obvezuje predati odnosno isporučiti Autorsko djelo Naručitelju {!! $data['service_deadline_custom'] !!}.
            </p>
        @elseif($data['service_deadline'] == 'time')
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Autor se obvezuje predati odnosno isporučiti Autorsko djelo Naručitelju dana {!! $data['service_deadline_time_date'] !!} godine u
                {!! $data['service_deadline_time_hour'] !!}:{!! $data['service_deadline_time_minute'] !!} sati.
            </p>
        @else
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Autor se obvezuje predati odnosno isporučiti Autorsko djelo Naručitelju najkasnije do {!! $data['service_deadline_date'] !!} godine.
            </p>
        @endif

    </div>
</div>

<div class="editable-segment" data-type="article">

    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Pravo iskorištavanja autorskog djela
    </p>

    <div class="article-body">
        @php $p_index = 1; @endphp
        @if(!$data['service_use_type_specific'] && $data['service_exclusive_rights'] !== "0" && !$data['service_has_content_use_restrictions'] && !$data['service_has_time_use_restrictions'] && !$data['service_has_location_use_restrictions'])
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Potpisom ovog Ugovora Autor za Naručitelja osniva isključivo te sadržajno, vremenski i prostorno neograničeno pravo iskorištavanja Autorskog djela i to na svaki sada i/ili u budućnosti mogući način (u daljnjem tekstu: Pravo iskorištavanja).
            </p>
            @if($data['author_maintains_service_rights'])
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Naručitelj prima na znanje i suglasan je s time da Autor pridržava za sebe pravo korištenja Autorskog djela.
                </p>
            @endif
        @else
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Potpisom ovog Ugovora Autor za Naručitelja osniva @if($data['service_exclusive_rights'] !== "0") isključivo @else neisključivo @endif pravo iskorištavanja
                Autorskog djela i to
                @if(!$data['service_use_type_specific'])
                    na svaki sada i/ili u budućnosti mogući način.
                @else
                    tako da Naručitelj može
                    @if(!empty($data['service_use_type_specific_cases']))
                        @foreach($data['service_use_type_specific_cases'] as $_i => $_case)
                            {!! $_case !!}{{StringHelper::countableConjunction(count($data['service_use_type_specific_cases']), $_i, false, "i", " (u daljnjem tekstu: Pravo iskorištavanja).")}}
                        @endforeach
                    @else
                        {!! $builder::$placeholder !!} (u daljnjem tekstu: Pravo iskorištavanja).
                    @endif
                @endif
            </p>

            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Pravo iskorištavanja sadržajno je
                @if(!$data['service_has_content_use_restrictions'])
                    neograničeno.
                @else
                    ograničeno tako da Naručitelj nema pravo
                    @if(!empty($data['service_content_use_restrictions']))
                        @foreach($data['service_content_use_restrictions'] as $_i => $_restriction)
                            {!! $_restriction !!}{{StringHelper::countableConjunction(count($data['service_content_use_restrictions']), $_i, false, "ni")}}
                        @endforeach
                    @else
                        {!! $builder::$placeholder !!}.
                    @endif
                @endif
                Pravo iskorištavanja
                @if(!$data['service_has_time_use_restrictions'])
                    vremenski je neograničeno.
                @else
                    @if($data['service_time_use_restrictions'] == 'one_year_from_signing_contract')
                        vremenski je ograničeno na godinu dana od dana sklapanja ovog Ugovora.
                    @elseif($data['service_time_use_restrictions'] == 'five_years_from_signing_contract')
                        vremenski je ograničeno na pet godina od dana sklapanja ovog Ugovora.
                    @elseif($data['service_time_use_restrictions'] == 'from_to_date')
                        vremenski je ograničeno na razdoblje od {!! $data['service_time_use_restrictions_from_date'] !!} godine do {!! $data['service_time_use_restrictions_to_date'] !!} godine.
                    @elseif($data['service_time_use_restrictions'] == 'custom')
                        vremenski je ograničeno {!! $data['service_time_use_restrictions_custom'] !!}.
                    @else
                        vremenski je ograničeno na godinu dana od dana sklapanja ovog Ugovora.
                    @endif
                @endif
                Pravo iskorištavanja
                @if(!$data['service_has_location_use_restrictions'])
                    prostorno je neograničeno.
                @else
                    @if($data['service_location_use_restrictions'] == 'croatia')
                        prostorno je ograničeno na Republiku Hrvatsku.
                    @elseif($data['service_location_use_restrictions'] == 'eu')
                        prostorno je ograničeno na Europsku uniju.
                    @elseif($data['service_location_use_restrictions'] == 'custom')
                        prostorno je ograničeno {!! $data['service_location_use_restrictions_custom'] !!}.
                    @else
                        prostorno je ograničeno na Republiku Hrvatsku.
                    @endif

                @endif
            </p>

            @if($data['service_exclusive_rights'] !== "0" && $data['author_maintains_service_rights'])
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Naručitelj prima na znanje i suglasan je s time da Autor pridržava za sebe pravo korištenja Autorskog djela.
                </p>
            @endif
        @endif

        @if($data['service_has_3rd_party_use_transfer_permission'] !== "0" && $data['service_has_3rd_party_rights_transfer_permission'] !== "0")
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Potpisom ovog Ugovora Autor daje Naručitelju pisanu suglasnost da Pravo iskorištavanja prenosi dalje na treće osobe i da za treće osobe osniva daljnja prava iskorištavanja.
            </p>
        @elseif($data['service_has_3rd_party_use_transfer_permission'] !== "0" && !$data['service_has_3rd_party_rights_transfer_permission'])
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Potpisom ovog Ugovora Autor daje Naručitelju pisanu suglasnost da Pravo iskorištavanja prenosi dalje na treće osobe.
            </p>
        @elseif(!$data['service_has_3rd_party_use_transfer_permission'] && $data['service_has_3rd_party_rights_transfer_permission'] !== "0")
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Potpisom ovog Ugovora Autor daje Naručitelju pisanu suglasnost da za treće osobe u okvirima ugovorenog Prava iskorištavanja osniva daljnja prava iskorištavanja.
            </p>
        @endif
    </div>

</div>

@foreach($data['compensation_data'] as $_c)
    <div class="editable-segment" data-section="financial_information" data-type="article">

        <p class="article-header">
            Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Autorska naknada{{$_c['suffix']}}
        </p>

        <div class="article-body">
            @php $p_index = 1; @endphp
            @if($data[$_c['compensation'].'_type'] == 'unit')
                @if($data["author_is_taxpayer"])
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Naručitelj se za Autorsko djelo obvezuje Autoru platiti naknadu{{$_c['suffix']}} u bruto iznosu od {!! StringHelper::amountToText($data["unit_".$_c['compensation']."_amount"]) !!} po {!! $data["unit_".$_c['compensation']."_unit"] !!} + PDV (u daljnjem tekstu: Autorska naknada{{$_c['suffix']}}).
                    </p>
                @else
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Naručitelj se za Autorsko djelo obvezuje Autoru platiti naknadu{{$_c['suffix']}} u bruto iznosu od {!! StringHelper::amountToText($data["unit_".$_c['compensation']."_amount"]) !!} po {!! $data["unit_".$_c['compensation']."_unit"] !!} (u daljnjem tekstu: Autorska naknada{{$_c['suffix']}}).
                    </p>
                @endif

                @if($data['client_is_business'] || (!$data['client_is_business'] && $data["tax_expenses_responsibility"] == 'client'))
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Naručitelj se obvezuje Autoru platiti Autorsku naknadu{{$_c['suffix']}} u cijelosti @if($data['unit_'.$_c['compensation'].'_deadline'] == 'custom') {!! $data['unit_'.$_c['compensation'].'_deadline_custom'] !!},
                        @else najkasnije dana {!! $data['unit_'.$_c['compensation'].'_deadline_date'] !!} godine,@endif i to tako da neto iznos Autorske naknade{{$_c['suffix']}} @if($data["author_is_taxpayer"]) i iznos PDV-a @endif plati Autoru na žiro račun IBAN:
                        {!! $data['compensation_iban'] !!}, a odgovarajući porez, prirez, doprinose i eventualna druga javna davanja plati na zakonom
                        propisane račune te u vezi s time podnese propisana izvješća nadležnim državnim tijelima.
                    </p>

                @elseif(!$data['client_is_business'] && $data["tax_expenses_responsibility"] == 'author')
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Naručitelj se obvezuje Autoru platiti Autorsku naknadu{{$_c['suffix']}} u cijelosti @if($data['unit_'.$_c['compensation'].'_deadline'] == 'custom') {!! $data['unit_'.$_c['compensation'].'_deadline_custom'] !!},
                        @else najkasnije dana {!! $data['unit_'.$_c['compensation'].'_deadline_date'] !!} godine,@endif i to tako da ukupan trošak (bruto 2 iznos) Autorske naknade{{$_c['suffix']}} @if($data["author_is_taxpayer"]) i iznos PDV-a @endif plati Autoru na žiro račun IBAN:
                        {!! $data['compensation_iban'] !!}, a Autor se obvezuje platiti odgovarajući porez, prirez, doprinose i eventualna druga
                        javna davanja na zakonom propisane račune te u vezi s time podnijeti propisana izvješća nadležnim državnim tijelima.
                    </p>
                @endif

            @else
                @if($data["author_is_taxpayer"])
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Naručitelj se za Autorsko djelo obvezuje Autoru platiti naknadu{{$_c['suffix']}} u ukupnom bruto iznosu od {!! StringHelper::amountToText($data[$_c['compensation']."_gross_amount"]) !!} + PDV (u daljnjem tekstu: Autorska naknada{{$_c['suffix']}}).
                    </p>
                @else
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Naručitelj se za Autorsko djelo obvezuje Autoru platiti naknadu{{$_c['suffix']}} u ukupnom bruto iznosu od {!! StringHelper::amountToText($data[$_c['compensation']."_gross_amount"]) !!} (u daljnjem tekstu: Autorska naknada{{$_c['suffix']}}).
                    </p>
                @endif

                @if(!$data[$_c['compensation'].'_in_installments'])
                    @if($data['client_is_business'] || (!$data['client_is_business'] && $data["tax_expenses_responsibility"] == 'client'))
                        <p>
                            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                            Naručitelj se obvezuje Autoru platiti Autorsku naknadu{{$_c['suffix']}} u cijelosti @if($data['fixed_'.$_c['compensation'].'_deadline'] == 'custom') {!! $data['fixed_'.$_c['compensation'].'_deadline_custom'] !!},
                            @else najkasnije dana {!! $data['fixed_'.$_c['compensation'].'_deadline_date'] !!} godine,@endif i to tako da neto iznos Autorske naknade{{$_c['suffix']}} @if($data["author_is_taxpayer"]) i iznos PDV-a @endif plati Autoru na žiro račun IBAN:
                            {!! $data['compensation_iban'] !!}, a odgovarajući porez, prirez, doprinose i eventualna druga javna davanja plati na
                            zakonom propisane račune te u vezi s time podnese propisana izvješća nadležnim državnim tijelima.
                        </p>
                    @else
                        <p>
                            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                            Naručitelj se obvezuje Autoru platiti Autorsku naknadu{{$_c['suffix']}} u cijelosti @if($data['fixed_'.$_c['compensation'].'_deadline'] == 'custom') {!! $data['fixed_'.$_c['compensation'].'_deadline_custom'] !!},
                            @else najkasnije dana {!! $data['fixed_'.$_c['compensation'].'_deadline_date'] !!} godine,@endif i to tako da ukupan trošak (bruto 2 iznos) Autorske naknade{{$_c['suffix']}} @if($data["author_is_taxpayer"]) i iznos PDV-a @endif plati
                            Autoru na žiro račun IBAN: {!! $data['compensation_iban'] !!}, a Autor se obvezuje platiti odgovarajući porez, prirez,
                            doprinose i eventualna druga javna davanja na zakonom propisane račune te u vezi s time podnijeti propisana
                            izvješća nadležnim državnim tijelima.
                        </p>
                    @endif
                @else
                    @if($data['client_is_business'] || (!$data['client_is_business'] && $data["tax_expenses_responsibility"] == 'client'))
                        <p>
                            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                            Ugovorne stranke suglasno utvrđuju sljedeću dinamiku plaćanja Autorske naknade{{$_c['suffix']}}:
                        </p>
                        @if(!empty($data[$_c['compensation'].'_installments']))
                            <ul>
                                @foreach($data[$_c['compensation'].'_installments'] as $_k => $_installment)
                                    <li>
                                        {{ $_k+1 }}. dio Autorske naknade{{$_c['suffix']}} u bruto iznosu od {!! !empty($_installment['amount']) ? StringHelper::amountToText($_installment['amount']) : ($builder::$placeholder." EUR") !!} @if($data["author_is_taxpayer"]) + PDV @endif Naručitelj se Autoru obvezuje platiti
                                        @if($_installment['deadline'] == 'custom')
                                            {!! $_installment['deadline_custom'] ?: $builder::$placeholder !!}{!! $_k == (count($data[$_c['compensation'].'_installments']) - 1) ? "." : null !!}
                                        @else
                                            najkasnije dana {!! $_installment['deadline_date'] ? ($_installment['deadline_date'] . " godine") : $builder::$placeholder !!}{!! $_k == (count($data[$_c['compensation'].'_installments']) - 1) ? "." : null !!}
                                        @endif
                                    </li>
                                @endforeach
                            </ul>
                        @endif

                        <p>
                            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                            Navedene iznose Naručitelj se obvezuje platiti Autoru tako da neto iznos Autorske naknade{{$_c['suffix']}}
                            @if($data["author_is_taxpayer"]) i iznos PDV-a @endif plati Autoru na žiro račun IBAN: {!! $data['compensation_iban'] !!},
                            a odgovarajući porez, prirez, doprinose i eventualna druga javna davanja plati na zakonom propisane račune te
                            u vezi s time podnese propisana izvješća nadležnim državnim tijelima.
                        </p>

                    @else
                        <p>
                            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                            Ugovorne stranke suglasno utvrđuju sljedeću dinamiku plaćanja Autorske naknade{{$_c['suffix']}}:
                        </p>

                        @if(!empty($data[$_c['compensation'].'_installments']))
                            <ul>
                                @foreach($data[$_c['compensation'].'_installments'] as $_k => $_installment)
                                    <li>
                                        {{ $_k+1 }}. dio Autorske naknade{{$_c['suffix']}} u bruto iznosu od {!! !empty($_installment['amount']) ? StringHelper::amountToText($_installment['amount']) : ($builder::$placeholder." EUR") !!} @if($data["author_is_taxpayer"]) + PDV @endif Naručitelj se Autoru obvezuje platiti
                                        @if($_installment['deadline'] == 'custom')
                                            {!! $_installment['deadline_custom'] !!}{!! $_k == (count($data[$_c['compensation'].'_installments']) - 1) ? "." : null !!}
                                        @else
                                            najkasnije dana {!! $_installment['deadline_date'] !!} godine{!! $_k == (count($data[$_c['compensation'].'_installments']) - 1) ? "." : null !!}
                                        @endif
                                    </li>
                                @endforeach
                            </ul>
                        @endif

                        <p>
                            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                            Navedene iznose Naručitelj se obvezuje platiti Autoru tako da ukupan trošak (bruto 2 iznos) Autorske naknade{{$_c['suffix']}}
                            @if($data["author_is_taxpayer"]) i iznos PDV-a @endif plati Autoru na žiro račun IBAN: {!! $data['compensation_iban'] !!},
                            a Autor se obvezuje platiti odgovarajući porez, prirez, doprinose i eventualna druga javna davanja na zakonom
                            propisane račune te u vezi s time podnijeti propisana izvješća nadležnim državnim tijelima.
                        </p>
                    @endif
                @endif
            @endif
        </div>
    </div>
@endforeach

<div class="editable-segment" data-section="additional_provisions" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Jamstvo autora
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Potpisom ovog Ugovora Autor izričito jamči Naručitelju da je Autorsko djelo njegova vlastita originalna intelektualna tvorevina, da ima pravo sklopiti ovaj Ugovor i osnovati za Naručitelja Pravo iskorištavanja u pogledu Autorskog djela, da na Autorskom djelu ne postoji nikakvo pravo trećega koje isključuje, umanjuje ili ograničuje prava Naručitelja, da Autorsko djelo nije opterećeno nikakvim teretima, da Autorsko djelo nije predmet spora u parničnom, ovršnom, upravnom i/ili drugom postupku na temelju kojih bi mogla biti ograničena prava Naručitelja te se obvezuje Naručitelju naknaditi svu štetu koja mu nastane kršenjem ove odredbe.
        </p>
    </div>
</div>

@if($data['is_confidential'])
    <div class="editable-segment" data-type="article">
        <p class="article-header">
            Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Obveza čuvanja povjerljivosti
        </p>
        <div class="article-body">
            @php $p_index = 1; @endphp
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Autor se obvezuje za vrijeme trajanja ovog Ugovora i nakon toga, bez vremenskog ograničenja, u tajnosti čuvati svu dokumentaciju, podatke, informacije, spoznaje o i/ili vezane uz Naručitelja, bilo poslovne ili osobne prirode, uključujući, između ostalog, sve što se može smatrati osobnim podacima sukladno Uredbi (EU) 2016/679 Europskog parlamenta i Vijeća od 27. travnja 2016. o zaštiti pojedinaca u vezi s obradom osobnih podataka i o slobodnom kretanju takvih podataka te o stavljanju izvan snage Direktive 95/46/EZ (u daljnjem tekstu: GDPR) i sve što se može smatrati poslovnom tajnom, koje dozna u izvršavanju ovog Ugovora.
            </p>
        </div>
    </div>
@else
    @php $builder->skipped_articles[] = $dynamic_index++; @endphp
@endif

@if($data['has_deadline_penalty'])
    <div class="editable-segment" data-type="article">
        <p class="article-header">
            Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Ugovorna kazna
        </p>
        <div class="article-body">
            @php $p_index = 1; @endphp
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Autor se obvezuje Naručitelju platiti iznos od {!! StringHelper::amountToText($data['deadline_penalty']) !!} za svaki dan prekoračenja roka predaje odnosno isporuke Autorskog djela. Obveza plaćanja Ugovorne kazne dospijeva u roku od 8 dana od dostave Autoru pisanog poziva Naručitelja na plaćanje kazne.
            </p>
        </div>
    </div>
@else
    @php $builder->skipped_articles[] = $dynamic_index++; @endphp
@endif

@if(!empty($data['custom_provisions']))
    @foreach($data['custom_provisions'] as $_i => $_custom_provision)
        @if(!empty(trim($_custom_provision['provision'])))
            <div class="editable-segment" data-type="article">
                <p class="article-header">
                    Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. {{ $_custom_provision['title'] }}
                </p>
                <div class="article-body">
                    @php
                        $p_index = 1;

                        $_custom_provision_content = App\Helpers\StringHelper::wordpressContent($_custom_provision['provision']);

                        foreach(explode("<p>", $_custom_provision_content) as $_p) {
                            if(!empty(trim($_p))){
                                echo "<p> ".$builder->getCurrentArticleIndex(). "." .$p_index++.". $_p";
                            }
                        }
                    @endphp
                </div>
            </div>
        @endif
    @endforeach
@endif

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Ništetnost
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ako se bilo koja odredba ovog Ugovora pokaže ništetnom, ostale odredbe ovog Ugovora u cijelosti ostaju na snazi. U slučaju ništetnosti jedne ili više odredaba ovog Ugovora, ugovorne stranke se obvezuju odmah pristupiti zamjeni ništetnih odredaba drugima, vodeći pri tome računa da se izmijenjenim odredbama postigne isti stupanj zadovoljenja interesa ugovornih stranaka, ali na način koji je dopušten.
        </p>
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Izmjene ugovora
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da bilo kakva izmjena, dopuna ili dodatak ovom Ugovoru moraju biti sastavljeni u pisanom obliku, valjano potpisani i odobreni od svih ugovornih stranaka, a eventualni usmeni dogovori moraju biti pisano potvrđeni.
        </p>
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Mjerodavno pravo
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da se na sva pitanja koja proizlaze iz ili u vezi s ovim Ugovorom koja nisu izrijekom uređena u njemu primjenjuju odgovarajući propisi na snazi u Republici Hrvatskoj.
        </p>
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Rješavanje sporova
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da će sve sporove koji nastanu iz ili u vezi s ovim Ugovorom koje ne mogu riješiti mirnim putem rješavati stvarno i mjesno nadležni sud.
        </p>
    </div>
</div>

<div class="editable-segment" data-section="final_provisions" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Broj primjeraka ugovora
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ovaj Ugovor sklapa se u {!! $data['contract_copy_count'] !!} {{ StringHelper::getPrimjerakaString($data['contract_copy_count']) }}, od kojih {!! $data['client_contract_copy_count'] !!} zadržava Naručitelj,
            a {!! $data['author_contract_copy_count'] !!} zadržava Autor.
        </p>
    </div>
</div>

@include('layouts.document.partials.signatures', [
  'data' => $data,
  'builder' => $builder,
  'dynamic_index' => $dynamic_index,
  'text' => 'Ugovorne stranke su suglasne da je u odredbama ovog Ugovora sadržana njihova prava i stvarna volja te ga u znak prihvata prava i obveza koje iz Ugovora proizlaze vlastoručno potpisuju.',
  'left_parties' => $builder->getParties()->where('side', 'left')->values()->toArray(),
  'right_parties' => $builder->getParties()->where('side', 'right')->values()->toArray(),
  'default_party_label_left' => 'Naručitelj',
  'default_party_label_right' => 'Autor',
])
