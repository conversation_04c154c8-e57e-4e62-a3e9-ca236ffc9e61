<h2 class="text-center editable-segment mb-0 pb-0" data-type="title">
    IZJAVA O SUGLASNOSTI
</h2>
<div class="editable-segment" data-type="body">
    <p class="text-center">kojom ja,</p>
    <p>
        <strong>{!! $data['grantor_name'] !!},</strong> {!! $data['grantor_address'] !!}
        , {!! $data['grantor_postal_code'] !!}
        {!! $data['grantor_city'] !!}, {!! $data['grantor_country'] !!}, OIB: {!! $data['grantor_oib'] !!}, kao davatelj
        suglasnosti (u daljnjem tekstu: <strong>Davatelj suglasnosti</strong>)
    </p>
    <p class="text-center">
        dajem suglasnost da moj {!! $data['grantee_type'] !!}
    </p>
    <p>
        <strong>{!! $data['grantee_name'] !!},</strong> {!! $data['grantee_address'] !!}
        , {!! $data['grantee_postal_code'] !!}
        {!! $data['grantee_city'] !!}, {!! $data['grantee_country'] !!}, OIB: {!! $data['grantee_oib'] !!}, kao
        primatelj suglasnosti (u daljnjem tekstu: <strong>Primatelj suglasnosti</strong>)
    </p>

    <p>
        vezano za @if(!empty($data['additional_realestates']))
            nekretnine koje predstavljaju
        @else
            nekretninu koja predstavlja
        @endif našu {!! $data["acquisition_type"]  !!} i to:
    </p>

    <ul>

        @include('documents.MatrimonialRealestateTransferConsent.template.partials.realestate', [
           'data' => $data,
		   'grantee_has_full_realestate_ownership' => $data['grantee_has_full_realestate_ownership'] ?? true,
           'is_full_disposal' => $data['is_full_disposal'] ?? true
       ])

        @if(!empty($data['additional_realestates']))

            @foreach($data['additional_realestates'] as $_data)
                @include('documents.MatrimonialRealestateTransferConsent.template.partials.realestate', [
                   'data' => $_data,
				   'grantee_has_full_realestate_ownership' => $_data['grantee_has_full_realestate_ownership'] ?? true,
                   'is_full_disposal' => $_data['is_full_disposal'] ?? true
               ])
            @endforeach

        @endif

    </ul>

    <p>
        može samostalno poduzeti sljedeće izvanredne poslove u smislu članka 32. stavka (2) i članka 37. stavka (2)
        trenutno važećeg Obiteljskog zakona:
    </p>

    <ul>

        @if(!empty($data['grantee_authorities']))
            @foreach($data['grantee_authorities'] as $_i => $_grantee_authority)
                <li>{!! $_grantee_authority !!}{!! $_i == (count($data['grantee_authorities']) - 1)  ? "." : null !!}</li>
            @endforeach
        @else
            <li>
                {!! $builder::$placeholder !!}
            </li>
        @endif

    </ul>

    @if(!empty($data['custom_provisions']))
        @foreach($data['custom_provisions'] as $_custom_provision)
            @if(!empty($_custom_provision['provision']))
                @php
                    $p_index = 1;

                    $_custom_provision_content = App\Helpers\StringHelper::wordpressContent($_custom_provision['provision']);

                    foreach(explode("<p>", $_custom_provision_content) as $_p) {
                        if(!empty(trim($_p))){
                            echo "<p> $_p";
                        }
                    }
                @endphp
            @endif

        @endforeach
    @endif

    <div class="avoid-page-break">
        <p>
            Ova Suglasnost vrijedi
            @switch($data['consent_form_validity_period'])
                @case('until_revoked')
                    do opoziva.
                    @break
                @case('until_date')
                    do dana {!! $data['consent_form_validity_period_until_date'] !!} godine.
                    @break
                @case('custom')
                    {!! $data['consent_form_validity_period_custom'] !!}.
                    @break
                @default
                    do opoziva.
            @endswitch
        </p>

        <p>
            {!! $data['consent_form_place'] !!}, dana {!! StringHelper::dateToText($data['consent_form_date'], $builder::$placeholder) !!} godine.
        </p>

        <table id="signatures-segment" style="table-layout: fixed; width: 100%; overflow: wrap;" autosize="1">
            @if(!empty($data['parties']))
                @foreach($data['parties'] as $_i => $_party)
                    <tr class="avoid-page-break">
                        <td style="vertical-align:top; width: 50%;">
                            <div style="margin:0; padding: 0;">
                                <img alt="signature" src="{L{{ $_i }}}">
                            </div>
                            <span class="signature-line">_____________________________________________</span><br>
                            <small>{!! $builder->getPartyLabel($_party, 'Davatelj suglasnosti') !!}</small>
                            <br/>
                        </td>
                        <td style="vertical-align:top; padding-left: 5%; width: 50%;">
                        </td>
                    </tr>
                @endforeach
            @else
                <tr class="avoid-page-break">
                    <td style="vertical-align:top; width: 50%;">
                        <div style="margin:0; padding: 0;">
                            <img alt="signature" src="{L0}">
                        </div>
                        <span class="signature-line">_____________________________________________</span><br>
                        <small>Davatelj suglasnosti</small>
                        <br/>
                    </td>
                    <td style="vertical-align:top; padding-left: 5%; width: 50%;">
                    </td>
                </tr>
            @endif
        </table>
    </div>

</div>