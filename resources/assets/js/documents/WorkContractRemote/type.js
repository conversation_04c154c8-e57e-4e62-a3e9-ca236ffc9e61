$(document).ready(function () {
    // definite or indefinite contract switch
    $('#definite_contract').click(function () {
        $('#definite_contract_container').removeClass("sliding-up").slideDown();
        $('#indefinite_contract_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");

        // adjust common subsequent contract container
        if ($('#is_subsequent_definite_contract').is(':checked')) {
            $('#common_subsequent_contract_container').removeClass("sliding-up").slideDown();
        } else {
            $('#common_subsequent_contract_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
        }
    });

    $('#indefinite_contract').click(function () {
        $('#indefinite_contract_container').removeClass("sliding-up").slideDown();
        $('#definite_contract_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");

        // adjust common subsequent contract container
        if ($('#is_subsequent_indefinite_contract').is(':checked')) {
            $('#common_subsequent_contract_container').removeClass("sliding-up").slideDown();
        } else {
            $('#common_subsequent_contract_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
        }
    });

    // is first or subsequent indefinite contract
    $('#is_subsequent_indefinite_contract').click(function () {
        $('#common_subsequent_contract_container').removeClass("sliding-up").slideDown();
    });

    $('#is_first_indefinite_contract').click(function () {
        $('#common_subsequent_contract_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });

    // is first or subsequent definite contract
    $('#is_subsequent_definite_contract').click(function () {
        $('#is_subsequent_definite_contract_container').removeClass("sliding-up").slideDown();
        $('#common_subsequent_contract_container').removeClass("sliding-up").slideDown();
    });

    $('#is_first_definite_contract').click(function () {
        $('#is_subsequent_definite_contract_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
        $('#common_subsequent_contract_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });

    // probation period
    $('#is_probation_period').click(function () {
        $('#probation_period_container').removeClass("sliding-up").slideDown();
    });

    $('#is_not_probation_period').click(function () {
        $('#probation_period_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });


});