$(document).ready(function () {

    let granteeShareTooltips = {
        'particular': {
            'single': {
                text: "Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.",
                link: "/documents/MatrimonialRealestateTransferConsent/tooltips/particular/9-1.jpeg",
            },
            'multiple': {
                text: "Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.",
                link: "/documents/MatrimonialRealestateTransferConsent/tooltips/particular/9-2.jpeg",
            }
        },
        'flat': {
            'single': {
                text: "Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.",
                link: "/documents/MatrimonialRealestateTransferConsent/tooltips/flat/9-1.jpeg",
            },
            'multiple': {
                text: "Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.",
                link: "/documents/MatrimonialRealestateTransferConsent/tooltips/flat/9-2.jpeg",
            }
        },
        'other': {
            'single': {
                text: "Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.",
                link: "/documents/MatrimonialRealestateTransferConsent/tooltips/other/9-1.jpeg",
            },
            'multiple': {
                text: "Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.",
                link: "/documents/MatrimonialRealestateTransferConsent/tooltips/other/9-2.jpeg",
            }
        }
    };

    function bindCourtLandRegistrySelect2() {

        if ($('.court-select2:visible').length) {
            $('.court-select2:visible').select2({
                language: {
                    errorLoading: function () {
                        return 'Preuzimanje nije uspjelo.';
                    },
                    loadingMore: function () {
                        return 'Učitavam rezultate...';
                    },
                    maximumSelected: function (args) {
                        return 'Maksimalan broj odabranih stavki je ' + args.maximum;
                    },
                    noResults: function () {
                        return 'Nema rezultata';
                    },
                    searching: function () {
                        return 'Pretražujem...';
                    }
                },
                minimumResultsForSearch: 10,
                placeholder: "Odaberi..."
            });
        }

        if ($('.landRegistry-select2:visible').length) {
            $('.landRegistry-select2:visible').select2({
                language: {
                    errorLoading: function () {
                        return 'Preuzimanje nije uspjelo.';
                    },
                    loadingMore: function () {
                        return 'Učitavam rezultate...';
                    },
                    maximumSelected: function (args) {
                        return 'Maksimalan broj odabranih stavki je ' + args.maximum;
                    },
                    noResults: function () {
                        return 'Nema rezultata';
                    },
                    searching: function () {
                        return 'Pretražujem...';
                    }
                },
                minimumResultsForSearch: 10,
                placeholder: "Odaberi ili upiši...",
                tags: true,
                createTag: function (params) {
                    return {
                        id: params.term,
                        text: params.term,
                        newOption: true
                    }
                }
            });
        }
    }

    bindCourtLandRegistrySelect2();

    function bindAddTypeParticularAdditionalLandPlot() {
        $.each($('.add_type_particular_additional_land_plot'), function (i, button) {
            $(button).unbind('click').on('click', function (e) {

                let field_name = $(this).data('field-name');

                let new_item = $('#type_particular_additional_land_plot_template').html()
                    .replace(new RegExp('{INDEX}', 'g'), Date.now())
                    .replace(new RegExp('{FIELD_NAME}', 'g'), field_name);

                $(button).closest('.card').find('.type_particular_additional_land_plot_container').append(new_item);

                bindRemoveTypeParticularAdditionalLandPlot();

                $('.fancybox').fancybox({
                    live: false,
                    helpers: {
                        title: {
                            type: 'inside',
                            position: 'top'
                        }
                    },
                    mobile: {preventCaptionOverlap: true}

                });

                $(function () {
                    $('[data-toggle="tooltip"]').tooltip()
                });

            });
        });
    }

    bindAddTypeParticularAdditionalLandPlot();

    function bindRemoveTypeParticularAdditionalLandPlot() {
        $.each($('.remove_type_particular_additional_land_plot'), function (i, button) {
            $(button).unbind('click').on('click', function (e) {
                $(button).closest('.type_particular_additional_land_plot_content').remove();
                $(document).trigger('updatePreviewEvent');
            });
        });
    }

    bindRemoveTypeParticularAdditionalLandPlot();

    function bindAddTypeOtherAdditionalLandPlot() {
        $.each($('.add_type_other_additional_land_plot'), function (i, button) {
            $(button).unbind('click').on('click', function (e) {

                let field_name = $(this).data('field-name');

                let new_item = $('#type_other_additional_land_plot_template').html()
                    .replace(new RegExp('{INDEX}', 'g'), Date.now())
                    .replace(new RegExp('{FIELD_NAME}', 'g'), field_name);

                $(button).closest('.card').find('.type_other_additional_land_plot_container').append(new_item);

                bindRemoveTypeOtherAdditionalLandPlot();
                bindAddTypeOtherAdditionalPosessionAreaAndIdentification();

                $('.fancybox').fancybox({
                    live: false,
                    helpers: {
                        title: {
                            type: 'inside',
                            position: 'top'
                        }
                    },
                    mobile: {preventCaptionOverlap: true}

                });

                $(function () {
                    $('[data-toggle="tooltip"]').tooltip()
                });

            });
        });
    }

    bindAddTypeOtherAdditionalLandPlot();

    function bindRemoveTypeOtherAdditionalLandPlot() {
        $.each($('.remove_type_other_additional_land_plot'), function (i, button) {
            $(button).unbind('click').on('click', function (e) {
                $(button).closest('.type_other_additional_land_plot_content').remove();
                $(document).trigger('updatePreviewEvent');
            });
        });
    }

    bindRemoveTypeOtherAdditionalLandPlot();

    function bindAddTypeOtherAdditionalPosessionAreaAndIdentification() {
        $.each($('.add_type_other_additional_possession_area_and_identification'), function (i, button) {
            $(button).unbind('click').on('click', function (e) {

                let field_name = $(this).data('field-name');

                let new_item = $('#type_other_additional_possession_area_and_identification_template').html()
                    .replace(new RegExp('{INDEX}', 'g'), Date.now())
                    .replace(new RegExp('{FIELD_NAME}', 'g'), field_name);

                $(button).closest('.card').find('.type_other_additional_possession_area_and_identification_container').append(new_item);

                bindRemoveTypeOtherAdditionalPosessionAreaAndIdentification();

                $('.fancybox').fancybox({
                    live: false,
                    helpers: {
                        title: {
                            type: 'inside',
                            position: 'top'
                        }
                    },
                    mobile: {preventCaptionOverlap: true}

                });

            });
        });
    }

    bindAddTypeOtherAdditionalPosessionAreaAndIdentification();


    function bindRemoveTypeOtherAdditionalPosessionAreaAndIdentification() {
        $.each($('.remove_type_other_additional_possession_area_and_identification'), function (i, button) {
            $(button).unbind('click').on('click', function (e) {

                $(button).closest('.type_other_additional_possession_area_and_identification_content').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");

            });
        });
    }

    bindRemoveTypeOtherAdditionalPosessionAreaAndIdentification();

    // populates question indexes from grantee shares onwards
    function populateDynamicIndex() {
        let realestates = $('.realestate_content:visible');

        $.each(realestates, function (i, realestate) {

            // starting dynamic index
            let dynamic_index = 6;

            if ($(realestate).find('.realestate_type_particular:checked').length) {
                dynamic_index = 6;
            }

            if ($(realestate).find('.realestate_type_flat:checked').length) {
                dynamic_index = 7;
            }

            if ($(realestate).find('.realestate_type_other:checked').length) {
                dynamic_index = 5;
            }

            $.each($(realestate).find('.dynamic_index'), function (j, dynamic_index_span) {
                $(dynamic_index_span).html(dynamic_index);
                dynamic_index++;
            });
        });
    }

    populateDynamicIndex();

    function populateGranteeShareTooltips() {
        // single grantee
        $.each($('.grantee_share:visible'), function (i, grantee_share) {

            // determine realestate type
            let realestate_type = "particular";

            if ($(grantee_share).parents('.realestate_content').find('.realestate_type_flat:checked').length) {
                realestate_type = "flat";
            } else if ($(grantee_share).parents('.realestate_content').find('.realestate_type_other:checked').length) {
                realestate_type = "other";
            }

            // populate tooltips

            $(grantee_share).find('.grantee_has_full_realestate_ownership').siblings('.fancybox').data('caption', granteeShareTooltips[realestate_type]["single"]["text"]);
            $(grantee_share).find('.grantee_has_full_realestate_ownership').siblings('.fancybox').attr('href', granteeShareTooltips[realestate_type]["single"]["link"]);

            $(grantee_share).find('.grantee_has_not_full_realestate_ownership').siblings('.fancybox').data('caption', granteeShareTooltips[realestate_type]["multiple"]["text"]);
            $(grantee_share).find('.grantee_has_not_full_realestate_ownership').siblings('.fancybox').attr('href', granteeShareTooltips[realestate_type]["multiple"]["link"]);

        });
    }

    function bindRealestateListeners() {
        // full ownership label switching
        $('.grantee_has_full_realestate_ownership').unbind('click').on('click', function () {
            $(this).closest('.realestate_content').find('.realestate_ownership_label_nominativ').html('vlasnički');
            $(this).closest('.realestate_content').find('.realestate_ownership_label_instrumental').html('vlasničkim');
        });

        $('.grantee_has_not_full_realestate_ownership').unbind('click').on('click', function () {
            $(this).closest('.realestate_content').find('.realestate_ownership_label_nominativ').html('suvlasnički');
            $(this).closest('.realestate_content').find('.realestate_ownership_label_instrumental').html('suvlasničkim');
        });

        populateGranteeShareTooltips();

        $('.fancybox').fancybox({
            live: false,
            helpers: {
                title: {
                    type: 'inside',
                    position: 'top'
                }
            },
            mobile: {preventCaptionOverlap: true}

        });

        // toggle realestate type
        $('.realestate_type_particular').on('click', function (e) {
            $(this).closest('.realestate_content').find('.realestate_type_flat_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            $(this).closest('.realestate_content').find('.realestate_type_other_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            $(this).closest('.realestate_content').find('.realestate_type_particular_container').removeClass('sliding-up').removeClass("sliding-up").slideDown();

            populateGranteeShareTooltips();
            populateDynamicIndex();
            bindCourtLandRegistrySelect2();

        });

        $('.realestate_type_flat').on('click', function (e) {
            $(this).closest('.realestate_content').find('.realestate_type_particular_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            $(this).closest('.realestate_content').find('.realestate_type_other_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            $(this).closest('.realestate_content').find('.realestate_type_flat_container').removeClass('sliding-up').removeClass("sliding-up").slideDown();


            populateGranteeShareTooltips();
            populateDynamicIndex();
            bindCourtLandRegistrySelect2();

        });

        $('.realestate_type_other').on('click', function (e) {
            $(this).closest('.realestate_content').find('.realestate_type_flat_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            $(this).closest('.realestate_content').find('.realestate_type_particular_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            $(this).closest('.realestate_content').find('.realestate_type_other_container').removeClass('sliding-up').removeClass("sliding-up").slideDown();


            populateGranteeShareTooltips();
            populateDynamicIndex();
            bindCourtLandRegistrySelect2();

        });

        bindAddTypeOtherAdditionalPosessionAreaAndIdentification();
        bindAddTypeOtherAdditionalLandPlot();

        bindAddTypeParticularAdditionalLandPlot();

    }

    function setIndexesOnRealestates() {
        if ($('.realestate_content:visible').length > 1) {
            $('.realestate_index:visible').each(function (index, realestate) {
                $(realestate).html(index + 1);
            });
        } else {
            $('.realestate_index:visible').html('');
        }
    }

    function bindRemoveRealestate() {
        $('.remove_realestate').unbind('click').click(function () {
            let target = $(this).closest('.realestate_content');
            target.remove();
            $(document).trigger('updatePreviewEvent');
            setIndexesOnRealestates();
        });
    }

    bindRealestateListeners();
    bindRemoveRealestate();


    // add new realestate
    $('#add_realestate').on('click', function (e) {

        // prevent propagation if tooltip was clicked
        if($(e.target).is("i")){
            return;
        } // otherwise, hide all tooltips
        else{
            $(".tooltip").tooltip('hide');
        }

        let new_realestate = $('#realestate_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now())
            .replace(new RegExp('{REALESTATE_INDEX}', 'g'), $('.realestate_content:visible').length);

        $('#realestates_container').append(new_realestate);

        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        });

        bindRealestateListeners();
        bindRemoveRealestate();
        setIndexesOnRealestates();
        populateDynamicIndex();
        bindCourtLandRegistrySelect2();

    });


});

// validation has to be declared outside document ready function and assigned to window object
function validate() {

    // remove all existing has-error classes
    $('div').removeClass('has-error');

    // set variables
    let errors = [];
    let realestates = $('.realestate_content:visible');
    let realestate_count = realestates.length;


    // **** VALIDATION RULES ****** //

    // 1. if grantee has partial realestate ownership, the field is required
    // 2. sum of all grantee parts cannot be bigger than 1
    $.each(realestates, function (i, realestate) {

        let grantee_shares_container = $(realestate).find('.grantee_shares_container');
        let realestate_index = i + 1;

        if ($(grantee_shares_container).find('.grantee_share').length) {
            let grantee_share = $(grantee_shares_container).find('.grantee_share');

            if ($(grantee_share).find('.grantee_has_not_full_realestate_ownership:checked').length) {
                let radio = $(grantee_share).find('.grantee_has_not_full_realestate_ownership:checked');
                let numbers = $(radio).closest('div.radio-input').find('input[type=number]');
                let numerator = $(numbers[0]).val();
                let denominator = $(numbers[1]).val();

                if (numerator && denominator) {
                    if (numerator / denominator >= 1) {

                        if (realestate_count > 1) {
                            errors.push("Suvlasnički dio primatelja suglasnosti na nekretnini " + realestate_index + "  mora biti manji od 1.");
                        } else {
                            errors.push("Suvlasnički dio primatelja suglasnosti na nekretnini mora biti manji od 1.");
                        }

                        $(radio).closest('div.radio-input').addClass('has-error');
                    }
                } else {

                    if (realestate_count > 1) {
                        errors.push("Potrebno je upisati suvlasnički dio primatelja suglasnosti na nekretnini " + realestate_index + ".");
                    } else {
                        errors.push("Potrebno je upisati suvlasnički dio primatelja suglasnosti.");
                    }

                    $(radio).closest('div.radio-input').addClass('has-error');
                }
            }
        }
    });
    // **** END VALIDATION RULES ****** //


    // display errors
    if (errors.length) {

        let errors_html = "<strong>Molimo ispravite sljedeće greške:</strong>";
        errors_html += "<ol>";

        $.each(errors, function (i, error) {
            errors_html += "<li>" + error + "</li>";
        });

        errors_html += "</ol>";

        $('#javascript-error').html(errors_html);
        $('#javascript-error-container').removeClass('d-none');

        window.scrollTo(0, 0);

        return false;
    } else {
        $('#javascript-error-container').addClass('d-none');
        return true;
    }

}

window.validate = validate;
