$(document).ready(function () {

    let max_additional_buyers = 10;
    let max_additional_sellers = 10;

    // toggle authorized seller persons
    function bindToggleAuthorizedSellerPersonsExist() {
        $('.authorized_seller_persons_exist').unbind('click').click(function () {

            // if tooltip was clicked, ignore and exit
            if($('.tooltip-inner:visible').length){
                $(this).prop("checked", !$(this).is(':checked'));
                return null;
            }

            if ($(this).is(':checked')) {

                // if none exist yet, add one
                if (!$(this).closest('.authorized_seller_persons').find('.authorized_seller_person_content').length) {
                    $(this).closest('.authorized_seller_persons').find('.add_authorized_seller_person').trigger({
                        type: 'click',
                        programmatic: true
                    });
                }

                $(this).closest('.authorized_seller_persons').find('.authorized_seller_persons_container').removeClass("sliding-up").slideDown();
                $(this).closest('.authorized_seller_persons').find('.add_authorized_seller_person_container').removeClass("sliding-up").slideDown();
            } else {
                $(this).closest('.authorized_seller_persons').find('.authorized_seller_persons_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
                $(this).closest('.authorized_seller_persons').find('.add_authorized_seller_person_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            }
        });
    }

    // add authorized seller person

    function bindAddAuthorizedSellerPerson() {
        $('.add_authorized_seller_person').unbind('click').click(function () {
            let person_id = $(this).data('id');
            let person_index = Date.now();  // we just care that it is unique
            let new_authorized_seller_person = $('#authorized_seller_person_template').html().replace(new RegExp('{ID}', 'g'), person_id).replace(new RegExp('{INDEX}', 'g'), person_index);
            $(this).closest('.authorized_seller_persons').find('.authorized_seller_persons_container').append(new_authorized_seller_person);
            bindRemoveAuthorizedSellerPerson();

            $(function () {
                $('[data-toggle="tooltip"]').tooltip()
            });

            window.bindMapsAutofillInputs($('.maps-autofill-input'));
        });
    }


    // remove authorized seller person
    function bindRemoveAuthorizedSellerPerson() {
        $('.remove_authorized_seller_person').unbind('click').click(function () {
            let target = $(this).closest('.authorized_seller_person_content');

            target.hide('slow', function () {
                // if last one removed, hide divs, uncheck checkbox
                if ($(this).closest('.authorized_seller_persons').find('.remove_authorized_seller_person').length == 1) {
                    $(this).closest('.authorized_seller_persons').find('.authorized_seller_persons_container').hide();
                    $(this).closest('.authorized_seller_persons').find('.add_authorized_seller_person_container').hide();
                    $('.authorized_seller_persons_exist').prop('checked', false);
                }

                target.remove();
            }).addClass("sliding-up");

        });
    }

    bindToggleAuthorizedSellerPersonsExist();
    bindAddAuthorizedSellerPerson();
    bindRemoveAuthorizedSellerPerson();


    // toggle authorized buyer persons

    function bindToggleAuthorizedBuyerPersonsExist() {
        $('.authorized_buyer_persons_exist').unbind('click').click(function () {

            // if tooltip was clicked, ignore and exit
            if($('.tooltip-inner:visible').length){
                $(this).prop("checked", !$(this).is(':checked'));
                return null;
            }

            if ($(this).is(':checked')) {

                // if none exist yet, add one
                if (!$(this).closest('.authorized_buyer_persons').find('.authorized_buyer_person_content').length) {
                    $(this).closest('.authorized_buyer_persons').find('.add_authorized_buyer_person').trigger({
                        type: 'click',
                        programmatic: true
                    });
                }

                $(this).closest('.authorized_buyer_persons').find('.authorized_buyer_persons_container').removeClass("sliding-up").slideDown();
                $(this).closest('.authorized_buyer_persons').find('.add_authorized_buyer_person_container').removeClass("sliding-up").slideDown();
            } else {
                $(this).closest('.authorized_buyer_persons').find('.authorized_buyer_persons_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
                $(this).closest('.authorized_buyer_persons').find('.add_authorized_buyer_person_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            }
        });
    }

    // add authorized buyer person
    function bindAddAuthorizedBuyerPerson() {
        $('.add_authorized_buyer_person').unbind('click').click(function () {
            let person_id = $(this).data('id');
            let person_index = Date.now();  // we just care that it is unique
            let new_authorized_buyer_person = $('#authorized_buyer_person_template').html().replace(new RegExp('{ID}', 'g'), person_id).replace(new RegExp('{INDEX}', 'g'), person_index);
            $(this).closest('.authorized_buyer_persons').find('.authorized_buyer_persons_container').append(new_authorized_buyer_person);
            bindRemoveAuthorizedBuyerPerson();

            $(function () {
                $('[data-toggle="tooltip"]').tooltip()
            });

            window.bindMapsAutofillInputs($('.maps-autofill-input'));
        });
    }


    // remove authorized buyer person
    function bindRemoveAuthorizedBuyerPerson() {
        $('.remove_authorized_buyer_person').unbind('click').click(function () {
            let target = $(this).closest('.authorized_buyer_person_content');

            target.hide('slow', function () {
                // if last one removed, hide divs, uncheck checkbox
                if ($(this).closest('.authorized_buyer_persons').find('.remove_authorized_buyer_person').length == 1) {
                    $(this).closest('.authorized_buyer_persons').find('.authorized_buyer_persons_container').hide();
                    $(this).closest('.authorized_buyer_persons').find('.add_authorized_buyer_person_container').hide();
                    $('.authorized_buyer_persons_exist').prop('checked', false);
                }

                target.remove();
            }).addClass("sliding-up");

        });
    }

    bindToggleAuthorizedBuyerPersonsExist();
    bindAddAuthorizedBuyerPerson();
    bindRemoveAuthorizedBuyerPerson();

    // add additional buyer
    $('#add_additional_buyer').click(function (e) {

        // prevent propagation if tooltip was clicked
        if($(e.target).is("i")){
            return;
        } // otherwise, hide all tooltips
        else{
            $(".tooltip").tooltip('hide');
        }

        if ($('.remove_additional_buyer:visible').length <= max_additional_buyers) {
            let new_buyer = $('#additional_buyer_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now());
            $('#additional_buyers').append(new_buyer);
            bindRemoveAdditionalBuyer();
            bindAddAuthorizedBuyerPerson();
            bindRemoveAuthorizedBuyerPerson();
            bindToggleAuthorizedBuyerPersonsExist();
            window.bindMapsAutofillInputs($('.maps-autofill-input'));

            $(function () {
                $('[data-toggle="tooltip"]').tooltip()
            });
        }
    });

    // remove additional buyer
    function bindRemoveAdditionalBuyer() {
        $('.remove_additional_buyer').unbind('click').click(function () {
            let target = $(this).closest('.additional_buyer_content');

            target.hide('slow', function () {
                target.remove();
            }).addClass("sliding-up");

        });
    }

    bindRemoveAdditionalBuyer();

    // add additional seller
    $('#add_additional_seller').click(function (e) {

        // prevent propagation if tooltip was clicked
        if($(e.target).is("i")){
            return;
        } // otherwise, hide all tooltips
        else{
            $(".tooltip").tooltip('hide');
        }

        if ($('.remove_additional_seller:visible').length <= max_additional_sellers) {
            let new_seller = $('#additional_seller_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now());
            $('#additional_sellers').append(new_seller);
            bindRemoveAdditionalSeller();
            bindAddAuthorizedSellerPerson();
            bindRemoveAuthorizedSellerPerson();
            bindToggleAuthorizedSellerPersonsExist();
            window.bindMapsAutofillInputs($('.maps-autofill-input'));

            $(function () {
                $('[data-toggle="tooltip"]').tooltip()
            });

            bindSellerCosigner();
        }
    });

    // remove additional seller
    function bindRemoveAdditionalSeller() {
        $('.remove_additional_seller').unbind('click').click(function () {
            let target = $(this).closest('.additional_seller_content');

            target.hide('slow', function () {
                target.remove();
            }).addClass("sliding-up");

        });
    }

    bindRemoveAdditionalSeller();

    function bindSellerCosigner() {
        $('.seller_cosigner_exists').unbind('click').click(function () {

            // if tooltip was clicked, ignore and exit
            if($('.tooltip-inner:visible').length){
                $(this).prop("checked", !$(this).is(':checked'));
                return null;
            }

            if ($(this).is(':checked')) {
                $(this).closest('.card').find('.seller_cosigner_container').removeClass("sliding-up").slideDown();
            } else {
                $(this).closest('.card').find('.seller_cosigner_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            }
        });
    }

    bindSellerCosigner();

    // generate parties
    function appendParties(e) {
        // generate and save party fields
        let parties = [];

        // if empty authorized seller persons
        if (!$('#seller_content').find('.authorized_seller_person_content:visible:not(.sliding-up)').length) {
            // add seller
            parties.push({
                label: "Prodavatelj (" + $('input[name="seller_name"]').val().trim() + ")",
                name: $('input[name="seller_name"]').val().trim(),
                side: "left"
            })
        } else {
            // else, add authorized persons
            $('#seller_content').find('.authorized_seller_person_content:visible:not(.sliding-up)').each(function () {
                parties.push({
                    label: "Za Prodavatelja (" + $('input[name="seller_name"]').val().trim() + ") <br> " + $(this).find('input')[1].value.trim() + " - " + $(this).find('input')[0].value.trim(),
                    name: $(this).find('input')[1].value.trim(),
                    side: "left"
                })
            })
        }

        // add seller cosigner
        if ($('#seller_content').find('.seller_cosigner_container:visible').length) {

            let container = $('#seller_content').find('.seller_cosigner_container:visible');

            let cosigner_type = container.find('select')[0].value.trim();

            if (cosigner_type == "0") {
                cosigner_type = "Bračni drug";
            } else if (cosigner_type == "1") {
                cosigner_type = "Izvanbračni drug";
            } else if (cosigner_type == "2") {
                cosigner_type = "Životni partner";
            }

            // append seller cosigner
            parties.push({
                label: cosigner_type + " Prodavatelja " + $('input[name="seller_name"]').val().trim() + " (" + container.find('input')[0].value.trim() + ")",
                name: container.find('input')[0].value.trim(),
                side: "left"
            })
        }

        // if not empty additional sellers
        if ($('.additional_seller_content:visible').length) {
            $('.additional_seller_content:visible').each(function () {
                // if empty authorized seller persons
                if (!$(this).find('.authorized_seller_person_content:visible:not(.sliding-up)').length) {
                    // add seller
                    parties.push({
                        label: "Prodavatelj (" + $(this).find('input')[0].value.trim() + ")",
                        name: $(this).find('input')[0].value.trim(),
                        side: "left"
                    })
                } else {
                    // else, add authorized persons
                    let seller_name = $(this).find('input')[0].value.trim();
                    $(this).find('.authorized_seller_person_content:visible:not(.sliding-up)').each(function () {
                        parties.push({
                            label: "Za Prodavatelja (" + seller_name + ") <br> " + $(this).find('input')[1].value.trim() + " - " + $(this).find('input')[0].value.trim(),
                            name: $(this).find('input')[1].value.trim(),
                            side: "left"
                        })
                    })
                }

                // add seller cosigner
                if ($(this).find('.seller_cosigner_container:visible').length) {

                    let container = $(this).find('.seller_cosigner_container:visible');
                    let seller_name = $(this).find('input')[0].value.trim();

                    let cosigner_type = container.find('select')[0].value.trim();

                    if (cosigner_type == "0") {
                        cosigner_type = "Bračni drug";
                    } else if (cosigner_type == "1") {
                        cosigner_type = "Izvanbračni drug";
                    } else if (cosigner_type == "2") {
                        cosigner_type = "Životni partner";
                    }

                    parties.push({
                        label: cosigner_type + " Prodavatelja " + seller_name + " (" + container.find('input')[0].value.trim() + ")",
                        name: container.find('input')[0].value.trim(),
                        side: "left"
                    })
                }
            })
        }

        // if empty authorized buyer persons
        if (!$('#buyer_content').find('.authorized_buyer_person_content:visible:not(.sliding-up)').length) {
            // add buyer
            parties.push({
                label: "Kupac (" + $('input[name="buyer_name"]').val().trim() + ")",
                name: $('input[name="buyer_name"]').val().trim(),
                side: "right"
            })
        } else {
            // else, add authorized persons
            $('#buyer_content').find('.authorized_buyer_person_content:visible:not(.sliding-up)').each(function () {
                parties.push({
                    label: "Za Kupca (" + $('input[name="buyer_name"]').val().trim() + ") <br> " + $(this).find('input')[1].value.trim() + " - " + $(this).find('input')[0].value.trim(),
                    name: $(this).find('input')[1].value.trim(),
                    side: "right"
                })
            })
        }

        // if not empty additional buyers
        if ($('.additional_buyer_content:visible').length) {
            $('.additional_buyer_content:visible').each(function () {
                // if empty authorized buyer persons
                if (!$(this).find('.authorized_buyer_person_content:visible:not(.sliding-up)').length) {
                    // add buyer
                    parties.push({
                        label: "Kupac (" + $(this).find('input')[0].value.trim() + ")",
                        name: $(this).find('input')[0].value.trim(),
                        side: "right"
                    })
                } else {
                    // else, add authorized persons
                    let buyer_name = $(this).find('input')[0].value.trim();
                    $(this).find('.authorized_buyer_person_content:visible:not(.sliding-up)').each(function () {
                        parties.push({
                            label: "Za Kupca (" + buyer_name + ") <br> " + $(this).find('input')[1].value.trim() + " - " + $(this).find('input')[0].value.trim(),
                            name: $(this).find('input')[1].value.trim(),
                            side: "right"
                        })
                    })
                }
            })
        }

        // create parties element, populate it and append to form
        if (!$('#parties').length) {
            $('#form-container').find('form').append("<input type='hidden' name='parties' id='parties'>");
        }

        $('#parties').val(JSON.stringify(parties));
    }


    window.appendParties = appendParties;
});
