$(document).ready(function () {

    let sellerShareTooltips = {
        'particular': {
            'single': {
                text: "Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.",
                link: "/documents/RealestateSalesPrecontract/tooltips/particular/9-1.jpeg",
            },
            'multiple': {
                text: "Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.",
                link: "/documents/RealestateSalesPrecontract/tooltips/particular/9-2.jpeg",
            }
        },
        'flat': {
            'single': {
                text: "Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.",
                link: "/documents/RealestateSalesPrecontract/tooltips/flat/9-1.jpeg",
            },
            'multiple': {
                text: "Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.",
                link: "/documents/RealestateSalesPrecontract/tooltips/flat/9-2.jpeg",
            }
        },
        'other': {
            'single': {
                text: "Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.",
                link: "/documents/RealestateSalesPrecontract/tooltips/other/9-1.jpeg",
            },
            'multiple': {
                text: "Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.",
                link: "/documents/RealestateSalesPrecontract/tooltips/other/9-2.jpeg",
            }
        }
    };

    function bindCourtLandRegistrySelect2() {

        if ($('.court-select2:visible').length) {
            $('.court-select2:visible').select2({
                language: {
                    errorLoading: function () {
                        return 'Preuzimanje nije uspjelo.';
                    },
                    loadingMore: function () {
                        return 'Učitavam rezultate...';
                    },
                    maximumSelected: function (args) {
                        return 'Maksimalan broj odabranih stavki je ' + args.maximum;
                    },
                    noResults: function () {
                        return 'Nema rezultata';
                    },
                    searching: function () {
                        return 'Pretražujem...';
                    }
                },
                minimumResultsForSearch: 10,
                placeholder: "Odaberi..."
            });
        }

        if ($('.landRegistry-select2:visible').length) {
            $('.landRegistry-select2:visible').select2({
                language: {
                    errorLoading: function () {
                        return 'Preuzimanje nije uspjelo.';
                    },
                    loadingMore: function () {
                        return 'Učitavam rezultate...';
                    },
                    maximumSelected: function (args) {
                        return 'Maksimalan broj odabranih stavki je ' + args.maximum;
                    },
                    noResults: function () {
                        return 'Nema rezultata';
                    },
                    searching: function () {
                        return 'Pretražujem...';
                    }
                },
                minimumResultsForSearch: 10,
                placeholder: "Odaberi ili upiši...",
                tags: true,
                createTag: function (params) {
                    return {
                        id: params.term,
                        text: params.term,
                        newOption: true
                    }
                }
            });
        }
    }

    bindCourtLandRegistrySelect2();

    function bindAddTypeParticularAdditionalLandPlot() {
        $.each($('.add_type_particular_additional_land_plot'), function (i, button) {
            $(button).unbind('click').on('click', function (e) {

                let field_name = $(this).data('field-name');

                let new_item = $('#type_particular_additional_land_plot_template').html()
                    .replace(new RegExp('{INDEX}', 'g'), Date.now())
                    .replace(new RegExp('{FIELD_NAME}', 'g'), field_name);

                $(button).closest('.card').find('.type_particular_additional_land_plot_container').append(new_item);

                bindRemoveTypeParticularAdditionalLandPlot();

                $('.fancybox').fancybox({
                    live: false,
                    helpers: {
                        title: {
                            type: 'inside',
                            position: 'top'
                        }
                    },
                    mobile: {preventCaptionOverlap: true}

                });

                $(function () {
                    $('[data-toggle="tooltip"]').tooltip()
                });

            });
        });
    }

    bindAddTypeParticularAdditionalLandPlot();

    function bindRemoveTypeParticularAdditionalLandPlot() {
        $.each($('.remove_type_particular_additional_land_plot'), function (i, button) {
            $(button).unbind('click').on('click', function (e) {
                $(button).closest('.type_particular_additional_land_plot_content').remove();
                $(document).trigger('updatePreviewEvent');
            });
        });
    }

    bindRemoveTypeParticularAdditionalLandPlot();

    function bindAddTypeOtherAdditionalLandPlot() {
        $.each($('.add_type_other_additional_land_plot'), function (i, button) {
            $(button).unbind('click').on('click', function (e) {

                let field_name = $(this).data('field-name');

                let new_item = $('#type_other_additional_land_plot_template').html()
                    .replace(new RegExp('{INDEX}', 'g'), Date.now())
                    .replace(new RegExp('{FIELD_NAME}', 'g'), field_name);

                $(button).closest('.card').find('.type_other_additional_land_plot_container').append(new_item);

                bindRemoveTypeOtherAdditionalLandPlot();
                bindAddTypeOtherAdditionalPosessionAreaAndIdentification();

                $('.fancybox').fancybox({
                    live: false,
                    helpers: {
                        title: {
                            type: 'inside',
                            position: 'top'
                        }
                    },
                    mobile: {preventCaptionOverlap: true}

                });

                $(function () {
                    $('[data-toggle="tooltip"]').tooltip()
                });

            });
        });
    }

    bindAddTypeOtherAdditionalLandPlot();

    function bindRemoveTypeOtherAdditionalLandPlot() {
        $.each($('.remove_type_other_additional_land_plot'), function (i, button) {
            $(button).unbind('click').on('click', function (e) {
                $(button).closest('.type_other_additional_land_plot_content').remove();
                $(document).trigger('updatePreviewEvent');
            });
        });
    }

    bindRemoveTypeOtherAdditionalLandPlot();

    function bindAddTypeOtherAdditionalPosessionAreaAndIdentification() {
        $.each($('.add_type_other_additional_possession_area_and_identification'), function (i, button) {
            $(button).unbind('click').on('click', function (e) {

                let field_name = $(this).data('field-name');

                let new_item = $('#type_other_additional_possession_area_and_identification_template').html()
                    .replace(new RegExp('{INDEX}', 'g'), Date.now())
                    .replace(new RegExp('{FIELD_NAME}', 'g'), field_name);

                $(button).closest('.card').find('.type_other_additional_possession_area_and_identification_container').append(new_item);

                bindRemoveTypeOtherAdditionalPosessionAreaAndIdentification();

                $('.fancybox').fancybox({
                    live: false,
                    helpers: {
                        title: {
                            type: 'inside',
                            position: 'top'
                        }
                    },
                    mobile: {preventCaptionOverlap: true}

                });

            });
        });
    }

    bindAddTypeOtherAdditionalPosessionAreaAndIdentification();


    function bindRemoveTypeOtherAdditionalPosessionAreaAndIdentification() {
        $.each($('.remove_type_other_additional_possession_area_and_identification'), function (i, button) {
            $(button).unbind('click').on('click', function (e) {

                $(button).closest('.type_other_additional_possession_area_and_identification_content').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");

            });
        });
    }

    bindRemoveTypeOtherAdditionalPosessionAreaAndIdentification();

    // populates question indexes from seller shares onwards
    function populateDynamicIndex() {
        let realestates = $('.realestate_content:visible');

        $.each(realestates, function (i, realestate) {

            // starting dynamic index
            let dynamic_index = 8;

            if ($(realestate).find('.realestate_type_particular:checked').length) {
                dynamic_index = 8;
            }

            if ($(realestate).find('.realestate_type_flat:checked').length) {
                dynamic_index = 9;
            }

            if ($(realestate).find('.realestate_type_other:checked').length) {
                dynamic_index = 7;
            }

            $.each($(realestate).find('.dynamic_index'), function (j, dynamic_index_span) {
                $(dynamic_index_span).html(dynamic_index);
                dynamic_index++;
            });
        });
    }

    populateDynamicIndex();

    function populateSellerShareTooltips() {
        // single seller
        $.each($('.seller_share:visible'), function (i, seller_share) {

            // determine realestate type
            let realestate_type = "particular";

            if ($(seller_share).parents('.realestate_content').find('.realestate_type_flat:checked').length) {
                realestate_type = "flat";
            } else if ($(seller_share).parents('.realestate_content').find('.realestate_type_other:checked').length) {
                realestate_type = "other";
            }

            // populate tooltips

            $(seller_share).find('.seller_has_full_realestate_ownership').siblings('.fancybox').data('caption', sellerShareTooltips[realestate_type]["single"]["text"]);
            $(seller_share).find('.seller_has_full_realestate_ownership').siblings('.fancybox').attr('href', sellerShareTooltips[realestate_type]["single"]["link"]);

            $(seller_share).find('.seller_has_not_full_realestate_ownership').siblings('.fancybox').data('caption', sellerShareTooltips[realestate_type]["multiple"]["text"]);
            $(seller_share).find('.seller_has_not_full_realestate_ownership').siblings('.fancybox').attr('href', sellerShareTooltips[realestate_type]["multiple"]["link"]);

        });

        // multi seller
        $.each($('.seller_shares:visible'), function (i, seller_share) {

            // determine realestate type
            let realestate_type = "particular";

            if ($(seller_share).parents('.realestate_content').find('.realestate_type_flat:checked').length) {
                realestate_type = "flat";
            } else if ($(seller_share).parents('.realestate_content').find('.realestate_type_other:checked').length) {
                realestate_type = "other";
            }

            // populate tooltips

            $(seller_share).find('.fancybox').data('caption', sellerShareTooltips[realestate_type]["multiple"]["text"]);
            $(seller_share).find('.fancybox').attr('href', sellerShareTooltips[realestate_type]["multiple"]["link"]);

        });
    }

    function bindRealestateListeners() {
        // encumbrance sheet information
        $('.type_particular_has_not_encumbrances_sheet_data').unbind('click').on('click', function () {
            $(this).closest('.realestate_content').find('.type_particular_encumbrances_sheet_data_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
        });
        $('.type_particular_has_encumbrances_sheet_data').unbind('click').on('click', function () {
            $(this).closest('.realestate_content').find('.type_particular_encumbrances_sheet_data_container').removeClass("sliding-up").slideDown();
        });

        $('.type_flat_has_not_encumbrances_sheet_data').unbind('click').on('click', function () {
            $(this).closest('.realestate_content').find('.type_flat_encumbrances_sheet_data_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
        });
        $('.type_flat_has_encumbrances_sheet_data').unbind('click').on('click', function () {
            $(this).closest('.realestate_content').find('.type_flat_encumbrances_sheet_data_container').removeClass("sliding-up").slideDown();
        });

        $('.type_other_has_not_encumbrances_sheet_data').unbind('click').on('click', function () {
            $(this).closest('.realestate_content').find('.type_other_encumbrances_sheet_data_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
        });
        $('.type_other_has_encumbrances_sheet_data').unbind('click').on('click', function () {
            $(this).closest('.realestate_content').find('.type_other_encumbrances_sheet_data_container').removeClass("sliding-up").slideDown();
        });

        // full ownership label switching
        $('.seller_has_full_realestate_ownership').unbind('click').on('click', function () {
            $(this).closest('.realestate_content').find('.realestate_ownership_label_nominativ').html('vlasnički');
            $(this).closest('.realestate_content').find('.realestate_ownership_label_genitiv').html('vlasničkog');
        });

        $('.seller_has_not_full_realestate_ownership').unbind('click').on('click', function () {
            $(this).closest('.realestate_content').find('.realestate_ownership_label_nominativ').html('suvlasnički');
            $(this).closest('.realestate_content').find('.realestate_ownership_label_genitiv').html('suvlasničkog');
        });

        // part ownership toggle
        $('.part_ownership_radio').on('click', function () {
            $(this).closest('.seller_group')
                .find('.buyer_group').not($(this).closest('.buyer_group'))
                .find('.full_ownership_radio:checked')
                .closest('.buyer_group')
                .find('.part_ownership_radio')
                .trigger({
                        type: 'click',
                        programmatic: true
                    });
        });

        // full/no ownership toggle
        $('.full_ownership_radio').on('click', function () {

            $(this).closest('.seller_group')
                .find('.buyer_group').not($(this).closest('.buyer_group'))
                .find('.no_ownership_radio')
                .trigger({
                        type: 'click',
                        programmatic: true
                    });
        });

        populateSellerShareTooltips();

        $('.fancybox').fancybox({
            live: false,
            helpers: {
                title: {
                    type: 'inside',
                    position: 'top'
                }
            },
            mobile: {preventCaptionOverlap: true}

        });

        // toggle realestate type
        $('.realestate_type_particular').on('click', function (e) {
            $(this).closest('.realestate_content').find('.realestate_type_flat_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            $(this).closest('.realestate_content').find('.realestate_type_other_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            $(this).closest('.realestate_content').find('.realestate_type_particular_container').removeClass('sliding-up').removeClass("sliding-up").slideDown();

            populateSellerShareTooltips();
            populateDynamicIndex();
            bindCourtLandRegistrySelect2();

        });

        $('.realestate_type_flat').on('click', function (e) {
            $(this).closest('.realestate_content').find('.realestate_type_particular_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            $(this).closest('.realestate_content').find('.realestate_type_other_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            $(this).closest('.realestate_content').find('.realestate_type_flat_container').removeClass('sliding-up').removeClass("sliding-up").slideDown();


            populateSellerShareTooltips();
            populateDynamicIndex();
            bindCourtLandRegistrySelect2();

        });

        $('.realestate_type_other').on('click', function (e) {
            $(this).closest('.realestate_content').find('.realestate_type_flat_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            $(this).closest('.realestate_content').find('.realestate_type_particular_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            $(this).closest('.realestate_content').find('.realestate_type_other_container').removeClass('sliding-up').removeClass("sliding-up").slideDown();


            populateSellerShareTooltips();
            populateDynamicIndex();
            bindCourtLandRegistrySelect2();

        });

        bindAddTypeOtherAdditionalPosessionAreaAndIdentification();
        bindAddTypeOtherAdditionalLandPlot();

        bindAddTypeParticularAdditionalLandPlot();

    }

    function setIndexesOnRealestates() {
        if ($('.realestate_content:visible').length > 1) {
            $('.realestate_index:visible').each(function (index, realestate) {
                $(realestate).html(index + 1);
            });
        } else {
            $('.realestate_index:visible').html('');
        }
    }

    function bindRemoveRealestate() {
        $('.remove_realestate').unbind('click').click(function () {
            let target = $(this).closest('.realestate_content');
            target.remove();
            $(document).trigger('updatePreviewEvent');
            setIndexesOnRealestates();
        });
    }

    bindRealestateListeners();
    bindRemoveRealestate();


    // add new realestate
    $('#add_realestate').on('click', function (e) {

        // prevent propagation if tooltip was clicked
        if($(e.target).is("i")){
            return;
        } // otherwise, hide all tooltips
        else{
            $(".tooltip").tooltip('hide');
        }

        let new_realestate = $('#realestate_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now())
            .replace(new RegExp('{REALESTATE_INDEX}', 'g'), $('.realestate_content:visible').length);

        $('#realestates_container').append(new_realestate);

        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        });

        bindRealestateListeners();
        bindRemoveRealestate();
        setIndexesOnRealestates();
        populateDynamicIndex();
        bindCourtLandRegistrySelect2();

    });


});

// validation has to be declared outside document ready function and assigned to window object
function validate() {

    // remove all existing has-error classes
    $('div').removeClass('has-error');

    // set variables
    let errors = [];
    let realestates = $('.realestate_content:visible');
    let realestate_count = realestates.length;

    // get buyers
    let buyers = $("div[data-buyer]:visible").map(function () {
        return $(this).data("buyer");
    }).get().filter(function (item, i, sites) {
        return i == sites.indexOf(item);
    });

    // get sellers
    let sellers = $("div[data-seller]:visible").map(function () {
        return $(this).data("seller");
    }).get().filter(function (item, i, sites) {
        return i == sites.indexOf(item);
    });


    // **** VALIDATION RULES ****** //

    // 1. if seller has partial realestate ownership, the field is required
    // 2. sum of all seller parts cannot be bigger than 1
    $.each(realestates, function (i, realestate) {

        let seller_shares_container = $(realestate).find('.seller_shares_container');
        let realestate_index = i + 1;

        // one seller
        if ($(seller_shares_container).find('.seller_share').length) {
            let seller_share = $(seller_shares_container).find('.seller_share');

            if ($(seller_share).find('.seller_has_not_full_realestate_ownership:checked').length) {
                let radio = $(seller_share).find('.seller_has_not_full_realestate_ownership:checked');
                let numbers = $(radio).closest('div.radio-input').find('input[type=number]');
                let numerator = $(numbers[0]).val();
                let denominator = $(numbers[1]).val();

                if (numerator && denominator) {
                    if (numerator / denominator >= 1) {

                        if (realestate_count > 1) {
                            errors.push("Suvlasnički dio prodavatelja na nekretnini " + realestate_index + "  mora biti manji od 1.");
                        } else {
                            errors.push("Suvlasnički dio prodavatelja na nekretnini mora biti manji od 1.");
                        }

                        $(radio).closest('div.radio-input').addClass('has-error');
                    }
                } else {

                    if (realestate_count > 1) {
                        errors.push("Potrebno je upisati suvlasnički dio prodavatelja na nekretnini " + realestate_index + ".");
                    } else {
                        errors.push("Potrebno je upisati suvlasnički dio prodavatelja.");
                    }

                    $(radio).closest('div.radio-input').addClass('has-error');
                }
            }
        } else if ($(seller_shares_container).find('.seller_shares').length) {
            //multiple sellers
            let seller_shares = $(seller_shares_container).find('.seller_shares');

            let error_seller_shares = [];

            let sum = 0;
            $.each(seller_shares, function (j, seller_share) {

                let numbers = $(seller_share).find('input[type=number]');
                let numerator = $(numbers[0]).val();
                let denominator = $(numbers[1]).val();

                if (numerator && denominator) {
                    sum += numerator / denominator;
                } else {
                    let seller_name = $(seller_share).data('seller-name');
                    if (realestate_count > 1) {
                        errors.push("Potrebno je upisati suvlasnički dio prodavatelja " + seller_name + " na nekretnini " + realestate_index + ".");
                    } else {
                        errors.push("Potrebno je upisati suvlasnički dio prodavatelja " + seller_name + ".");
                    }

                    error_seller_shares.push(seller_share);
                }

            });

            if (sum > 1) {
                if (realestate_count > 1) {
                    errors.push("Zbroj suvlasničkih dijelova prodavatelja na nekretnini " + realestate_index + " ne smije prelaziti 1.");
                } else {
                    errors.push("Zbroj suvlasničkih dijelova prodavatelja na nekretnini ne smije prelaziti 1.");
                }

                $(seller_shares).addClass('has-error');
            }

            $.each(error_seller_shares, function (j, error_share) {
                $(error_share).addClass('has-error');
            });
        }

    });


    if (sellers.length > 1) {
        // 3. Each seller must give away at least some part to some buyer on a realestate
        $.each(realestates, function (i, realestate) {
            let realestate_sellers = $(realestate).find('div[data-seller]');

            $.each(realestate_sellers, function (j, seller) {
                let seller_donates = false;
                let seller_buyers = $(seller).find('div[data-buyer]');
                $.each(seller_buyers, function (k, buyer) {
                    if ($(buyer).find('.part_ownership_radio:checked').length || $(buyer).find('.full_ownership_radio:checked').length) {
                        seller_donates = true;
                    }
                });

                if (!seller_donates) {
                    if (realestate_count > 1) {
                        errors.push('Prodavatelj ' + $(seller).data('seller-name') + ' mora prodati svoj dio na nekretnini ' + (i + 1) + '.');
                    } else {
                        errors.push('Prodavatelj ' + $(seller).data('seller-name') + ' mora prodati barem jedan dio svoje nekretnine.');
                    }
                }
            });
        });
    }


    if (sellers.length > 1 && buyers.length > 1) {

        // 4. Each buyer must acquire at least something from some seller
        $.each(buyers, function (i, buyer) {

            let error = true;

            let buyer_groups = $('div[data-buyer=' + buyer + ']:visible');

            $.each(buyer_groups, function (j, buyer_group) {
                let has_part_ownership = $(buyer_group).find('.part_ownership_radio:checked').length;
                let has_full_ownership = $(buyer_group).find('.full_ownership_radio:checked').length;

                if (has_part_ownership || has_full_ownership) {
                    error = false;
                }

            });

            if (error) {
                let buyer_name = $(buyer_groups[0]).data('buyer-name');
                errors.push("Kupac " + buyer_name + " mora stjecati neki dio nekretnine.");
            }

        });


    }


    if (sellers.length > 1) {

        // 5. Sum of buyer ownership parts per seller must not be greater than 1
        // 6. If parts radio selected, value is required
        $.each(realestates, function (i, realestate) {

            let realestate_index = i + 1;

            $.each(sellers, function (j, seller) {

                let seller_groups = $(realestate).find('div[data-seller=' + seller + ']:visible');

                $.each(seller_groups, function (k, seller_group) {

                    let sum = 0;
                    let seller_name = $(seller_group).data('seller-name');
                    let buyer_groups = $(seller_group).find('div[data-buyer]');
                    let partial_buyer_groups = [];

                    $.each(buyer_groups, function (l, buyer_group) {
                        let part_ownership_radio = $(buyer_group).find('.part_ownership_radio:checked');
                        if (part_ownership_radio.length) {
                            let numbers = $(part_ownership_radio).closest('.radio-input').find('input[type=number]');
                            let numerator = $(numbers[0]).val();
                            let denominator = $(numbers[1]).val();

                            if (numerator && denominator) {
                                if (numerator / denominator < 1) {
                                    sum += numerator / denominator;
                                    partial_buyer_groups.push(buyer_group)
                                } else {
                                    if (realestate_count > 1) {

                                        if (buyers.length > 1) {
                                            errors.push("Djelomični dio koji kupac " + $(buyer_group).data('buyer-name') + " stječe od prodavatelja " + seller_name + " na nekretnini " + realestate_index + " mora biti manji od 1.");
                                        } else {
                                            errors.push("Djelomični dio koji kupac stječe od prodavatelja " + seller_name + " na nekretnini " + realestate_index + " mora biti manji od 1.");
                                        }

                                    } else {

                                        if (buyers.length > 1) {
                                            errors.push("Djelomični dio koji kupac " + $(buyer_group).data('buyer-name') + " stječe od prodavatelja " + seller_name + " mora biti manji od 1.");
                                        } else {
                                            errors.push("Djelomični dio koji kupac stječe od prodavatelja " + seller_name + " mora biti manji od 1.");
                                        }

                                    }

                                    $(buyer_group).addClass('has-error');
                                }

                            } else {
                                if (realestate_count > 1) {

                                    if (buyers.length > 1) {
                                        errors.push("Potrebno je upisati dio koji kupac " + $(buyer_group).data('buyer-name') + " stječe od prodavatelja " + seller_name + " na nekretnini " + realestate_index + ".");
                                    } else {
                                        errors.push("Potrebno je upisati dio koji kupac stječe od prodavatelja " + seller_name + " na nekretnini " + realestate_index + ".");
                                    }

                                } else {

                                    if (buyers.length > 1) {
                                        errors.push("Potrebno je upisati dio koji kupac " + $(buyer_group).data('buyer-name') + " stječe od prodavatelja " + seller_name + ".");
                                    } else {
                                        errors.push("Potrebno je upisati dio koji kupac stječe od prodavatelja " + seller_name + ".");
                                    }

                                }

                                $(buyer_group).addClass('has-error');

                            }
                        }
                    });


                    if (sum > 1) {
                        if (realestate_count > 1) {
                            if (buyers.length > 1) {
                                errors.push("Zbroj dijelova koje kupci stječu od prodavatelja " + seller_name + " na nekretnini " + realestate_index + " ne smije biti veći od 1.");
                            } else {
                                errors.push("Kupčev dio nekretnine " + realestate_index + " prodavatelja " + seller_name + " ne smije biti veći od 1.");
                            }
                        } else {
                            if (buyers.length > 1) {
                                errors.push("Zbroj dijelova koje kupci stječu od prodavatelja " + seller_name + " ne smije biti veći od 1.");
                            } else {
                                errors.push("Kupčev dio prodavatelja " + seller_name + " ne smije biti veći od 1.");
                            }
                        }

                        $.each(partial_buyer_groups, function (m, partial_buyer_group) {
                            $(partial_buyer_group).addClass('has-error');
                        });
                    }

                });


            });


        });

    }

    if (sellers.length == 1) {
        if (buyers.length == 1) {
            $.each(realestates, function (i, realestate) {
                let realestate_index = i + 1;
                let container = $(realestate).find('.single_seller_single_buyer:visible');

                if (container.length) {
                    let part_radio = $(container).find('.part_ownership_radio:checked');

                    if (part_radio.length) {
                        let numbers = $(container).find('input[type=number]');
                        let numerator = $(numbers[0]).val();
                        let denominator = $(numbers[1]).val();

                        if (numerator && denominator) {
                            if (numerator / denominator >= 1) {

                                if (realestate_count > 1) {
                                    errors.push("Djelomični dio koji stječe kupac na nekretnini " + realestate_index + " mora biti manji od 1.");
                                } else {
                                    errors.push("Djelomični dio koji stječe kupac mora biti manji od 1.");
                                }

                                console.log($(part_radio).closest('.radio-input'))

                                $(part_radio).closest('.radio-input').addClass('has-error');
                            }

                        } else {

                            if (realestate_count > 1) {
                                errors.push("Potrebno je upisati dio koji kupac stječe na nekretnini " + realestate_index + ".");
                            } else {
                                errors.push("Potrebno je upisati dio koji kupac stječe na nekretnini.");
                            }

                            $(part_radio).closest('.radio-input').addClass('has-error');
                        }
                    }
                }
            });
        } else {

            $.each(realestates, function (i, realestate) {

                let realestate_index = i + 1;

                let seller_group = $(realestate).find('.single_seller_multi_buyers');
                let sum = 0;
                let buyer_groups = $(seller_group).find('div[data-buyer]');

                $.each(buyer_groups, function (j, buyer_group) {
                    let numbers = $(buyer_group).find('input[type=number]');
                    let numerator = $(numbers[0]).val();
                    let denominator = $(numbers[1]).val();

                    if (numerator && denominator) {

                        if (numerator / denominator < 1) {
                            sum += numerator / denominator;
                        } else {
                            if (realestate_count > 1) {
                                errors.push("Djelomični dio koji kupac " + $(buyer_group).data('buyer-name') + " stječe od prodavatelja na nekretnini " + realestate_index + " mora biti manji od 1.");
                            } else {
                                errors.push("Djelomični dio koji kupac " + $(buyer_group).data('buyer-name') + " stječe od prodavatelja mora biti manji od 1.");
                            }

                            $(buyer_group).addClass('has-error');
                        }


                    } else {
                        if (realestate_count > 1) {
                            if (buyers.length > 1) {
                                errors.push("Potrebno je upisati dio koji kupac " + $(buyer_group).data('buyer-name') + " stječe od prodavatelja na nekretnini " + realestate_index + ".");
                            } else {
                                errors.push("Potrebno je upisati dio koji kupac stječe od prodavatelja na nekretnini " + realestate_index + ".");
                            }

                        } else {

                            if (buyers.length > 1) {
                                errors.push("Potrebno je upisati dio koji kupac " + $(buyer_group).data('buyer-name') + " stječe od prodavatelja.");
                            } else {
                                errors.push("Potrebno je upisati dio koji kupac stječe od prodavatelja.");
                            }

                        }

                        $(buyer_group).addClass('has-error');

                    }
                });


                if (sum > 1) {
                    if (realestate_count > 1) {
                        if (buyers.length > 1) {
                            errors.push("Zbroj dijelova koje kupci stječu od prodavatelja na nekretnini " + realestate_index + " ne smije biti veći od 1.");
                        } else {
                            errors.push("Kupčev dio od prodavatelja na nekretnini " + realestate_index + " ne smije biti veći od 1.");
                        }
                    } else {
                        if (buyers.length > 1) {
                            errors.push("Zbroj dijelova koje kupci stječu od prodavatelja ne smije biti veći od 1.");
                        } else {
                            errors.push("Kupčev dio od prodavatelja ne smije biti veći od 1.");
                        }
                    }

                    $(seller_group).addClass('has-error');
                }


            });

        }
    }


    // **** END VALIDATION RULES ****** //


    // display errors
    if (errors.length) {

        let errors_html = "<strong>Molimo ispravite sljedeće greške:</strong>";
        errors_html += "<ol>";

        $.each(errors, function (i, error) {
            errors_html += "<li>" + error + "</li>";
        });

        errors_html += "</ol>";

        $('#javascript-error').html(errors_html);
        $('#javascript-error-container').removeClass('d-none');

        window.scrollTo(0, 0);

        return false;
    } else {
        $('#javascript-error-container').addClass('d-none');
        return true;
    }

}

window.validate = validate;
