$($).ready(function () {
    // add movable
    $('#add_movable').click(function () {
        let new_movable = $('#movable_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now());
        $('#movables').append(new_movable);
        bindRemoveWorkResponsibility();
    });

    function bindRemoveWorkResponsibility() {
        $('.remove_movable').unbind('click').click(function () {
            let target = $(this).closest('.movable_content');

            if ($('.remove_movable:visible').length > 1) {
                target.remove();
                $(document).trigger('updatePreviewEvent');
            }

        });
    }

    bindRemoveWorkResponsibility();


    // containers toggle
    $('#includes_movables').on('click', function () {
        $('#movables_container').removeClass("sliding-up").slideDown();
    });
    $('#does_not_include_movables').on('click', function () {
        $('#movables_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });


});
