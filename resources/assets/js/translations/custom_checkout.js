$(document).ready(function () {

    $('#translation-type').on('change', function () {
        if ($(this).val() === 'certified') {
            $('.certified-translation').show();
            $('.normal-translation').hide();

            $('#phone').prop('required', true);
        } else {
            $('.certified-translation').hide();
            $('.normal-translation').show();

            $('#phone').prop('required', false);
        }
    });

    $('#add-document').on('click', function () {
        if($('#translation-documents tr').length < 10) {
            $('#translation-documents').append('<tr>\n' +
                '<td style="max-width: 300px;" class="pt-2"><input class="document" required type="file" name="documents['+Math.random()+']"></td>\n' +
                '<td><button type="button" class="btn btn-sm btn-link remove-document">ukloni</button></td>\n' +
                '</tr>');

            bindRemoveDocumentLinks();
        }
    })

    function bindRemoveDocumentLinks() {
        $('.remove-document').unbind('click').on('click', function (e) {
            e.preventDefault();
            $(this).closest('tr').remove();
        })
    }

    $('#use-different-shipping-address').on('change', function () {
        if ($(this).is(':checked')) {
            $('#shipping').show();
        } else {
            $('#shipping').hide();
        }
    });

    $('#person-type-individual').on('click', function () {
        $('.person-type-individual').show();
        $('.person-type-business').hide();

        $('#oib').prop('required', false);
        $('#name').attr('placeholder', 'Unesi ime i prezime...');

    });

    $('#person-type-business').on('click', function () {
        $('.person-type-individual').hide();
        $('.person-type-business').show();

        $('#oib').prop('required', true);
        $('#name').attr('placeholder', 'Unesi naziv...');
    });

    $('#scanned-delivery-type-zagreb, #scanned-delivery-type-rijeka').on('click', function () {
        $('#different-shipping-information').hide();
        $('#shipping').hide();
    })

    $('#scanned-delivery-type-post').on('click', function () {
        $('#different-shipping-information').show();
        if($('#use-different-shipping-address').is(':checked')) {
            $('#shipping').show();
        } else {
            $('#shipping').hide();
        }
    })

    $('#binding-type-original').on('click', function () {
        $('#binding-type-original-container').show();
        $('#binding-type-scanned-container').hide();

        $('#different-shipping-information').hide();
        $('#shipping').hide();
    })

    $('#binding-type-scanned').on('click', function () {
        $('#binding-type-original-container').hide();
        $('#binding-type-scanned-container').show();

        if($('#scanned-delivery-type-post').is(':checked')) {
            $('#different-shipping-information').show();
            if($('#use-different-shipping-address').is(':checked')) {
                $('#shipping').show();
            } else {
                $('#shipping').hide();
            }
        } else {
            $('#different-shipping-information').hide();
            $('#shipping').hide();
        }
    })

    // on submit button, remove "required" attribute from all hidden fields and submit the form
    $('#submit-button').on('click', function () {

        let form = $('#custom-translation-form');

        // temporarily remove required attribute from hidden fields
        const hiddenFields = form.find(':hidden').find('[required]').removeAttr('required');

        // trigger form validation and submission
        form[0].reportValidity();
        if (form[0].checkValidity()) {
            form.submit();
        }

        // reapply the required attribute to hidden fields
        hiddenFields.attr('required', 'required');
    })
});
