$(document).ready(function () {

    // global scope, so it can be accessed from editor.js
    window.articleBodyConfig = {
        language: 'hr',
        selector: '.article-body',
        fixed_toolbar_container: '#editor-toolbar',
        menubar: false,
        setup: (editor) => {

            editor.on('init', () => {
                $('#editor-toolbar').hide(); // hide toolbar container
                editor.ui.show(); // load toolbar in DOM

                // strip link tags
                let content = editor.getContent();
                content = content.replace(/<a\s[^>]*>(.*?)<\/a>/g, "$1");
                editor.setContent(content);
            });

            editor.on('focus', () => {
                $('.tox-tinymce-inline').css('display', 'none'); // hide any remaining open toolbars
                $('#editor-toolbar').show(); // reveal toolbar container
                $(editor.getElement()).closest('.segment-container').addClass('active');    // make segment container active
            })

            editor.on('blur', function(e) {
                // if content is different from starting content, wrap in dirty div
                if(editor.getContent({ format: 'raw' }).replace(/\n/g, "") !== editor.startContent) {
                    if(!$(editor.getElement()).parent('.segment-dirty').length) {
                        $(editor.getElement()).wrap('<div class="segment-dirty"></div>');
                    }
                }

                $(editor.getElement()).closest('.segment-container').removeClass('active');     // remove segment container active
            })

            editor.ui.registry.addToggleButton('paragraphList', {
                tooltip: 'Numeriraj stavak',
                text: 'Numerirani stavak',
                onAction: function (_) {
                    // check if button clicked inside paragraph list
                    const inParagraphList = editor.dom.getParent(editor.selection.getNode(), 'ol.paragraph-list');

                    if(inParagraphList) {
                        // just remove the list
                        editor.execCommand('InsertOrderedList');
                    } else {
                        // Replace the entire parent element with ol.paragraph-list > li
                        editor.undoManager.transact(function () {
                            // Save the current selection
                            const bookmark = editor.selection.getBookmark(2, true);

                            // Find the parent block element
                            let blockEl = editor.dom.getParent(editor.selection.getStart(), editor.dom.isBlock);

                            // if node is a child of a table element, return false
                            if (blockEl && blockEl.nodeName === 'TD' || blockEl.nodeName === 'TH') {
                                return false;
                            }

                            // If currently selected element is list item
                            if (blockEl && blockEl.nodeName === 'LI') {
                                // Find the outermost parent list if the parent block is within a nested list
                                while (blockEl && blockEl.nodeName !== 'OL' && blockEl.nodeName !== 'UL') {
                                    blockEl = blockEl.parentNode;
                                }
                            }

                            if (blockEl) {
                                // Create a new ol.paragraph-list with a li item containing the block's content
                                let newBlockHtml = `<ol class="paragraph-list"><li>${blockEl.innerHTML}</li></ol>`;

                                if(blockEl.nodeName === 'OL' || blockEl.nodeName === 'UL') {
                                    newBlockHtml = `<ol class="paragraph-list">${blockEl.innerHTML}</ol>`;
                                }

                                // Replace the parent block element with the new structure
                                editor.dom.replace(editor.dom.createFragment(newBlockHtml), blockEl);

                                // Restore the selection
                                editor.selection.moveToBookmark(bookmark);
                            }
                        });
                    }
                },
                onSetup: function (buttonApi) {
                    editor.on('NodeChange', function(e) {
                        const inParagraphList = editor.dom.getParent(editor.selection.getNode(), 'ol.paragraph-list');
                        buttonApi.setActive(!!inParagraphList);
                    });
                    return function() {
                        editor.off('NodeChange');
                    };
                }
            });

            editor.ui.registry.addToggleButton('customNumlist', {
                tooltip: 'Numerirani popis',
                icon: 'ordered-list',
                onAction: function (_) {
                    // check if button clicked inside paragraph list
                    const inParagraphList = editor.dom.getParent(editor.selection.getNode(), 'ol.paragraph-list');

                    editor.execCommand('InsertOrderedList');

                    // if it was paragraph list, the list was first destroyed
                    if (inParagraphList) {
                        // then we need to create a new (normal) one
                        editor.execCommand('InsertOrderedList');
                    }
                },
                onSetup: function (buttonApi) {
                    editor.on('NodeChange', function(e) {
                        const inOrderedList = editor.dom.getParent(editor.selection.getNode(), 'ol');
                        const inParagraphList = editor.dom.getParent(editor.selection.getNode(), 'ol.paragraph-list');
                        buttonApi.setActive(!!inOrderedList && !inParagraphList);
                    });
                    return function() {
                        editor.off('NodeChange');
                    };
                }
            });

            let articleIndex = parseInt($('.editable-segment[data-type="article"]').index($(editor.getElement()).closest('.editable-segment'))) + 1;

            editor.ui.registry.addButton('navigationLabel', {
                text: 'Članak ' + articleIndex + '.',
                onAction: function (_) {},
            });

            editor.on('NodeChange', (e) => {
                let articleBody = $(editor.getElement());

                // wrap all tables that are not wrapped in a div with the class .overflow-x-auto
                articleBody.find('table').each(function() {
                    if (!$(this).parent().hasClass('overflow-x-auto')) {
                        $(this).wrap('<div class="overflow-x-auto"></div>');
                    }
                });

                // remove 'paragraph-list' class from all nested lists
                articleBody.find('ol ol, ol ul, ul ol').removeClass('paragraph-list');

                // remove 'paragraph-list' class from all unordered lists
                articleBody.find('ul').removeClass('paragraph-list');

                // each <ol.paragraph-list> should have only one <li> element
                // if it has multiple, each multiple list item should be extracted out and wrapped in its own <ol.paragraph-list> as a sibling to the original <o.paragraph-list>
                articleBody.find('ol.paragraph-list').each(function() {
                    let $this = $(this);
                    if ($this.children('li').length > 1) {
                        let lastLi;
                        $this.children('li').each(function() {
                            // Wrap this <li> in a new <ol> with 'paragraph-list' class
                            $(this).wrap('<ol class="paragraph-list"></ol>');
                            lastLi = $(this); // Keep track of the last <li> element processed
                        });
                        // Remove the original <ol> now that its children are wrapped in new <ol>s
                        $this.children('ol.paragraph-list').unwrap();

                        // Assuming lastLi now references the last <li> element processed
                        // Set the cursor to the end of this <li> (blinking cursor)
                        if (lastLi && lastLi.length > 0) {
                            editor.selection.select(lastLi.get(0), true);
                            editor.selection.collapse(false);
                        }
                    }
                });

                if (window.matchMedia('(min-width: 1024px)').matches) {
                    // Remove any orphaned tooltip elements
                    $('.tooltip').remove();

                    // Handle tutorial tooltips
                    $.each(editor.dom.select('span[data-toggle="tooltip"]'), function () {
                        const $span = $(this);

                        // Ensure the tooltip is always disposed of before being re-initialized
                        $span.tooltip('dispose').tooltip({ html: true });

                        // Check if the span is empty or contains only invisible characters and remove it if so
                        if (!$span.text().trim()) {
                            $span.remove();
                            return; // Skip the rest of the logic since this span is removed
                        }

                        // Get the original content if not already set
                        if (!$span.data('original-content')) {
                            $span.data('original-content', $span.html());
                        }

                        // Check if the content has changed
                        if ($span.data('original-content') !== $span.html()) {
                            // Save the current selection range
                            const selection = window.getSelection();
                            const range = selection.getRangeAt(0);
                            const startContainer = range.startContainer;
                            const startOffset = range.startOffset;

                            // Unwrap the span but keep the inner content intact
                            $span.contents().unwrap();

                            // Restore the caret position
                            range.setStart(startContainer, startOffset);
                            range.collapse(true);
                            selection.removeAllRanges();
                            selection.addRange(range);
                        }

                        // Enable tooltip on hover or when the caret is inside the span
                        $span.on('mouseenter focus click', function () {
                            $(this).tooltip('show');
                        }).on('mouseleave blur', function () {
                            const selection = window.getSelection();
                            const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;

                            // Only hide the tooltip if the caret is not inside the span
                            if (!range || !$(this).is($(range.commonAncestorContainer).closest('span[data-toggle="tooltip"]'))) {
                                $(this).tooltip('hide');
                            }
                        }).on('click', function (e) {    // Select the entire tooltip span on click
                            const selection = window.getSelection();
                            if (selection.isCollapsed) {  // Check if there is no selection (collapsed selection means no text is selected)
                                const range = document.createRange();
                                range.selectNodeContents(this);
                                selection.removeAllRanges();
                                selection.addRange(range);
                            }
                        });
                    });
                }

                // Remove empty mce_caret spans
                $.each(editor.dom.select('span[id="_mce_caret"]'), function () {
                    const $span = $(this);

                    // Check if the span is empty or contains only invisible characters and remove it if so
                    if (!$span.text().trim()) {
                        $span.remove();
                    }
                });

                enumerateParagraphs(articleBody);
            });

            // Observer to dispose tooltips on DOM mutations (span removal, etc.)
            if (window.matchMedia('(min-width: 1024px)').matches) {
                const observer = new MutationObserver(function (mutationsList) {
                    for (let mutation of mutationsList) {
                        if (mutation.removedNodes.length > 0) {
                            // Remove any orphaned tooltip elements
                            $('.tooltip').remove();
                        }
                    }
                });

                observer.observe(editor.getElement(), { childList: true, subtree: true });
            }

            editor.on('keydown', function(e) {
                if(e.which === 9) {   // disable TAB
                    e.preventDefault();
                    return false;
                }

                // Only on desktop (where tooltips are enabled)
                if (window.matchMedia('(min-width: 1024px)').matches) {
                    if (e.which === 8 || e.which === 46) { // Backspace or Delete key
                        const selection = editor.selection;
                        const node = selection.getNode();
                        if (node.nodeName === 'SPAN' && node.hasAttribute('data-toggle')) {
                            setTimeout(() => {
                                if (!document.body.contains(node)) {
                                    $(node).tooltip('dispose');

                                    // Remove formatting after span is deleted
                                    editor.execCommand('RemoveFormat');
                                }
                            }, 0);
                        }
                    }
                }
            });

        },
        plugins: ['lists', 'save', 'table'],
        // on reset button click, restore editor content and set cursor at the end
        save_oncancelcallback: function (editor) {
            editor.setContent(editor.startContent);
            // place cursor at end of input
            editor.selection.select(editor.getBody(), true);
            editor.selection.collapse(false);

        },
        visual: false,
        inline: true,
        paste_as_text: true,
        paste_block_drop: true,
        smart_paste: false,
        paste_preprocess : function(editor, args) {
            let content = args.content;

            content = content.replaceAll(/&nbsp;/g, '');
            content = content.replaceAll('\u00A0', '');

            args.content = content;
        },
        toolbar: 'navigationLabel paragraphList bold italic underline backcolor removeformat alignleft aligncenter alignright customNumlist bullist indent outdent undo redo cancel',
        extended_valid_elements: 'span[class|style]',
        object_resizing : false,
        color_default_background: '#FBEEB8',
    };

    tinymce.init(window.articleBodyConfig);

    // add initialization to window function
    window.initArticleBody = function() {tinymce.init(window.articleBodyConfig)}

    function enumerateParagraphs(article) {
        const paragraphs = article[0].querySelectorAll('.paragraph-list > li');
        paragraphs.forEach((paragraph, paragraphIndex) => {
            paragraph.style.counterReset = `paragraphIndex ${paragraphIndex}`;
        });
    }
});