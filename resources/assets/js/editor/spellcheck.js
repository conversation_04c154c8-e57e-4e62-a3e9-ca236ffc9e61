$(document).ready(function () {

    // save editors that have been affected by the spellcheck
    let affectedEditors = [];

    $('.spellcheck').on('click', function (e) {
        // only run if spinner not already spinning
        if ($('.spellcheck-spinner').hasClass('d-none')) {
            affectedEditors = [];
            showSpinner();
            checkSpelling();
        }
    });

    function showSpinner() {
        $('.spellcheck-spinner').removeClass('d-none');
    }

    function hideSpinner() {
        $('.spellcheck-spinner').addClass('d-none');
    }

    function checkSpelling() {

        // remove parties segment
        let segmentsForCheck = $('#editor .editable-segment').filter(function () {
            return $(this).data('type') !== 'parties';
        })

        // concatenate html from each of the segments into a single string
        let content = segmentsForCheck.map(function () {
            // disable contendeditable attribute
            let segment = this.outerHTML.replace(/ contenteditable="true"/g, "");

            //unwrap spans with data-toggle="tooltip"
            let tempDiv = document.createElement('div');
            tempDiv.innerHTML = segment;

            let spans = tempDiv.querySelectorAll('span[data-toggle="tooltip"]');
            spans.forEach(span => {
                // replace the span with its inner HTML
                span.replaceWith(...span.childNodes);
            });

            // return the modified segment as a string
            return tempDiv.innerHTML;
        }).get().join('');

        // perform spellcheck on backend
        $.ajax({
            url: '/editor/spellcheck',
            type: 'POST',
            data: {
                _token: $("[name='_token']").val(),
                id: window.location.href.split("/").pop(),
                content: content
            },
            success: function (response) {

                // show error modal
                if(response.errors && response.adjustments) {

                    $('#spellchecked-content').html(response.content);

                    $('.spelling-error').popover({
                        container: '#spellchecked-content',
                        html: true,
                        trigger: 'manual',
                        sanitize: false
                    });

                    $('#spellcheckModal').on('click', '.spelling-error', function(e) {
                        $(this).popover('show');
                        $('.spelling-error').not($(this)).popover('hide');

                        bindPopoverEvents($(this));
                    });

                    $('#spellcheckModal').modal('show');

                } else {
                    $('#spellcheckNoErrorsModal').modal('show');
                }
            },
            error: function () {
                hideSpinner();
            }
        })
    }

    function bindPopoverEvents(triggerElement) {

        // store trigger element on the span
        let popoverId = triggerElement.attr('aria-describedby');
        $('#' + popoverId).data('triggerElement', triggerElement);

        // on click ignore button
        $('.spellcheck-suggestion-ignore').on('click', function () {
            triggerElement.removeClass('spelling-error').addClass('spelling-fixed');
            triggerElement.popover('hide');
            triggerElement.popover('disable');

            // put focus back on the modal
            $('#spellcheckModal').focus();
        });

        // on click apply button
        $('.spellcheck-suggestion-apply').on('click', function () {

            let checkedRadio = $(this).closest('.popover').find('.form-check-input:checked');
            let replacement = checkedRadio.val();

            if(replacement === 'custom') {
                replacement = checkedRadio.closest('.form-check').find('input[type="text"]').val();
            }

            // if user input not empty
            if(replacement && replacement.trim()) {
                triggerElement.html(replacement);

                // add editor to the list of affected editors
                let editorId = triggerElement.closest('.mce-content-body').attr('id');
                if(affectedEditors.indexOf(editorId) === -1) {
                    affectedEditors.push(editorId);
                }

                triggerElement.removeClass('spelling-error').addClass('spelling-fixed');
                triggerElement.popover('hide');
                triggerElement.popover('disable');
            }

            // put focus back on the modal
            $('#spellcheckModal').focus()
        });

        // on focus radio input text, select radio
        $('.radio_input_text').on('focus', function () {
            $(this).closest('.form-check').find('.form-check-input').click();
        });

        // listen for enter keypress in custom input text field
        $('.popover').on('keypress', 'input[type="text"]', function(e) {
            if (e.which === 13) { // 13 is the enter key
                e.preventDefault(); // prevent the default action
                $(this).closest('.popover').find('.spellcheck-suggestion-apply').click();
            }
        });
    }

    // update corresponding tinyMCE editors
    $('#apply-spellcheck').on('click', function () {
        let $this = $(this);
        affectedEditors.forEach(function (_editorId) {
            // find the editor's content within the modal
            let $editorContent = $this.closest('.modal').find('#' + _editorId);

            // find spans with 'spelling-error' and 'spelling-fixed' classes and unwrap them
            $editorContent.find('span.spelling-error, span.spelling-fixed').each(function () {
                $(this).contents().unwrap();
            });

            tinyMCE.get(_editorId).setContent($editorContent.html());
        });

        $('#spellcheckModal').modal('hide');
    });

    // on spellcheckModal and spellcheckNoErrorsModal show, hide spinner
    $('#spellcheckModal, #spellcheckNoErrorsModal').on('show.bs.modal', function () {
        setTimeout(function () {
            hideSpinner();
        }, 500);
    })

    // close popover if clicked outside it
    $('#spellcheckModal').off('click.popoverClose').on('click.popoverClose', function(e) {
        if (!$(e.target).closest('.spelling-error').length && !$(e.target).closest('.popover').length) {
            $('.spelling-error').popover('hide');
        }
    });

    // on close modal, reset content
    $('#spellcheckModal').on('hidden.bs.modal', function () {
        $('#spellchecked-content').empty();
    })


});
