<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_options', function (Blueprint $table) {
            $table->boolean('is_translation_tutorial_shown')->default(false)->after('is_editor_tutorial_shown');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_options', function (Blueprint $table) {
			$table->dropColumn('is_translation_tutorial_shown');
        });
    }
};
