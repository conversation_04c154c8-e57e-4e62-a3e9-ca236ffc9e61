<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class SetDefaultWordpressBlankSlateTheme extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $option = \Corcel\Model\Option::where('option_name', 'template')->first();
        $option->option_value = 'blankslate';
        $option->save();

        $option = \Corcel\Model\Option::where('option_name', 'stylesheet')->first();
        $option->option_value = 'blankslate';
        $option->save();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
