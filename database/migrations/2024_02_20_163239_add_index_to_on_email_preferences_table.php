<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('email_preferences', function (Blueprint $table) {
            $table->index('newsletter');
            $table->index('general');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('email_preferences', function (Blueprint $table) {
			$table->dropIndex('email_preferences_newsletter_index');
			$table->dropIndex('email_preferences_general_index');
        });
    }
};
