<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
	    Schema::table('translations', function (Blueprint $table) {
		    $table->dropColumn('refund_note');
	    });

        Schema::table('receipts', function (Blueprint $table) {
	        $table->text('refund_note')->nullable()->after('refund_for_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('receipts', function (Blueprint $table) {
	        $table->dropColumn('refund_note');
        });

	    Schema::table('translations', function (Blueprint $table) {
		    $table->text('refund_note')->nullable()->after('shipment_tracking_code');
	    });
    }
};
