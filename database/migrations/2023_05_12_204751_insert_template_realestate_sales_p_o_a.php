<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class InsertTemplateRealestateSalesPOA extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    \DB::insert("
	        INSERT INTO `contract_templates` (
	            `title`, 
	            `public_title`, 
	            `tags`, 
	            `time_to_fill`, 
	            `slug`, 
	            `precontract_for_id`, 
	            `type_id`, 
	            `is_visible`, 
	            `created_at`, 
	            `updated_at`
	        ) 
	        VALUES ('RealestateSalesPOA', 'Punomoć za prodaju nekretnine', 'punomoć, punomoć za prodaju nekretnine, punomoć prodaja nekretnine, punomoć prodaja stana, punomoć prodaja kuće, punomoć za prodaju stana, punomoć za prodaju kuće, prodaja kuće, prodaja nekretnine, prodaja stana', '5', 'punomoc-za-prodaju-nekretnine', NULL, '2', 1, NOW(), NOW())");
		    
		$result = \DB::select('SELECT MAX(id) as id FROM contract_templates;');
		$template_id = intval($result[0]->id);
		
		DB::insert("INSERT INTO `contract_categories_templates` (`category_id`, `template_id`, `order_index`, `created_at`, `updated_at`) VALUES ('3', ?, '2', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `contract_template_sections` (`contract_template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Stranke', 'parties', '1', 'Stranke', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `contract_template_sections` (`contract_template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Nekretnina', 'realestate', '2', 'Nekretnina', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `contract_template_sections` (`contract_template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Ovlaštenja', 'authorities', '3', 'Ovlaštenja', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `contract_template_sections` (`contract_template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Važenje punomoći', 'validity', '4', 'Važenje punomoći', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `contract_template_sections` (`contract_template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Završne odredbe', 'final_provisions', '5', 'Završne odredbe', NOW(), NOW())", [$template_id]);
		
	}

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
		$template_id = \DB::table('contract_templates')->select('id')->where('title', 'RealestateSalesPOA')->value('id');
		\DB::table('contract_templates')->where('id', $template_id)->delete();
		\DB::table('contract_categories_templates')->where('template_id', $template_id)->delete();
		\DB::table('contract_template_sections')->where('contract_template_id', $template_id)->delete();
		
	}
}
