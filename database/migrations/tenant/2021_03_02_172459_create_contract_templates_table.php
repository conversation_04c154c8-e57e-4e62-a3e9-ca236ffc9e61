<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateContractTemplatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contract_templates', function (Blueprint $table) {
            $table->increments('id');
            $table->string('title');
            $table->string('public_title')->nullable();
            $table->text('tags')->nullable();
            $table->integer('time_to_fill');
            $table->string('slug');
            $table->integer('precontract_for_id')->nullable()->index();
            $table->integer('version_id')->nullable()->index();
            $table->tinyInteger('is_visible')->default(1)->index();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contract_templates');
    }
}
