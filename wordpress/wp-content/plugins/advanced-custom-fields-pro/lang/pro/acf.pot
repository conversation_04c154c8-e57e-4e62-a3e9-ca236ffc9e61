# Copyright (C) 2023 Advanced Custom Fields PRO
# This file is distributed under the same license as the Advanced Custom Fields PRO package.
msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields PRO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language-Team: WP Engine <<EMAIL>>\n"
"POT-Creation-Date: 2023-06-27 14:12+0000\n"
"Report-Msgid-Bugs-To: https://support.advancedcustomfields.com\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_ex:1,2c;_n:1,2;_n_noop:1,2;_nx:1,2,4c;_nx_noop:1,2,3c;_x:1,2c;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr ""

#: pro/blocks.php:170
msgid "Block type name is required."
msgstr ""

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr ""

#: pro/blocks.php:943
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:949
msgid "Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to this block."
msgstr ""

#: pro/options-page.php:47
msgid "Options"
msgstr ""

#: pro/options-page.php:77, pro/fields/class-acf-field-gallery.php:528
msgid "Update"
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr ""

#: pro/updates.php:99
msgid "To enable updates, please enter your license key on the <a href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see <a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""

#: pro/updates.php:159
msgid "<b>ACF Activation Error</b>. Your defined license key has changed, but an error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid "<b>ACF Activation Error</b>. Your defined license key has changed, but an error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
msgid "<b>ACF Activation Error</b>. An error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:279
msgid "Check Again"
msgstr ""

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""

#: pro/admin/admin-options-page.php:205
msgid "Publish"
msgstr ""

#: pro/admin/admin-options-page.php:209
msgid "No Custom Field Groups found for this options page. <a href=\"%s\">Create a Custom Field Group</a>"
msgstr ""

#: pro/admin/admin-options-page.php:319
msgid "Edit field group"
msgstr ""

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr ""

#: pro/admin/admin-updates.php:122, pro/admin/admin-updates.php:122, pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr ""

#: pro/admin/admin-updates.php:212
msgid "<b>Error</b>. Could not authenticate update package. Please check again or deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:199
msgid "<b>Error</b>. Your license for this site has expired or been deactivated. Please reactivate your ACF PRO license."
msgstr ""

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:27, pro/fields/class-acf-field-repeater.php:31
msgid "Allows you to select and display existing fields. It does not duplicate any fields in the database, but loads and displays the selected fields at run-time. The Clone field can either replace itself with the selected fields or display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:819, pro/fields/class-acf-field-flexible-content.php:78
msgid "Fields"
msgstr ""

#: pro/fields/class-acf-field-clone.php:820
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:839
msgid "Display"
msgstr ""

#: pro/fields/class-acf-field-clone.php:840
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:845
msgid "Group (displays selected fields in a group within this field)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:846
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:855, pro/fields/class-acf-field-flexible-content.php:558, pro/fields/class-acf-field-flexible-content.php:616, pro/fields/class-acf-field-repeater.php:177
msgid "Layout"
msgstr ""

#: pro/fields/class-acf-field-clone.php:856
msgid "Specify the style used to render the selected fields"
msgstr ""

#: pro/fields/class-acf-field-clone.php:861, pro/fields/class-acf-field-flexible-content.php:629, pro/fields/class-acf-field-repeater.php:185, pro/locations/class-acf-location-block.php:22
msgid "Block"
msgstr ""

#: pro/fields/class-acf-field-clone.php:862, pro/fields/class-acf-field-flexible-content.php:628, pro/fields/class-acf-field-repeater.php:184
msgid "Table"
msgstr ""

#: pro/fields/class-acf-field-clone.php:863, pro/fields/class-acf-field-flexible-content.php:630, pro/fields/class-acf-field-repeater.php:186
msgid "Row"
msgstr ""

#: pro/fields/class-acf-field-clone.php:869
msgid "Labels will be displayed as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Labels"
msgstr ""

#: pro/fields/class-acf-field-clone.php:884
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:889
msgid "Prefix Field Names"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1006
msgid "Unknown field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1010
msgid "(no title)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1043
msgid "Unknown field group"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1047
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:27
msgid "Allows you to define, create and manage content with total control by creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:27
msgid "We do not recommend using this field in ACF Blocks."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:36, pro/fields/class-acf-field-repeater.php:103, pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:76, pro/fields/class-acf-field-flexible-content.php:943, pro/fields/class-acf-field-flexible-content.php:1022
msgid "layout"
msgid_plural "layouts"
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:81, pro/fields/class-acf-field-flexible-content.php:942, pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:420, pro/fields/class-acf-repeater-table.php:366
msgid "Drag to reorder"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Duplicate layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:426, pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:593
msgid "Label"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:609
msgid "Name"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:727, pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1710, pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:27
msgid "An interactive interface for managing a collection of attachments, such as images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:78
msgid "Add Image to Gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:79
msgid "Maximum selection reached"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:325
msgid "Length"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:340
msgid "Edit"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:341, pro/fields/class-acf-field-gallery.php:496
msgid "Remove"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:357
msgid "Title"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:369
msgid "Caption"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:381
msgid "Alt Text"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:393
msgid "Description"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:505
msgid "Add to gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:509
msgid "Bulk actions"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date uploaded"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by date modified"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:512
msgid "Sort by title"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:513
msgid "Reverse current order"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:525
msgid "Close"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:557
msgid "Return Format"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:563
msgid "Image Array"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:564
msgid "Image URL"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:565
msgid "Image ID"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:573
msgid "Library"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:574
msgid "Limit the media library choice"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:579, pro/locations/class-acf-location-block.php:66
msgid "All"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:580
msgid "Uploaded to post"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:616
msgid "Minimum Selection"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:626
msgid "Maximum Selection"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:636
msgid "Minimum"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:637, pro/fields/class-acf-field-gallery.php:673
msgid "Restrict which images can be uploaded"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:640, pro/fields/class-acf-field-gallery.php:676
msgid "Width"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:651, pro/fields/class-acf-field-gallery.php:687
msgid "Height"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:663, pro/fields/class-acf-field-gallery.php:699
msgid "File size"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:672
msgid "Maximum"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:708
msgid "Allowed File Types"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:709
msgid "Comma separated list. Leave blank for all types"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:728
msgid "Insert"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:729
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:733
msgid "Append to the end"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:734
msgid "Prepend to the beginning"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:742
msgid "Preview Size"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:845
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-repeater.php:29
msgid "Repeater"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:66, pro/fields/class-acf-field-repeater.php:463
msgid "Minimum rows not reached ({min} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:162
msgid "Sub Fields"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:195
msgid "Pagination"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1045
msgid "Invalid nonce."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1060
msgid "Invalid field key or name."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
msgid "Click to reorder"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:448, pro/fields/class-acf-repeater-table.php:465, pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:456, pro/fields/class-acf-repeater-table.php:457
msgid "First Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:460, pro/fields/class-acf-repeater-table.php:461
msgid "Previous Page"
msgstr ""

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477, pro/fields/class-acf-repeater-table.php:478
msgid "Next Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:481, pro/fields/class-acf-repeater-table.php:482
msgid "Last Page"
msgstr ""

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:22
msgid "Options Page"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr ""

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr ""

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr ""

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:34
msgid "To unlock updates, please enter your license key below. If you don't have a licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</a>."
msgstr ""

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr ""

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr ""

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr ""

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr ""

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr ""

#: pro/admin/views/html-settings-updates.php:91
msgid "No"
msgstr ""

#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr ""

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr ""

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
msgid "Enter your license key to unlock updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr ""

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr ""
