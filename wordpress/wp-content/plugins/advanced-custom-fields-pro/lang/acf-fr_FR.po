# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-06-27T14:09:59+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: fr_FR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:17
msgid "%s fields"
msgstr "%s champs"

#: includes/admin/post-types/admin-taxonomies.php:260
msgid "No terms"
msgstr "Aucun terme"

#: includes/admin/post-types/admin-taxonomies.php:233
msgid "No post types"
msgstr "Aucun type de publication"

#: includes/admin/post-types/admin-post-types.php:256
msgid "No posts"
msgstr "Aucun article"

#: includes/admin/post-types/admin-post-types.php:230
msgid "No taxonomies"
msgstr "Aucune taxonomie"

#: includes/admin/post-types/admin-post-types.php:175
#: includes/admin/post-types/admin-taxonomies.php:175
msgid "No field groups"
msgstr "Aucun groupe de champs"

#: includes/admin/post-types/admin-field-groups.php:259
msgid "No fields"
msgstr "Aucun champ"

#: includes/admin/post-types/admin-field-groups.php:132
#: includes/admin/post-types/admin-post-types.php:139
#: includes/admin/post-types/admin-taxonomies.php:139
msgid "No description"
msgstr "Aucune description"

#: includes/fields/class-acf-field-page_link.php:484
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:608
msgid "Any post status"
msgstr "Tout état de publication"

#: includes/post-types/class-acf-taxonomy.php:278
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"Cette clé de taxonomie est déjà utilisée par une autre taxonomie enregistrée "
"en dehors d’ACF et ne peut pas être utilisée."

#: includes/post-types/class-acf-taxonomy.php:273
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"Cette clé de taxonomie est déjà utilisée par une autre taxonomie dans ACF et "
"ne peut pas être utilisée."

#: includes/post-types/class-acf-taxonomy.php:246
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"La clé de taxonomie doit uniquement contenir des caractères alphanumériques, "
"des tirets bas ou des tirets."

#: includes/post-types/class-acf-taxonomy.php:241
msgid "The taxonomy key must be under 20 characters."
msgstr "La clé de taxonomie doit comporter moins de 20 caractères."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "Aucune taxonomie trouvée dans la corbeille"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "Aucune taxonomie trouvée"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Rechercher des taxonomies"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "Afficher la taxonomie"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "Nouvelle taxonomie"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Modifier la taxonomie"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Ajout une  nouvelle taxinomie"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found in Trash"
msgstr "Aucun type de publication trouvé dans la corbeille"

#: includes/post-types/class-acf-post-type.php:98
msgid "No Post Types found"
msgstr "Aucun type de publication trouvé"

#: includes/post-types/class-acf-post-type.php:97
msgid "Search Post Types"
msgstr "Rechercher des types de publication"

#: includes/post-types/class-acf-post-type.php:96
msgid "View Post Type"
msgstr "Voir le type de publication"

#: includes/post-types/class-acf-post-type.php:95
msgid "New Post Type"
msgstr "Nouveau type de publication"

#: includes/post-types/class-acf-post-type.php:94
msgid "Edit Post Type"
msgstr "Modifier le type de publication"

#: includes/post-types/class-acf-post-type.php:93
msgid "Add New Post Type"
msgstr "Ajouter un nouveau type de publication personnalisé"

#: includes/post-types/class-acf-post-type.php:338
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"Cette clé de type de publication est déjà utilisée par un autre type de "
"publication enregistré en dehors d’ACF et ne peut pas être utilisée."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"Cette clé de type de publication est déjà utilisée par un autre type de "
"publication dans ACF et ne peut pas être utilisée."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:312
#: includes/post-types/class-acf-taxonomy.php:252
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"Ce champ ne doit pas être un <a href=\"%s\" target=\"_blank\">terme réservé</"
"a> WordPress."

#: includes/post-types/class-acf-post-type.php:306
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"La clé du type de publication doit contenir uniquement des caractères "
"alphanumériques, des tirets bas ou des tirets."

#: includes/post-types/class-acf-post-type.php:301
msgid "The post type key must be under 20 characters."
msgstr "La clé du type de publication doit comporter moins de 20 caractères."

#: includes/fields/class-acf-field-wysiwyg.php:27
msgid "We do not recommend using this field in ACF Blocks."
msgstr "Nous vous déconseillons d’utiliser ce champ dans les blocs ACF."

#: includes/fields/class-acf-field-wysiwyg.php:27
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"Affiche l’éditeur WordPress WYSIWYG tel qu’il apparaît dans les articles et "
"pages, permettant une expérience d’édition de texte riche qui autorise "
"également du contenu multimédia."

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "WYSIWYG Editor"
msgstr "Éditeur WYSIWYG"

#: includes/fields/class-acf-field-user.php:22
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"Autorise la sélection d’un ou plusieurs comptes pouvant être utilisés pour "
"créer des relations entre les objets de données."

#: includes/fields/class-acf-field-url.php:26
msgid "A text input specifically designed for storing web addresses."
msgstr "Une saisie de texte spécialement conçue pour stocker des adresses web."

#: includes/fields/class-acf-field-url.php:25
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:27
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"Une permutation qui vous permet de choisir une valeur de 1 ou 0 (actif ou "
"inactif, vrai ou faux, etc.). Peut être présenté sous la forme de "
"commutateur stylisé ou de case à cocher."

#: includes/fields/class-acf-field-time_picker.php:27
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""

#: includes/fields/class-acf-field-textarea.php:26
msgid "A basic textarea input for storing paragraphs of text."
msgstr ""

#: includes/fields/class-acf-field-text.php:26
msgid "A basic text input, useful for storing single string values."
msgstr ""
"Une entrée de texte de base, utile pour stocker des valeurs de chaîne unique."

#: includes/fields/class-acf-field-taxonomy.php:30
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""

#: includes/fields/class-acf-field-tab.php:28
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""

#: includes/fields/class-acf-field-select.php:27
msgid "A dropdown list with a selection of choices that you specify."
msgstr "Une liste déroulante avec une sélection de choix que vous spécifiez."

#: includes/fields/class-acf-field-relationship.php:27
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""

#: includes/fields/class-acf-field-range.php:26
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""

#: includes/fields/class-acf-field-radio.php:27
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""

#: includes/fields/class-acf-field-post_object.php:27
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""

#: includes/fields/class-acf-field-password.php:26
msgid "An input for providing a password using a masked field."
msgstr "Une entrée pour fournir un mot de passe à l’aide d’un champ masqué."

#: includes/fields/class-acf-field-page_link.php:476
#: includes/fields/class-acf-field-post_object.php:388
#: includes/fields/class-acf-field-relationship.php:600
msgid "Filter by Post Status"
msgstr "Filtrer par état de publication"

#: includes/fields/class-acf-field-page_link.php:27
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""

#: includes/fields/class-acf-field-oembed.php:27
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""

#: includes/fields/class-acf-field-number.php:26
msgid "An input limited to numerical values."
msgstr "Une entrée limitée à des valeurs numériques."

#: includes/fields/class-acf-field-message.php:28
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"Utilisé pour afficher un message aux éditeurs à côté d’autres champs. Utile "
"pour fournir un contexte ou des instructions supplémentaires concernant vos "
"champs."

#: includes/fields/class-acf-field-link.php:27
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"Permet de spécifier un lien et ses propriétés, telles que le titre et la "
"cible en utilisant le sélecteur de liens de WordPress."

#: includes/fields/class-acf-field-image.php:27
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""
"Utilise le sélecteur de média natif de WordPress pour téléverser ou choisir "
"des images."

#: includes/fields/class-acf-field-group.php:27
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"Permet de structurer les champs en groupes afin de mieux organiser les "
"données et l’écran d‘édition."

#: includes/fields/class-acf-field-google-map.php:27
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""

#: includes/fields/class-acf-field-file.php:27
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""
"Utilise le sélecteur de médias WordPress natif pour téléverser ou choisir "
"des fichiers."

#: includes/fields/class-acf-field-email.php:26
msgid "A text input specifically designed for storing email addresses."
msgstr ""
"Une saisie de texte spécialement conçue pour stocker des adresses e-mail."

#: includes/fields/class-acf-field-date_time_picker.php:27
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"Une interface utilisateur interactive pour choisir une date et un horaire. "
"Le format de la date retour peut être personnalisé à l’aide des réglages du "
"champ."

#: includes/fields/class-acf-field-date_picker.php:27
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"Une interface utilisateur interactive pour choisir une date. Le format de la "
"date retour peut être personnalisé en utilisant les champs de réglages."

#: includes/fields/class-acf-field-color_picker.php:27
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""
"Une interface utilisateur interactive pour sélectionner une couleur ou "
"spécifier la valeur hexa."

#: includes/fields/class-acf-field-checkbox.php:27
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"Un groupe de case à cocher autorisant l’utilisateur/utilisatrice à "
"sélectionner une ou plusieurs valeurs que vous spécifiez."

#: includes/fields/class-acf-field-button-group.php:26
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"Un groupe de boutons avec des valeurs que vous spécifiez, les utilisateurs/"
"utilisatrices peuvent choisir une option parmi les valeurs fournies."

#: includes/fields/class-acf-field-accordion.php:27
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"Autorise à regrouper et organiser les champs personnalisés dans des volets "
"dépliants qui s’affichent lors de la modification du contenu. Utile pour "
"garder de grands ensembles de données ordonnés."

#: includes/fields.php:473
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"Cela propose une solution pour dupliquer des contenus tels que des "
"diapositive, des membres de l’équipe et des boutons d’appel à l‘action, en "
"agissant comme un parent pour un ensemble de sous-champs qui peuvent être "
"répétés à l’infini."

#: includes/fields.php:463
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"Cela propose une interface interactive permettant de gérer une collection de "
"fichiers joints. La plupart des réglages sont similaires à ceux du champ "
"Image. Des réglages supplémentaires vous autorise à spécifier l’endroit où "
"les nouveaux fichiers joints sont ajoutés dans la galerie et le nombre "
"minimum/maximum de fichiers joints autorisées."

#: includes/fields.php:453
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""

#: includes/fields.php:444
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""

#: includes/fields.php:441
msgctxt "noun"
msgid "Clone"
msgstr "Cloner"

#: includes/fields.php:357
msgid "PRO"
msgstr "Pro"

#: includes/fields.php:355
msgid "Advanced"
msgstr "Avancé"

#: includes/ajax/class-acf-ajax-local-json-diff.php:85
msgid "JSON (newer)"
msgstr "JSON (plus récent)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Original"
msgstr "Original"

#: includes/ajax/class-acf-ajax-local-json-diff.php:55
msgid "Invalid post ID."
msgstr "ID de publication invalide."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid post type selected for review."
msgstr "Type de publication sélectionné pour révision invalide."

#: includes/admin/views/global/navigation.php:104
msgid "More"
msgstr "Plus"

#: includes/admin/views/browse-fields-modal.php:86
msgid "Tutorial"
msgstr "Tutoriel"

#: includes/admin/views/browse-fields-modal.php:75
msgid "Available with ACF PRO"
msgstr "Disponible avec ACF Pro"

#: includes/admin/views/browse-fields-modal.php:63
msgid "Select Field"
msgstr "Sélectionner le champ"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:50
msgid "Try a different search term or browse %s"
msgstr "Essayez un autre terme de recherche ou parcourez %s"

#: includes/admin/views/browse-fields-modal.php:47
msgid "Popular fields"
msgstr "Champs populaires"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:40
msgid "No search results for '%s'"
msgstr "Aucun résultat de recherche pour « %s »"

#: includes/admin/views/browse-fields-modal.php:13
msgid "Search fields..."
msgstr "Rechercher des champs…"

#: includes/admin/views/browse-fields-modal.php:11
msgid "Select Field Type"
msgstr "Sélectionner le type de champ"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Populaire"

#: includes/admin/views/acf-taxonomy/list-empty.php:7
msgid "Add Taxonomy"
msgstr "Ajouter une taxonomie"

#: includes/admin/views/acf-taxonomy/list-empty.php:6
msgid "Create custom taxonomies to classify post type content"
msgstr ""
"Créer des taxonomies personnalisées pour classer le contenu du type de "
"publication"

#: includes/admin/views/acf-taxonomy/list-empty.php:5
msgid "Add Your First Taxonomy"
msgstr "Ajouter votre première taxonomie"

#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr ""
"Les taxonomies hiérarchiques peuvent avoir des enfants (comme les "
"catégories)."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""
"Rend une taxonomie visible sur l’interface publique et dans le tableau de "
"bord d’administration."

#: includes/admin/views/acf-taxonomy/basic-settings.php:75
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""
"Un ou plusieurs types de publication peuvant être classés avec cette "
"taxonomie."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:44
msgid "genre"
msgstr "genre"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:26
msgid "Genre"
msgstr "Genre"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:9
msgid "Genres"
msgstr "Genres"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1129
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""
"Contrôleur personnalisé facultatif à utiliser à la place de "
"« WP_REST_Terms_Controller »."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1073
msgid "Expose this post type in the REST API."
msgstr "Exposez ce type de publication dans l’API REST."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Customize the query variable name"
msgstr "Personnaliser le nom de la variable de requête"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1024
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:977
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "Termes parent-enfant dans les URL pour les taxonomies hiérarchiques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:937
msgid "Customize the slug used in the URL"
msgstr "Personnaliser le slug utilisé dans l’URL"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:920
msgid "Permalinks for this taxonomy are disabled."
msgstr "Les permaliens sont désactivés pour cette taxonomie."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:917
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"Réécrire l’URL en utilisant la clé de taxonomie comme slug. Votre structure "
"de permalien sera"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:909
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1026
#: includes/admin/views/acf-taxonomy/basic-settings.php:41
msgid "Taxonomy Key"
msgstr "Clé de taxonomie"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:907
msgid "Select the type of permalink to use for this taxonomy."
msgstr "Sélectionnez le type de permalien à utiliser pour cette taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:892
msgid "Display a column for the taxonomy on post type listing screens."
msgstr ""
"Affichez une colonne pour la taxonomie sur les écrans de liste de type de "
"publication."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:891
msgid "Show Admin Column"
msgstr "Afficher la colonne « Admin »"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:878
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "Afficher la taxonomie dans le panneau de modification rapide/groupée."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:877
msgid "Quick Edit"
msgstr "Modification rapide"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:864
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr ""
"Lister la taxonomie dans les contrôles du widget « Nuage d’étiquettes »."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:863
msgid "Tag Cloud"
msgstr "Nuage d’étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:820
msgid ""
"A PHP function name to be called tor sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"Un nom de fonction PHP à appeler pour assainir les données de taxonomie "
"enregistrées à partir d’une boîte méta."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:819
msgid "Meta Box Sanitization Callback"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:801
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:800
msgid "Register Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:759
msgid "No Meta Box"
msgstr "Aucune boîte méta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:758
msgid "Custom Meta Box"
msgstr "Boîte méta personnalisée"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:754
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:753
msgid "Meta Box"
msgstr "Boîte méta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:742
#: includes/admin/views/acf-taxonomy/advanced-settings.php:763
msgid "Categories Meta Box"
msgstr "Boîte méta des catégories"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:741
#: includes/admin/views/acf-taxonomy/advanced-settings.php:762
msgid "Tags Meta Box"
msgstr "Boîte méta des étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:700
msgid "A link to a tag"
msgstr "Un lien vers une étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:699
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""
"Décrit une variante de bloc de lien de navigation utilisée dans l’éditeur de "
"blocs."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:694
msgid "A link to a %s"
msgstr "Un lien vers un %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:679
msgid "Tag Link"
msgstr "Lien de l’étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:659
msgid "← Go to tags"
msgstr "← Aller aux étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:658
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "Back To Items"
msgstr "Retour aux éléments"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:653
msgid "← Go to %s"
msgstr "← Aller à « %s »"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:638
msgid "Tags list"
msgstr "Liste des étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "Assigns text to the table hidden heading."
msgstr "Assigne du texte au titre masqué du tableau."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:618
msgid "Tags list navigation"
msgstr "Navigation de la liste des étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "Assigns text to the table pagination hidden heading."
msgstr "Affecte du texte au titre masqué de pagination de tableau."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:593
msgid "Filter by category"
msgstr "Filtrer par catégorie"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:592
msgid "Assigns text to the filter button in the posts lists table."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter By Item"
msgstr "Filtrer par élément"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:587
msgid "Filter by %s"
msgstr "Filtrer par %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:571
#: includes/admin/views/acf-taxonomy/advanced-settings.php:572
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:570
msgid "Describes the Description field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:569
msgid "Description Field Description"
msgstr "Description du champ « Description »"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:550
#: includes/admin/views/acf-taxonomy/advanced-settings.php:551
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:549
msgid "Describes the Parent field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:548
msgid "Parent Field Description"
msgstr "Description du champ « Parent »"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:534
#: includes/admin/views/acf-taxonomy/advanced-settings.php:535
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:533
msgid "Describes the Slug field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:532
msgid "Slug Field Description"
msgstr "Description du champ « Slug »"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:518
#: includes/admin/views/acf-taxonomy/advanced-settings.php:519
msgid "The name is how it appears on your site"
msgstr "Le nom est la façon dont il apparaît sur votre site"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:517
msgid "Describes the Name field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:516
msgid "Name Field Description"
msgstr "Description du champ « Nom »"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:503
msgid "No tags"
msgstr "Aucune étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:502
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No Terms"
msgstr "Aucun terme"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:497
msgid "No %s"
msgstr "Aucun %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:482
msgid "No tags found"
msgstr "Aucune étiquette trouvée"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:481
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "Not Found"
msgstr "Non trouvé"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:459
msgid "Assigns text to the Title field of the Most Used tab."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:458
#: includes/admin/views/acf-taxonomy/advanced-settings.php:460
#: includes/admin/views/acf-taxonomy/advanced-settings.php:461
msgid "Most Used"
msgstr "Les plus utilisés"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:440
msgid "Choose from the most used tags"
msgstr "Choisir parmi les étiquettes les plus utilisées"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:439
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose From Most Used"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:434
msgid "Choose from the most used %s"
msgstr "Choisir parmi les %s les plus utilisés"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:414
msgid "Add or remove tags"
msgstr "Ajouter ou retirer des étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:413
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"Assigne le texte d’ajout ou de suppression d’éléments utilisé dans la boîte "
"méta lorsque JavaScript est désactivé. Utilisé uniquement sur les taxonomies "
"non hiérarchiques"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add Or Remove Items"
msgstr "Ajouter ou supprimer des éléments"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:408
msgid "Add or remove %s"
msgstr "Ajouter ou retirer %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:388
msgid "Separate tags with commas"
msgstr "Séparer les étiquettes par des virgules"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:387
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate Items With Commas"
msgstr "Séparer les éléments par des virgules"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:382
msgid "Separate %s with commas"
msgstr "Séparer les %s avec une virgule"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:362
msgid "Popular Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:360
msgid "Popular Items"
msgstr "Éléments populaires"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:357
msgid "Popular %s"
msgstr "%s populaire"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:343
msgid "Search Tags"
msgstr "Rechercher des étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Assigns search items text."
msgstr "Assigne le texte des éléments de recherche."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:319
msgid "Parent Category:"
msgstr "Catégorie parente :"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""
"Assigne le texte de l’élément parent, mais avec deux points (:) ajouté à la "
"fin."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:317
msgid "Parent Item With Colon"
msgstr "Élément parent avec deux-points"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:294
msgid "Parent Category"
msgstr "Catégorie parente"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""
"Assigne le texte de l’élément parent. Utilisé uniquement sur les taxonomies "
"hiérarchiques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:292
msgid "Parent Item"
msgstr "Élément parent"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:289
msgid "Parent %s"
msgstr "%s parent"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:274
msgid "New Tag Name"
msgstr "Nom de la nouvelle étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "Assigns the new item name text."
msgstr "Assigne le texte du nom du nouvel élément."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:272
msgid "New Item Name"
msgstr "Nom du nouvel élément"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:269
msgid "New %s Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:254
msgid "Add New Tag"
msgstr "Ajouter une nouvelle étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Assigns the add new item text."
msgstr "Assigne le texte « Ajouter un nouvel élément »."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:234
msgid "Update Tag"
msgstr "Mettre à jour l’étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Assigns the update item text."
msgstr "Assigne le texte de l’élément « Mettre à jour »."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:232
msgid "Update Item"
msgstr "Mettre à jour l’élément"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:229
msgid "Update %s"
msgstr "Mettre à jour %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:214
msgid "View Tag"
msgstr "Voir l’étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "In the admin bar to view term during editing."
msgstr ""
"Dans la barre d’administration pour voir le terme lors de la modification."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:194
msgid "Edit Tag"
msgstr "Modifier l’étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "At the top of the editor screen when editing a term."
msgstr "En haut de l’écran de l’éditeur lors de la modification d’un terme."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:174
msgid "All Tags"
msgstr "Toutes les étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "Assigns the all items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:154
msgid "Assigns the menu name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:153
msgid "Menu Label"
msgstr "Libellé du menu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:127
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "Les taxonomies actives sont activées et enregistrées avec WordPress."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:111
msgid "A descriptive summary of the taxonomy."
msgstr "Un résumé descriptif de la taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:91
msgid "A descriptive summary of the term."
msgstr "Un résumé descriptif du terme."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:90
msgid "Term Description"
msgstr "Description du terme"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:72
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "Un seul mot, aucun espace. Tirets bas et tirets autorisés."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:71
msgid "Term Slug"
msgstr "Slug du terme"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:52
msgid "The name of the default term."
msgstr "Nom du terme par défaut."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:51
msgid "Term Name"
msgstr "Nom du terme"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:37
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:36
msgid "Default Term"
msgstr "Terme par défaut"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:24
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:23
msgid "Sort Terms"
msgstr "Trier les termes"

#: includes/admin/views/acf-post-type/list-empty.php:7
msgid "Add Post Type"
msgstr "Ajouter un type de publication"

#: includes/admin/views/acf-post-type/list-empty.php:6
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:5
msgid "Add Your First Post Type"
msgstr "Ajouter votre premier type de publication"

#: includes/admin/views/acf-post-type/basic-settings.php:120
#: includes/admin/views/acf-taxonomy/basic-settings.php:119
msgid "I know what I'm doing, show me all the options."
msgstr "Je sais ce que je fais, affichez-moi toutes les options."

#: includes/admin/views/acf-post-type/basic-settings.php:119
#: includes/admin/views/acf-taxonomy/basic-settings.php:118
msgid "Advanced Configuration"
msgstr "Configuration avancée"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Hierarchical post types can have descendants (like pages)."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/advanced-settings.php:976
#: includes/admin/views/acf-taxonomy/basic-settings.php:105
msgid "Hierarchical"
msgstr "Hiérachique"

#: includes/admin/views/acf-post-type/basic-settings.php:91
msgid "Visible on the frontend and in the admin dashboard."
msgstr ""
"Visible sur l’interface publique et dans le tableau de bord de "
"l’administration."

#: includes/admin/views/acf-post-type/basic-settings.php:90
#: includes/admin/views/acf-taxonomy/basic-settings.php:90
msgid "Public"
msgstr "Public"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:43
msgid "movie"
msgstr "film"

#: includes/admin/views/acf-post-type/basic-settings.php:41
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr ""
"Lettres minuscules, tiret bas et tirets uniquement, maximum 20 caractères."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:25
msgid "Movie"
msgstr "Film"

#: includes/admin/views/acf-post-type/basic-settings.php:23
#: includes/admin/views/acf-taxonomy/basic-settings.php:24
msgid "Singular Label"
msgstr "Libellé au singulier"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:8
msgid "Movies"
msgstr "Films"

#: includes/admin/views/acf-post-type/basic-settings.php:6
#: includes/admin/views/acf-taxonomy/basic-settings.php:7
msgid "Plural Label"
msgstr "Libellé au pluriel"

#: includes/admin/views/acf-post-type/advanced-settings.php:1250
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""
"Contrôleur personnalisé facultatif à utiliser à la place de "
"« WP_REST_Posts_Controller »."

#: includes/admin/views/acf-post-type/advanced-settings.php:1249
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1128
msgid "Controller Class"
msgstr "Classe de contrôleur"

#: includes/admin/views/acf-post-type/advanced-settings.php:1231
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1109
msgid "The namespace part of the REST API URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1230
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1108
msgid "Namespace Route"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1212
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1090
msgid "The base URL for the post type REST API URLs."
msgstr "URL de base pour les URL de l’API REST du type de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1089
msgid "Base URL"
msgstr "URL de base"

#: includes/admin/views/acf-post-type/advanced-settings.php:1197
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"Expose ce type de publication dans l’API REST. Nécessaire pour utiliser "
"l’éditeur de bloc."

#: includes/admin/views/acf-post-type/advanced-settings.php:1196
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1072
msgid "Show In REST API"
msgstr "Afficher dans l’API Rest"

#: includes/admin/views/acf-post-type/advanced-settings.php:1175
msgid "Customize the query variable name."
msgstr "Personnaliser le nom de la variable de requête."

#: includes/admin/views/acf-post-type/advanced-settings.php:1174
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
msgid "Query Variable"
msgstr "Variable de requête"

#: includes/admin/views/acf-post-type/advanced-settings.php:1152
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1028
msgid "No Query Variable Support"
msgstr "Aucune prise en charge des variables de requête"

#: includes/admin/views/acf-post-type/advanced-settings.php:1151
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1027
msgid "Custom Query Variable"
msgstr "Variable de requête personnalisée"

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1147
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "Query Variable Support"
msgstr "Prise en charge des variables de requête"

#: includes/admin/views/acf-post-type/advanced-settings.php:1122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:999
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:998
msgid "Publicly Queryable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1100
msgid "Custom slug for the Archive URL."
msgstr "Slug personnalisé pour l’URL de l’archive."

#: includes/admin/views/acf-post-type/advanced-settings.php:1099
msgid "Archive Slug"
msgstr "Slug de l’archive"

#: includes/admin/views/acf-post-type/advanced-settings.php:1086
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""
"Possède une archive d’élément qui peut être personnalisée avec un fichier de "
"modèle d’archive dans votre thème."

#: includes/admin/views/acf-post-type/advanced-settings.php:1085
msgid "Archive"
msgstr "Archive"

#: includes/admin/views/acf-post-type/advanced-settings.php:1065
msgid "Pagination support for the items URLs such as the archives."
msgstr ""
"Prise en charge de la pagination pour les URL des éléments tels que les "
"archives."

#: includes/admin/views/acf-post-type/advanced-settings.php:1064
msgid "Pagination"
msgstr "Pagination"

#: includes/admin/views/acf-post-type/advanced-settings.php:1047
msgid "RSS feed URL for the post type items."
msgstr "URL de flux RSS pour les éléments du type de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:1046
msgid "Feed URL"
msgstr "URL du flux"

#: includes/admin/views/acf-post-type/advanced-settings.php:1028
#: includes/admin/views/acf-taxonomy/advanced-settings.php:957
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""
"Modifie la structure du permalien pour ajouter le préfixe « WP_Rewrite::"
"$front » aux URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1027
#: includes/admin/views/acf-taxonomy/advanced-settings.php:956
msgid "Front URL Prefix"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1008
msgid "Customize the slug used in the URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1007
#: includes/admin/views/acf-taxonomy/advanced-settings.php:936
msgid "URL Slug"
msgstr "Slug de l’URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:991
msgid "Permalinks for this post type are disabled."
msgstr "Les permaliens sont désactivés pour ce type de publication."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:990
#: includes/admin/views/acf-taxonomy/advanced-settings.php:919
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:982
#: includes/admin/views/acf-taxonomy/advanced-settings.php:911
msgid "No Permalink (prevent URL rewriting)"
msgstr "Aucun permalien (empêcher la réécriture d’URL)"

#: includes/admin/views/acf-post-type/advanced-settings.php:981
#: includes/admin/views/acf-taxonomy/advanced-settings.php:910
msgid "Custom Permalink"
msgstr "Permalien personnalisé"

#: includes/admin/views/acf-post-type/advanced-settings.php:980
#: includes/admin/views/acf-post-type/advanced-settings.php:1150
#: includes/admin/views/acf-post-type/basic-settings.php:40
msgid "Post Type Key"
msgstr "Clé du type de publication"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:978
#: includes/admin/views/acf-post-type/advanced-settings.php:988
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""
"Réécrire l’URL en utilisant la clé du type de publication comme slug. Votre "
"structure de permalien sera"

#: includes/admin/views/acf-post-type/advanced-settings.php:976
#: includes/admin/views/acf-taxonomy/advanced-settings.php:906
msgid "Permalink Rewrite"
msgstr "Réécriture du permalien"

#: includes/admin/views/acf-post-type/advanced-settings.php:962
msgid "Delete items by a user when that user is deleted."
msgstr "Supprimer les éléments d’un compte lorsque ce dernier est supprimé."

#: includes/admin/views/acf-post-type/advanced-settings.php:961
msgid "Delete With User"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:947
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr ""
"Autoriser l’exportation du type de publication depuis « Outils » > "
"« Exporter »."

#: includes/admin/views/acf-post-type/advanced-settings.php:946
msgid "Can Export"
msgstr "Exportable"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
msgid "Optionally provide a plural to be used in capabilities."
msgstr ""
"Fournissez éventuellement un pluriel à utiliser dans les fonctionnalités."

#: includes/admin/views/acf-post-type/advanced-settings.php:914
msgid "Plural Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Singular Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:881
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:880
msgid "Rename Capabilities"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:866
msgid "Sets whether posts should be excluded from search results."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:865
msgid "Exclude From Search"
msgstr "Exclure de la recherche"

#: includes/admin/views/acf-post-type/advanced-settings.php:852
#: includes/admin/views/acf-taxonomy/advanced-settings.php:850
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:851
#: includes/admin/views/acf-taxonomy/advanced-settings.php:849
msgid "Appearance Menus Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:833
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr ""
"Apparaît en tant qu’élément dans le menu « Nouveau » de la barre "
"d’administration."

#: includes/admin/views/acf-post-type/advanced-settings.php:832
msgid "Show In Admin Bar"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:801
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:800
msgid "Custom Meta Box Callback"
msgstr "Rappel de boîte méta personnalisée"

#: includes/admin/views/acf-post-type/advanced-settings.php:780
msgid "Menu Icon"
msgstr "Icône de menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:762
msgid "The position in the sidebar menu in the admin dashboard."
msgstr ""
"Position dans le menu de la colonne latérale du tableau de bord "
"d’administration."

#: includes/admin/views/acf-post-type/advanced-settings.php:761
msgid "Menu Position"
msgstr "Position du menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:743
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:742
msgid "Admin Menu Parent"
msgstr ""

#. translators: %s = "dashicon class name", link to the WordPress dashicon
#. documentation.
#: includes/admin/views/acf-post-type/advanced-settings.php:730
msgid ""
"The icon used for the post type menu item in the admin dashboard. Can be a "
"URL or %s to use for the icon."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:725
msgid "Dashicon class name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:714
#: includes/admin/views/acf-taxonomy/advanced-settings.php:730
msgid "Admin editor navigation in the sidebar menu."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:713
#: includes/admin/views/acf-taxonomy/advanced-settings.php:729
msgid "Show In Admin Menu"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:700
#: includes/admin/views/acf-taxonomy/advanced-settings.php:715
msgid "Items can be edited and managed in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:699
#: includes/admin/views/acf-taxonomy/advanced-settings.php:714
msgid "Show In UI"
msgstr "Afficher dans l’interface utilisateur"

#: includes/admin/views/acf-post-type/advanced-settings.php:685
msgid "A link to a post."
msgstr "Un lien vers une publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:684
msgid "Description for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:683
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "Item Link Description"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:679
msgid "A link to a %s."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:664
msgid "Post Link"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:663
msgid "Title for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:662
#: includes/admin/views/acf-taxonomy/advanced-settings.php:677
msgid "Item Link"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:659
#: includes/admin/views/acf-taxonomy/advanced-settings.php:674
msgid "%s Link"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:644
msgid "Post updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:643
msgid "In the editor notice after an item is updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:642
msgid "Item Updated"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:639
msgid "%s updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:624
msgid "Post scheduled."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:623
msgid "In the editor notice after scheduling an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:622
msgid "Item Scheduled"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:619
msgid "%s scheduled."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:604
msgid "Post reverted to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:603
msgid "In the editor notice after reverting an item to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:602
msgid "Item Reverted To Draft"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:599
msgid "%s reverted to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:584
msgid "Post published privately."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:583
msgid "In the editor notice after publishing a private item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:582
msgid "Item Published Privately"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:579
msgid "%s published privately."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:564
msgid "Post published."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:563
msgid "In the editor notice after publishing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:562
msgid "Item Published"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:559
msgid "%s published."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:544
msgid "Posts list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:543
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:542
#: includes/admin/views/acf-taxonomy/advanced-settings.php:636
msgid "Items List"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:539
#: includes/admin/views/acf-taxonomy/advanced-settings.php:633
msgid "%s list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:524
msgid "Posts list navigation"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:523
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:616
msgid "Items List Navigation"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:519
#: includes/admin/views/acf-taxonomy/advanced-settings.php:613
msgid "%s list navigation"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:503
msgid "Filter posts by date"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:502
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:501
msgid "Filter Items By Date"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:497
msgid "Filter %s by date"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:482
msgid "Filter posts list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:481
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:480
msgid "Filter Items List"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:476
msgid "Filter %s list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:460
msgid "In the media modal showing all media uploaded to this item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:459
msgid "Uploaded To This Item"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:455
msgid "Uploaded to this %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:440
msgid "Insert into post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:439
msgid "As the button label when adding media to content."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:438
msgid "Insert Into Media Button"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:434
msgid "Insert into %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:419
msgid "Use as featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:418
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:417
msgid "Use Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:404
msgid "Remove featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:403
msgid "As the button label when removing the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:402
msgid "Remove Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:389
msgid "Set featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:388
msgid "As the button label when setting the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:387
msgid "Set Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:374
msgid "Featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:373
msgid "In the editor used for the title of the featured image meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:372
msgid "Featured Image Meta Box"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:359
msgid "Post Attributes"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:358
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:357
msgid "Attributes Meta Box"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:354
msgid "%s Attributes"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:339
msgid "Post Archives"
msgstr "Archives de publication"

#: includes/admin/views/acf-post-type/advanced-settings.php:338
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:337
msgid "Archives Nav Menu"
msgstr "Menu de navigation des archives"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:334
msgid "%s Archives"
msgstr "Archives des %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:319
msgid "No posts found in Trash"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:318
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:317
msgid "No Items Found in Trash"
msgstr "Aucun élément trouvé dans la corbeille"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:313
msgid "No %s found in Trash"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:298
msgid "No posts found"
msgstr "Aucune publication trouvée"

#: includes/admin/views/acf-post-type/advanced-settings.php:297
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:296
msgid "No Items Found"
msgstr "Aucun élément trouvé"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:292
#: includes/admin/views/acf-taxonomy/advanced-settings.php:476
msgid "No %s found"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:277
msgid "Search Posts"
msgstr "Rechercher des publications"

#: includes/admin/views/acf-post-type/advanced-settings.php:276
msgid "At the top of the items screen when searching for an item."
msgstr "En haut de l’écran des éléments lors de la recherche d’un élément."

#: includes/admin/views/acf-post-type/advanced-settings.php:275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:341
msgid "Search Items"
msgstr "Rechercher des éléments"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:272
#: includes/admin/views/acf-taxonomy/advanced-settings.php:338
msgid "Search %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:257
msgid "Parent Page:"
msgstr "Page parente :"

#: includes/admin/views/acf-post-type/advanced-settings.php:256
msgid "For hierarchical types in the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:255
msgid "Parent Item Prefix"
msgstr "Préfixe de l’élément parent"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:252
#: includes/admin/views/acf-taxonomy/advanced-settings.php:314
msgid "Parent %s:"
msgstr "%s parent :"

#: includes/admin/views/acf-post-type/advanced-settings.php:237
msgid "New Post"
msgstr "Nouvelle publication"

#: includes/admin/views/acf-post-type/advanced-settings.php:235
msgid "New Item"
msgstr "Nouvel élément"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:232
msgid "New %s"
msgstr "Nouveau %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:202
msgid "Add New Post"
msgstr "Ajouter une nouvelle publication"

#: includes/admin/views/acf-post-type/advanced-settings.php:201
msgid "At the top of the editor screen when adding a new item."
msgstr "En haut de l’écran de l’éditeur lors de l’ajout d’un nouvel élément."

#: includes/admin/views/acf-post-type/advanced-settings.php:200
#: includes/admin/views/acf-taxonomy/advanced-settings.php:252
msgid "Add New Item"
msgstr "Ajouter un nouvel élément"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:197
#: includes/admin/views/acf-taxonomy/advanced-settings.php:249
msgid "Add New %s"
msgstr "Ajouter %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:182
msgid "View Posts"
msgstr "Voir les publications"

#: includes/admin/views/acf-post-type/advanced-settings.php:181
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:180
msgid "View Items"
msgstr "Voir les éléments"

#: includes/admin/views/acf-post-type/advanced-settings.php:162
msgid "View Post"
msgstr "Voir la publication"

#: includes/admin/views/acf-post-type/advanced-settings.php:161
msgid "In the admin bar to view item when editing it."
msgstr ""
"Dans la barre d’administration pour afficher l’élément lors de sa "
"modification."

#: includes/admin/views/acf-post-type/advanced-settings.php:160
#: includes/admin/views/acf-taxonomy/advanced-settings.php:212
msgid "View Item"
msgstr "Voir l’élément"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:157
#: includes/admin/views/acf-post-type/advanced-settings.php:177
#: includes/admin/views/acf-taxonomy/advanced-settings.php:209
msgid "View %s"
msgstr "Voir %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:142
msgid "Edit Post"
msgstr "Modifier la publication"

#: includes/admin/views/acf-post-type/advanced-settings.php:141
msgid "At the top of the editor screen when editing an item."
msgstr "En haut de l’écran de l’éditeur lors de la modification d’un élément."

#: includes/admin/views/acf-post-type/advanced-settings.php:140
#: includes/admin/views/acf-taxonomy/advanced-settings.php:192
msgid "Edit Item"
msgstr "Modifier l’élément"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:137
#: includes/admin/views/acf-taxonomy/advanced-settings.php:189
msgid "Edit %s"
msgstr "Modifier %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:122
msgid "All Posts"
msgstr "Toutes les publications"

#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-post-type/advanced-settings.php:216
#: includes/admin/views/acf-post-type/advanced-settings.php:236
msgid "In the post type submenu in the admin dashboard."
msgstr ""
"Dans le sous-menu de type de publication du tableau de bord d’administration."

#: includes/admin/views/acf-post-type/advanced-settings.php:120
#: includes/admin/views/acf-taxonomy/advanced-settings.php:172
msgid "All Items"
msgstr "Tous les éléments"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:117
#: includes/admin/views/acf-taxonomy/advanced-settings.php:169
msgid "All %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:101
msgid "Admin menu name for the post type."
msgstr "Nom du menu d’administration pour le type de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:100
msgid "Menu Name"
msgstr "Nom du menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:86
#: includes/admin/views/acf-taxonomy/advanced-settings.php:138
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:84
#: includes/admin/views/acf-taxonomy/advanced-settings.php:136
msgid "Regenerate"
msgstr "Régénérer"

#: includes/admin/views/acf-post-type/advanced-settings.php:75
msgid "Active post types are enabled and registered with WordPress."
msgstr ""
"Les types de publication actifs sont activés et enregistrés avec WordPress."

#: includes/admin/views/acf-post-type/advanced-settings.php:59
msgid "A descriptive summary of the post type."
msgstr "Un résumé descriptif du type de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:44
msgid "Add Custom"
msgstr "Ajouter une personalisation"

#: includes/admin/views/acf-post-type/advanced-settings.php:38
msgid "Enable various features in the content editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Post Formats"
msgstr "Formats des publications"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
msgid "Editor"
msgstr "Éditeur"

#: includes/admin/views/acf-post-type/advanced-settings.php:20
msgid "Trackbacks"
msgstr "Rétroliens"

#: includes/admin/views/acf-post-type/basic-settings.php:71
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""

#: includes/admin/views/acf-field-group/field.php:141
msgid "Browse Fields"
msgstr "Parcourir les champs"

#: includes/admin/tools/class-acf-admin-tool-import.php:292
msgid "Nothing to import"
msgstr "Rien à importer"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ". L’extension Custom Post Type UI peut être désactivée."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:278
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:262
msgid "Failed to import taxonomies."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:244
msgid "Failed to import post types."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:233
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:209
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:122
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:127
msgid "Import from Custom Post Type UI"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:390
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's functions."
"php file or include it within an external file, then deactivate or delete "
"the items from the ACF admin."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:389
msgid "Export - Generate PHP"
msgstr "Exportation - Générer PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:362
msgid "Export"
msgstr "Exporter"

#: includes/admin/tools/class-acf-admin-tool-export.php:276
msgid "Select Taxonomies"
msgstr "Sélectionner les taxonomies"

#: includes/admin/tools/class-acf-admin-tool-export.php:254
msgid "Select Post Types"
msgstr "Sélectionner les types de publication"

#: includes/admin/tools/class-acf-admin-tool-export.php:167
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-taxonomy.php:124
#: assets/build/js/acf-internal-post-type.js:144
#: assets/build/js/acf-internal-post-type.js:204
msgid "Category"
msgstr "Catégorie"

#: includes/admin/post-types/admin-taxonomy.php:122
#: assets/build/js/acf-internal-post-type.js:141
#: assets/build/js/acf-internal-post-type.js:201
msgid "Tag"
msgstr "Étiquette"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:103
msgid "Create new post type"
msgstr "Créer un nouveau type de publication"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "Brouillon de taxonomie mis à jour."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "Taxonomie planifiée pour."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "Taxonomie envoyée."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "Taxonomie enregistrée."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "Taxonomie supprimée."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "Taxonomie mise à jour."

#: includes/admin/post-types/admin-taxonomies.php:344
#: includes/admin/post-types/admin-taxonomy.php:152
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "Taxonomie synchronisée."
msgstr[1] "%s taxonomies synchronisées."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "Taxonomie dupliquée."
msgstr[1] "%s taxonomies dupliquées."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "Taxonomie désactivée."
msgstr[1] "%s taxonomies désactivées."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:305
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "Taxonomie activée."
msgstr[1] "%s taxonomies activées."

#: includes/admin/post-types/admin-taxonomies.php:106
msgid "Terms"
msgstr "Termes"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:319
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "Type de publication synchronisé."
msgstr[1] "%s types de publication synchronisés."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:312
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "Type de publication dupliqué."
msgstr[1] "%s types de publication dupliqués."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:305
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "Type de publication désactivé."
msgstr[1] "%s types de publication désactivés."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:298
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "Type de publication activé."
msgstr[1] "%s types de publication activés."

#: includes/admin/post-types/admin-post-types.php:79
#: includes/admin/post-types/admin-taxonomies.php:104
#: includes/admin/tools/class-acf-admin-tool-import.php:82
#: includes/admin/views/acf-taxonomy/basic-settings.php:66
#: includes/post-types/class-acf-post-type.php:90
msgid "Post Types"
msgstr "Types de publication"

#: includes/admin/post-types/admin-post-type.php:159
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Advanced Settings"
msgstr "Réglages avancés"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:158
msgid "Basic Settings"
msgstr "Réglages de base"

#: includes/admin/post-types/admin-post-type.php:152
#: includes/admin/post-types/admin-post-types.php:337
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:125
#: assets/build/js/acf-internal-post-type.js:138
#: assets/build/js/acf-internal-post-type.js:198
msgid "Pages"
msgstr "Pages"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:102
msgid "Create new taxonomy"
msgstr "Créer une nouvelle taxonomie"

#: includes/admin/post-types/admin-post-type.php:101
#: includes/admin/post-types/admin-taxonomy.php:101
msgid "Link existing field groups"
msgstr ""

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:82
msgid "%s post type created"
msgstr ""

#. translators: %s post type name
#. translators: %s taxonomy name
#: includes/admin/post-types/admin-post-type.php:78
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr ""

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "Type de publication planifié pour."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "Type de publication enregistré."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "Type de publication mis à jour."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "Type de publication supprimé."

#: includes/admin/post-types/admin-field-group.php:120
#: assets/build/js/acf-field-group.js:1146
#: assets/build/js/acf-field-group.js:1366
msgid "Type to search..."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:105
#: assets/build/js/acf-field-group.js:1172
#: assets/build/js/acf-field-group.js:2295
#: assets/build/js/acf-field-group.js:1414
#: assets/build/js/acf-field-group.js:2689
msgid "PRO Only"
msgstr "Uniquement sur PRO"

#: includes/admin/post-types/admin-field-group.php:97
#: assets/build/js/acf-internal-post-type.js:270
#: assets/build/js/acf-internal-post-type.js:365
msgid "Field groups linked successfully."
msgstr ""

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:194
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""

#: includes/admin/admin.php:48
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:337
msgid "taxonomy"
msgstr "taxonomie"

#: includes/admin/admin-internal-post-type.php:337
msgid "post type"
msgstr "type de publication"

#. translators: %1$s - name of newly created post. %2$s - either "post type" or
#. "taxonomy".
#: includes/admin/admin-internal-post-type.php:335
msgid "Link %1$s %2$s to field groups"
msgstr "Lier %1$s %2$s aux groupes de champs"

#: includes/admin/admin-internal-post-type.php:328
msgid "Done"
msgstr "Terminé"

#: includes/admin/admin-internal-post-type.php:315
msgid "Field group(s)"
msgstr "Groupe(s) de champs"

#: includes/admin/admin-internal-post-type.php:314
msgid "Select one or many field groups..."
msgstr ""

#: includes/admin/admin-internal-post-type.php:313
msgid "Please select the field groups to link."
msgstr "Veuillez choisir les groupes de champs à lier."

#: includes/admin/admin-internal-post-type.php:277
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-internal-post-type-list.php:255
#: includes/admin/post-types/admin-post-types.php:338
#: includes/admin/post-types/admin-taxonomies.php:345
msgctxt "post status"
msgid "Registration Failed"
msgstr "Echec de l’enregistrement"

#: includes/admin/admin-internal-post-type-list.php:254
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""

#: includes/acf-internal-post-type-functions.php:482
#: includes/acf-internal-post-type-functions.php:510
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:481
msgid "Permissions"
msgstr "Droits"

#: includes/acf-internal-post-type-functions.php:480
#: includes/acf-internal-post-type-functions.php:509
msgid "URLs"
msgstr "URL"

#: includes/acf-internal-post-type-functions.php:479
#: includes/acf-internal-post-type-functions.php:508
msgid "Visibility"
msgstr "Visibilité"

#: includes/acf-internal-post-type-functions.php:478
#: includes/acf-internal-post-type-functions.php:507
msgid "Labels"
msgstr "Libellés"

#: includes/admin/post-types/admin-field-group.php:243
msgid "Field Settings Tabs"
msgstr ""

#. Author URI of the plugin
msgid ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""

#: includes/api/api-template.php:867
msgid "[ACF shortcode value disabled for preview]"
msgstr "[Valeur du code court ACF désactivée pour l’aperçu]"

#: includes/admin/admin-internal-post-type.php:287
#: includes/admin/post-types/admin-field-group.php:545
msgid "Close Modal"
msgstr "Fermer la modale"

#: includes/admin/post-types/admin-field-group.php:96
#: assets/build/js/acf-field-group.js:1661
#: assets/build/js/acf-field-group.js:1980
msgid "Field moved to other group"
msgstr "Champ déplacé vers un autre groupe"

#: includes/admin/post-types/admin-field-group.php:95
#: assets/build/js/acf.js:1440 assets/build/js/acf.js:1521
msgid "Close modal"
msgstr "Fermer la modale"

#: includes/fields/class-acf-field-tab.php:125
msgid "Start a new group of tabs at this tab."
msgstr "Commencez un nouveau groupe d’onglets dans cet onglet."

#: includes/fields/class-acf-field-tab.php:124
msgid "New Tab Group"
msgstr "Nouveau groupe d’onglets"

#: includes/fields/class-acf-field-select.php:457
#: includes/fields/class-acf-field-true_false.php:200
msgid "Use a stylized checkbox using select2"
msgstr "Utiliser une case à cocher stylisée avec select2"

#: includes/fields/class-acf-field-radio.php:260
msgid "Save Other Choice"
msgstr "Enregistrer un autre choix"

#: includes/fields/class-acf-field-radio.php:249
msgid "Allow Other Choice"
msgstr "Autoriser un autre choix"

#: includes/fields/class-acf-field-checkbox.php:450
msgid "Add Toggle All"
msgstr "Ajouter « Tout ouvrir/fermer »"

#: includes/fields/class-acf-field-checkbox.php:409
msgid "Save Custom Values"
msgstr "Enregistrer les valeurs personnalisées"

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Allow Custom Values"
msgstr "Autoriser les valeurs personnalisées"

#: includes/fields/class-acf-field-checkbox.php:148
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"Les valeurs personnalisées des cases à cocher ne peuvent pas être vides. "
"Décochez toutes les valeurs vides."

#: includes/admin/views/global/navigation.php:140
msgid "Updates"
msgstr "Mises à jour"

#: includes/admin/views/global/navigation.php:83
msgid "Advanced Custom Fields logo"
msgstr "Logo Advanced Custom Fields"

#: includes/admin/views/global/form-top.php:57
msgid "Save Changes"
msgstr "Enregistrer les modifications"

#: includes/admin/views/global/form-top.php:44
msgid "Field Group Title"
msgstr "Titre du groupe de champs"

#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Ajouter un titre"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:20
#: includes/admin/views/acf-post-type/list-empty.php:12
#: includes/admin/views/acf-taxonomy/list-empty.php:12
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"Nouveau sur ACF ? Jetez un œil à notre <a href=\"%s\" target=\"_blank"
"\">guide des premiers pas</a>."

#: includes/admin/views/acf-field-group/list-empty.php:15
msgid "Add Field Group"
msgstr "Ajouter un groupe de champs"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:10
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF utilise <a href=\"%s\" target=\"_blank\">des groupes de champs</a> pour "
"grouper des champs personnalisés et les associer aux écrans de modification."

#: includes/admin/views/acf-field-group/list-empty.php:5
msgid "Add Your First Field Group"
msgstr "Ajouter votre premier groupe de champs"

#: includes/admin/views/acf-field-group/pro-features.php:16
msgid "Upgrade Now"
msgstr "Mettre à niveau maintenant"

#: includes/admin/views/acf-field-group/pro-features.php:11
msgid "Options Pages"
msgstr "Pages d’options"

#: includes/admin/views/acf-field-group/pro-features.php:10
msgid "ACF Blocks"
msgstr "Blocs ACF"

#: includes/admin/views/acf-field-group/pro-features.php:8
msgid "Gallery Field"
msgstr "Champ galerie"

#: includes/admin/views/acf-field-group/pro-features.php:7
msgid "Flexible Content Field"
msgstr "Champ de contenu flexible"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "Repeater Field"
msgstr "Champ répéteur"

#: includes/admin/views/acf-field-group/pro-features.php:4
#: includes/admin/views/global/navigation.php:125
msgid "Unlock Extra Features with ACF PRO"
msgstr "Débloquer des fonctionnalités supplémentaires avec ACF PRO"

#: includes/admin/views/acf-field-group/options.php:252
msgid "Delete Field Group"
msgstr "Supprimer le groupe de champ"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:246
msgid "Created on %1$s at %2$s"
msgstr "Créé le %1$s à %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Réglages du groupe"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Règles de localisation"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:61
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Choisissez parmi plus de 30 types de champs. <a href=\"%s\" target=\"_blank"
"\">En savoir plus</a>."

#: includes/admin/views/acf-field-group/fields.php:54
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Commencez à créer de nouveaux champs personnalisés pour vos articles, pages, "
"types de publication personnalisés et autres contenus WordPress."

#: includes/admin/views/acf-field-group/fields.php:53
msgid "Add Your First Field"
msgstr "Ajouter votre premier champ"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:32
msgid "#"
msgstr "N°"

#: includes/admin/views/acf-field-group/fields.php:22
#: includes/admin/views/acf-field-group/fields.php:56
#: includes/admin/views/acf-field-group/fields.php:92
#: includes/admin/views/global/form-top.php:53
msgid "Add Field"
msgstr "Ajouter un champ"

#: includes/acf-field-group-functions.php:496 includes/fields.php:410
msgid "Presentation"
msgstr "Présentation"

#: includes/fields.php:409
msgid "Validation"
msgstr "Validation"

#: includes/acf-internal-post-type-functions.php:477
#: includes/acf-internal-post-type-functions.php:506 includes/fields.php:408
msgid "General"
msgstr "Général"

#: includes/admin/tools/class-acf-admin-tool-import.php:70
msgid "Import JSON"
msgstr "Importer un JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:370
msgid "Export As JSON"
msgstr "Exporter en tant que JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:345
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "Groupe de champs désactivé."
msgstr[1] "%s groupes de champs désactivés."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:338
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "Groupe de champs activé."
msgstr[1] "%s groupes de champs activés."

#: includes/admin/admin-internal-post-type-list.php:430
#: includes/admin/admin-internal-post-type-list.php:461
msgid "Deactivate"
msgstr "Désactiver"

#: includes/admin/admin-internal-post-type-list.php:430
msgid "Deactivate this item"
msgstr "Désactiver cet élément"

#: includes/admin/admin-internal-post-type-list.php:426
#: includes/admin/admin-internal-post-type-list.php:460
msgid "Activate"
msgstr "Activer"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Activate this item"
msgstr "Activer cet élément"

#: includes/admin/post-types/admin-field-group.php:92
#: assets/build/js/acf-field-group.js:2741
#: assets/build/js/acf-field-group.js:3180
msgid "Move field group to trash?"
msgstr "Déplacer le groupe de champs vers la corbeille ?"

#: acf.php:485 includes/admin/admin-internal-post-type-list.php:242
#: includes/admin/post-types/admin-field-group.php:271
#: includes/admin/post-types/admin-post-type.php:292
#: includes/admin/post-types/admin-taxonomy.php:292
msgctxt "post status"
msgid "Inactive"
msgstr "Inactif"

#. Author of the plugin
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:543
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields et Advanced Custom Fields Pro ne doivent pas être "
"actives en même temps. Nous avons automatiquement désactivé Advanced Custom "
"Fields Pro."

#: acf.php:541
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields et Advanced Custom Fields Pro ne doivent pas être "
"actives en même temps. Nous avons automatiquement désactivé Advanced Custom "
"Fields."

#: includes/acf-value-functions.php:374
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
"\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> - Nous avons détecté un ou plusieurs appels pour "
"récupérer les valeurs des champs ACF avant l’initialisation d’ACF. Ceci "
"n’est pas pris en charge et peut entraîner des données incorrectes ou "
"manquantes. <a href=\"%2$s\" target=\"_blank\">Découvrez comment corriger ce "
"problème</a>."

#: includes/fields/class-acf-field-user.php:540
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s doit avoir un compte avec le rôle %2$s."
msgstr[1] "%1$s doit avoir un compte avec l’un des rôles suivants : %2$s"

#: includes/fields/class-acf-field-user.php:531
msgid "%1$s must have a valid user ID."
msgstr "%1$s doit avoir un ID de compte valide."

#: includes/fields/class-acf-field-user.php:369
msgid "Invalid request."
msgstr "Demande invalide."

#: includes/fields/class-acf-field-select.php:690
msgid "%1$s is not one of %2$s"
msgstr "%1$s n’est pas l’un des %2$s"

#: includes/fields/class-acf-field-post_object.php:698
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s doit contenir le terme %2$s."
msgstr[1] "%1$s doit contenir l’un des termes suivants : %2$s"

#: includes/fields/class-acf-field-post_object.php:682
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s doit être une publication de type %2$s."
msgstr[1] ""
"%1$s doit appartenir à l’un des types de publication suivants : %2$s"

#: includes/fields/class-acf-field-post_object.php:673
msgid "%1$s must have a valid post ID."
msgstr "%1$s doit avoir un ID de publication valide."

#: includes/fields/class-acf-field-file.php:475
msgid "%s requires a valid attachment ID."
msgstr "%s nécessite un ID de fichier jointe valide."

#: includes/admin/views/acf-field-group/options.php:218
msgid "Show in REST API"
msgstr "Afficher dans l’API REST"

#: includes/fields/class-acf-field-color_picker.php:170
msgid "Enable Transparency"
msgstr "Activer la transparence"

#: includes/fields/class-acf-field-color_picker.php:189
msgid "RGBA Array"
msgstr "Tableau RGBA"

#: includes/fields/class-acf-field-color_picker.php:99
msgid "RGBA String"
msgstr "Chaine RGBA"

#: includes/fields/class-acf-field-color_picker.php:98
#: includes/fields/class-acf-field-color_picker.php:188
msgid "Hex String"
msgstr "Chaine hexadécimale"

#: includes/admin/views/browse-fields-modal.php:65
msgid "Upgrade to PRO"
msgstr "Mettre à niveau vers PRO"

#: includes/admin/post-types/admin-field-group.php:271
#: includes/admin/post-types/admin-post-type.php:292
#: includes/admin/post-types/admin-taxonomy.php:292
msgctxt "post status"
msgid "Active"
msgstr "Actif"

#: includes/fields/class-acf-field-email.php:181
msgid "'%s' is not a valid email address"
msgstr "« %s » n’est pas une adresse e-mail valide"

#: includes/fields/class-acf-field-color_picker.php:77
msgid "Color value"
msgstr "Valeur de la couleur"

#: includes/fields/class-acf-field-color_picker.php:75
msgid "Select default color"
msgstr "Sélectionner une couleur par défaut"

#: includes/fields/class-acf-field-color_picker.php:73
msgid "Clear color"
msgstr "Effacer la couleur"

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr "Blocs"

#: includes/acf-wp-functions.php:83
msgid "Options"
msgstr "Options"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr "Comptes"

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr "Éléments de menu"

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr "Widgets"

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr "Fichiers joints"

#: includes/acf-wp-functions.php:54
#: includes/admin/post-types/admin-post-types.php:104
#: includes/admin/post-types/admin-taxonomies.php:79
#: includes/admin/tools/class-acf-admin-tool-import.php:93
#: includes/admin/views/acf-post-type/basic-settings.php:70
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Taxonomies"

#: includes/acf-wp-functions.php:41
#: includes/admin/post-types/admin-post-type.php:123
#: includes/admin/post-types/admin-post-types.php:106
#: includes/admin/views/acf-post-type/advanced-settings.php:102
#: assets/build/js/acf-internal-post-type.js:135
#: assets/build/js/acf-internal-post-type.js:195
msgid "Posts"
msgstr "Publications"

#: includes/ajax/class-acf-ajax-local-json-diff.php:76
msgid "Last updated: %s"
msgstr "Dernière mise à jour : %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:70
msgid "Sorry, this post is unavailable for diff comparison."
msgstr ""
"Désolé, cette publication n’est pas disponible pour la comparaison de "
"différence."

#: includes/ajax/class-acf-ajax-local-json-diff.php:42
msgid "Invalid field group parameter(s)."
msgstr "Paramètres de groupe de champ invalides."

#: includes/admin/admin-internal-post-type-list.php:396
msgid "Awaiting save"
msgstr "En attente d’enregistrement"

#: includes/admin/admin-internal-post-type-list.php:393
msgid "Saved"
msgstr "Enregistré"

#: includes/admin/admin-internal-post-type-list.php:389
#: includes/admin/tools/class-acf-admin-tool-import.php:49
msgid "Import"
msgstr "Importer"

#: includes/admin/admin-internal-post-type-list.php:385
msgid "Review changes"
msgstr "Évaluer les modifications"

#: includes/admin/admin-internal-post-type-list.php:361
msgid "Located in: %s"
msgstr "Situés dans : %s"

#: includes/admin/admin-internal-post-type-list.php:358
msgid "Located in plugin: %s"
msgstr "Situés dans l’extension : %s"

#: includes/admin/admin-internal-post-type-list.php:355
msgid "Located in theme: %s"
msgstr "Situés dans le thème : %s"

#: includes/admin/post-types/admin-field-groups.php:239
msgid "Various"
msgstr "Divers"

#: includes/admin/admin-internal-post-type-list.php:210
#: includes/admin/admin-internal-post-type-list.php:468
msgid "Sync changes"
msgstr "Synchroniser les modifications"

#: includes/admin/admin-internal-post-type-list.php:209
msgid "Loading diff"
msgstr "Chargement du différentiel"

#: includes/admin/admin-internal-post-type-list.php:208
msgid "Review local JSON changes"
msgstr "Vérifier les modifications JSON locales"

#: includes/admin/admin.php:169
msgid "Visit website"
msgstr "Visiter le site"

#: includes/admin/admin.php:168
msgid "View details"
msgstr "Voir les détails"

#: includes/admin/admin.php:167
msgid "Version %s"
msgstr "Version %s"

#: includes/admin/admin.php:166
msgid "Information"
msgstr "Informations"

#: includes/admin/admin.php:157
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Service d’assistance</a>. Les "
"professionnels du support de notre service d’assistance vous aideront à "
"résoudre vos problèmes techniques les plus complexes."

#: includes/admin/admin.php:153
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. Nous avons une communauté "
"active et amicale sur nos forums communautaires qui peut vous aider à "
"comprendre le fonctionnement de l’écosystème ACF."

#: includes/admin/admin.php:149
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Notre documentation "
"complète contient des références et des guides pour la plupart des "
"situations que vous pouvez rencontrer."

#: includes/admin/admin.php:146
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Nous sommes fanatiques du support et souhaitons que vous tiriez le meilleur "
"parti de votre site avec ACF. Si vous rencontrez des difficultés, il existe "
"plusieurs endroits où vous pouvez trouver de l’aide :"

#: includes/admin/admin.php:143 includes/admin/admin.php:145
msgid "Help & Support"
msgstr "Aide & support"

#: includes/admin/admin.php:134
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Veuillez utiliser l’onglet « Aide & support » pour nous contacter si vous "
"avez besoin d’assistance."

#: includes/admin/admin.php:131
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Avant de créer votre premier groupe de champ, nous vous recommandons de lire "
"notre guide <a href=\"%s\" target=\"_blank\">Pour commencer</a> afin de vous "
"familiariser avec la philosophie et les meilleures pratiques de l’extension."

#: includes/admin/admin.php:129
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"L’extension Advanced Custom Fields fournit un constructeur visuel de "
"formulaires pour modifier les écrans de WordPress avec des champs "
"supplémentaires, ainsi qu’une API intuitive pour afficher les valeurs des "
"champs personnalisés dans n’importe quel fichier de modèle de thème."

#: includes/admin/admin.php:126 includes/admin/admin.php:128
msgid "Overview"
msgstr "Aperçu"

#: includes/locations.php:36
msgid "Location type \"%s\" is already registered."
msgstr "Le type d’emplacement « %s » est déjà inscrit."

#: includes/locations.php:25
msgid "Class \"%s\" does not exist."
msgstr "La classe « %s » n’existe pas."

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Nonce invalide."

#: includes/fields/class-acf-field-user.php:364
msgid "Error loading field."
msgstr "Erreur lors du chargement du champ."

#: assets/build/js/acf-input.js:2750 assets/build/js/acf-input.js:2819
#: assets/build/js/acf-input.js:2926 assets/build/js/acf-input.js:3000
msgid "Location not found: %s"
msgstr "Emplacement introuvable : %s"

#: includes/forms/form-user.php:353
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Erreur</strong> : %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Rôle du compte"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Commenter"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Format de publication"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Élément de menu"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "État de la publication"

#: includes/acf-wp-functions.php:71
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menus"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Emplacements de menus"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Taxonomie de la publication"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Page enfant (a un parent)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Page parent (a des enfants)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Page de plus haut niveau (aucun parent)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Page des publications"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Page d’accueil"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Type de page"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Affichage de l’interface d’administration"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Vue de l’interface publique"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Connecté"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Compte actuel"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Modèle de page"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "S’inscrire"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Ajouter/Modifier"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Formulaire de profil"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Page parente"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super admin"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Rôle du compte actuel"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Modèle par défaut"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Modèle de publication"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Catégorie de publication"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Tous les formats %s"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Fichier joint"

#: includes/validation.php:364
msgid "%s value is required"
msgstr "La valeur %s est obligatoire"

#: includes/admin/views/acf-field-group/conditional-logic.php:59
msgid "Show this field if"
msgstr "Afficher ce champ si"

#: includes/admin/views/acf-field-group/conditional-logic.php:26
#: includes/admin/views/acf-field-group/field.php:105 includes/fields.php:411
msgid "Conditional Logic"
msgstr "Logique conditionnelle"

#: includes/admin/admin.php:234
#: includes/admin/views/acf-field-group/conditional-logic.php:156
#: includes/admin/views/acf-field-group/location-rule.php:91
msgid "and"
msgstr "et"

#: includes/admin/post-types/admin-field-groups.php:101
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:110
msgid "Local JSON"
msgstr "JSON Local"

#: includes/admin/views/acf-field-group/pro-features.php:9
msgid "Clone Field"
msgstr "Champ clone"

#: includes/admin/views/upgrade/notice.php:30
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Veuillez également vérifier que tous les modules premium (%s) soient mis à "
"jour à la dernière version."

#: includes/admin/views/upgrade/notice.php:28
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Cette version contient des améliorations de la base de données et nécessite "
"une mise à niveau."

#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Merci d‘avoir mis à jour %1$s v%2$s !"

#: includes/admin/views/upgrade/notice.php:27
msgid "Database Upgrade Required"
msgstr "Mise à niveau de la base de données requise"

#: includes/admin/post-types/admin-field-group.php:132
#: includes/admin/views/upgrade/notice.php:18
msgid "Options Page"
msgstr "Page d’options"

#: includes/admin/views/upgrade/notice.php:15 includes/fields.php:460
msgid "Gallery"
msgstr "Galerie"

#: includes/admin/views/upgrade/notice.php:12 includes/fields.php:450
msgid "Flexible Content"
msgstr "Contenu flexible"

#: includes/admin/views/upgrade/notice.php:9 includes/fields.php:470
msgid "Repeater"
msgstr "Répéteur"

#: includes/admin/views/tools/tools.php:24
msgid "Back to all tools"
msgstr "Retour aux outils"

#: includes/admin/views/acf-field-group/options.php:180
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Si plusieurs groupes de champs apparaissent sur un écran de modification, "
"les options du premier groupe de champs seront utilisées (celle avec le "
"numéro de commande le plus bas)."

#: includes/admin/views/acf-field-group/options.php:180
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Sélectionner</b> les éléments à <b>masquer</b> sur l’écran de "
"modification."

#: includes/admin/views/acf-field-group/options.php:179
msgid "Hide on screen"
msgstr "Masquer de l’écran"

#: includes/admin/views/acf-field-group/options.php:171
msgid "Send Trackbacks"
msgstr "Envoyer des rétroliens"

#: includes/admin/post-types/admin-taxonomy.php:123
#: includes/admin/views/acf-field-group/options.php:170
#: includes/admin/views/acf-taxonomy/advanced-settings.php:155
#: assets/build/js/acf-internal-post-type.js:142
#: assets/build/js/acf-internal-post-type.js:202
msgid "Tags"
msgstr "Étiquettes"

#: includes/admin/post-types/admin-taxonomy.php:125
#: includes/admin/views/acf-field-group/options.php:169
#: assets/build/js/acf-internal-post-type.js:145
#: assets/build/js/acf-internal-post-type.js:205
msgid "Categories"
msgstr "Catégories"

#: includes/admin/views/acf-field-group/options.php:167
#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Page Attributes"
msgstr "Attributs de page"

#: includes/admin/views/acf-field-group/options.php:166
msgid "Format"
msgstr "Format"

#: includes/admin/views/acf-field-group/options.php:165
#: includes/admin/views/acf-post-type/advanced-settings.php:18
msgid "Author"
msgstr "Auteur/autrice"

#: includes/admin/views/acf-field-group/options.php:164
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/acf-field-group/options.php:163
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Revisions"
msgstr "Révisions"

#: includes/acf-wp-functions.php:63
#: includes/admin/views/acf-field-group/options.php:162
#: includes/admin/views/acf-post-type/advanced-settings.php:19
msgid "Comments"
msgstr "Commentaires"

#: includes/admin/views/acf-field-group/options.php:161
msgid "Discussion"
msgstr "Commentaires"

#: includes/admin/views/acf-field-group/options.php:159
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Excerpt"
msgstr "Extrait"

#: includes/admin/views/acf-field-group/options.php:158
msgid "Content Editor"
msgstr "Éditeur de contenu"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Permalink"
msgstr "Permalien"

#: includes/admin/views/acf-field-group/options.php:235
msgid "Shown in field group list"
msgstr "Affiché dans la liste des groupes de champs"

#: includes/admin/views/acf-field-group/options.php:142
msgid "Field groups with a lower order will appear first"
msgstr "Les groupes de champs avec un ordre inférieur apparaitront en premier"

#: includes/admin/views/acf-field-group/options.php:141
msgid "Order No."
msgstr "N° de commande."

#: includes/admin/views/acf-field-group/options.php:132
msgid "Below fields"
msgstr "Sous les champs"

#: includes/admin/views/acf-field-group/options.php:131
msgid "Below labels"
msgstr "Sous les libellés"

#: includes/admin/views/acf-field-group/options.php:124
msgid "Instruction placement"
msgstr "Emplacement des instructions"

#: includes/admin/views/acf-field-group/options.php:107
msgid "Label placement"
msgstr "Emplacement des libellés"

#: includes/admin/views/acf-field-group/options.php:97
msgid "Side"
msgstr "Sur le côté"

#: includes/admin/views/acf-field-group/options.php:96
msgid "Normal (after content)"
msgstr "Normal (après le contenu)"

#: includes/admin/views/acf-field-group/options.php:95
msgid "High (after title)"
msgstr "Haute (après le titre)"

#: includes/admin/views/acf-field-group/options.php:88
msgid "Position"
msgstr "Emplacement"

#: includes/admin/views/acf-field-group/options.php:79
msgid "Seamless (no metabox)"
msgstr "Sans contour (pas de boîte meta)"

#: includes/admin/views/acf-field-group/options.php:78
msgid "Standard (WP metabox)"
msgstr "Standard (boîte méta WP)"

#: includes/admin/views/acf-field-group/options.php:71
msgid "Style"
msgstr "Style"

#: includes/admin/views/acf-field-group/fields.php:44
msgid "Type"
msgstr "Type"

#: includes/admin/post-types/admin-field-groups.php:95
#: includes/admin/post-types/admin-post-types.php:103
#: includes/admin/post-types/admin-taxonomies.php:103
#: includes/admin/views/acf-field-group/fields.php:43
msgid "Key"
msgstr "Clé"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:37
msgid "Order"
msgstr "Ordre"

#: includes/admin/views/acf-field-group/field.php:318
msgid "Close Field"
msgstr "Fermer le champ"

#: includes/admin/views/acf-field-group/field.php:249
msgid "id"
msgstr "ID"

#: includes/admin/views/acf-field-group/field.php:233
msgid "class"
msgstr "classe"

#: includes/admin/views/acf-field-group/field.php:275
msgid "width"
msgstr "largeur"

#: includes/admin/views/acf-field-group/field.php:269
msgid "Wrapper Attributes"
msgstr "Attributs du conteneur"

#: includes/admin/views/acf-field-group/field.php:192
msgid "Required"
msgstr "Obligatoire"

#: includes/admin/views/acf-field-group/field.php:217
msgid "Instructions for authors. Shown when submitting data"
msgstr ""
"Instructions pour les auteurs et autrices. Affichées lors de l’envoi des "
"données"

#: includes/admin/views/acf-field-group/field.php:216
msgid "Instructions"
msgstr "Instructions"

#: includes/admin/views/acf-field-group/field.php:125
msgid "Field Type"
msgstr "Type de champ"

#: includes/admin/views/acf-field-group/field.php:166
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr ""
"Un seul mot. Aucun espace. Les tirets bas et les tirets sont autorisés."

#: includes/admin/views/acf-field-group/field.php:165
msgid "Field Name"
msgstr "Nom du champ"

#: includes/admin/views/acf-field-group/field.php:153
msgid "This is the name which will appear on the EDIT page"
msgstr "Ceci est le nom qui apparaîtra sur la page de modification"

#: includes/admin/views/acf-field-group/field.php:152
#: includes/admin/views/browse-fields-modal.php:59
msgid "Field Label"
msgstr "Libellé du champ"

#: includes/admin/views/acf-field-group/field.php:77
msgid "Delete"
msgstr "Supprimer"

#: includes/admin/views/acf-field-group/field.php:77
msgid "Delete field"
msgstr "Supprimer le champ"

#: includes/admin/views/acf-field-group/field.php:75
msgid "Move"
msgstr "Déplacer"

#: includes/admin/views/acf-field-group/field.php:75
msgid "Move field to another group"
msgstr "Deplacer le champ vers un autre groupe"

#: includes/admin/views/acf-field-group/field.php:73
msgid "Duplicate field"
msgstr "Dupliquer le champ"

#: includes/admin/views/acf-field-group/field.php:69
#: includes/admin/views/acf-field-group/field.php:72
msgid "Edit field"
msgstr "Modifier le champ"

#: includes/admin/views/acf-field-group/field.php:65
msgid "Drag to reorder"
msgstr "Faites glisser pour réorganiser"

#: includes/admin/post-types/admin-field-group.php:103
#: includes/admin/views/acf-field-group/location-group.php:3
#: assets/build/js/acf-field-group.js:2323
#: assets/build/js/acf-field-group.js:2725
msgid "Show this field group if"
msgstr "Afficher ce groupe de champs si"

#: includes/admin/views/upgrade/upgrade.php:94
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Aucune mise à jour disponible."

#: includes/admin/views/upgrade/upgrade.php:33
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Mise à niveau de base de données complète. <a href=\"%s\">Voir les "
"nouveautés</a>"

#: includes/admin/views/upgrade/upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Lecture des tâches de mise à niveau…"

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:65
msgid "Upgrade failed."
msgstr "Mise à niveau échouée."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Mise à niveau terminée."

#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:31
msgid "Upgrading data to version %s"
msgstr "Mise à niveau des données vers la version %s"

#: includes/admin/views/upgrade/network.php:121
#: includes/admin/views/upgrade/notice.php:44
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Il est recommandé de sauvegarder votre base de données avant de procéder. "
"Confirmez-vous le lancement de la mise à jour maintenant ?"

#: includes/admin/views/upgrade/network.php:117
msgid "Please select at least one site to upgrade."
msgstr "Veuillez choisir au moins un site à mettre à niveau."

#: includes/admin/views/upgrade/network.php:97
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Mise à niveau de la base de données effectuée. <a href=\"%s\">Retourner au "
"tableau de bord du réseau</a>"

#: includes/admin/views/upgrade/network.php:80
msgid "Site is up to date"
msgstr "Le site est à jour"

#: includes/admin/views/upgrade/network.php:78
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr ""
"Le site nécessite une mise à niveau de la base de données à partir de %1$s "
"vers %2$s"

#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Site"
msgstr "Site"

#: includes/admin/views/upgrade/network.php:26
#: includes/admin/views/upgrade/network.php:27
#: includes/admin/views/upgrade/network.php:96
msgid "Upgrade Sites"
msgstr "Mettre à niveau les sites"

#: includes/admin/views/upgrade/network.php:26
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Les sites suivants nécessitent une mise à niveau de la base de données. "
"Sélectionner ceux que vous voulez mettre à jour et cliquer sur %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:171
#: includes/admin/views/acf-field-group/locations.php:38
msgid "Add rule group"
msgstr "Ajouter un groupe de règles"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Créer un ensemble de règles pour déterminer quels écrans de modification "
"utiliseront ces champs personnalisés avancés"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Règles"

#: includes/admin/tools/class-acf-admin-tool-export.php:482
msgid "Copied"
msgstr "Copié"

#: includes/admin/tools/class-acf-admin-tool-export.php:458
msgid "Copy to clipboard"
msgstr "Copier dans le presse-papier"

#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Sélectionnez les éléments que vous souhaitez exporter, puis choisissez la "
"méthode d’exportation. « Exporter comme JSON » pour exporter vers un "
"fichier .json que vous pouvez ensuite importer dans une autre installation "
"ACF. « Générer le PHP » pour exporter un code PHP que vous pouvez placer "
"dans votre thème."

#: includes/admin/tools/class-acf-admin-tool-export.php:233
msgid "Select Field Groups"
msgstr "Sélectionner les groupes de champs"

#: includes/admin/tools/class-acf-admin-tool-export.php:96
#: includes/admin/tools/class-acf-admin-tool-export.php:131
msgid "No field groups selected"
msgstr "Aucun groupe de champs sélectionné"

#: includes/admin/tools/class-acf-admin-tool-export.php:39
#: includes/admin/tools/class-acf-admin-tool-export.php:371
#: includes/admin/tools/class-acf-admin-tool-export.php:399
msgid "Generate PHP"
msgstr "Générer le PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:35
msgid "Export Field Groups"
msgstr "Exporter les groupes de champs"

#: includes/admin/tools/class-acf-admin-tool-import.php:177
msgid "Import file empty"
msgstr "Fichier d’importation vide"

#: includes/admin/tools/class-acf-admin-tool-import.php:168
msgid "Incorrect file type"
msgstr "Type de fichier incorrect"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Error uploading file. Please try again"
msgstr "Erreur de téléversement du fichier. Veuillez réessayer."

#: includes/admin/tools/class-acf-admin-tool-import.php:50
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Sélectionnez le fichier JSON Advanced Custom Fields que vous souhaitez "
"importer. Quand vous cliquez sur le bouton « Importer » ci-dessous, ACF "
"importera les éléments dans ce fichier."

#: includes/admin/tools/class-acf-admin-tool-import.php:27
msgid "Import Field Groups"
msgstr "Importer les groupes de champs"

#: includes/admin/admin-internal-post-type-list.php:384
msgid "Sync"
msgstr "Synchroniser"

#: includes/admin/admin-internal-post-type-list.php:841
msgid "Select %s"
msgstr "Sélectionner %s"

#: includes/admin/admin-internal-post-type-list.php:419
#: includes/admin/admin-internal-post-type-list.php:457
#: includes/admin/views/acf-field-group/field.php:73
msgid "Duplicate"
msgstr "Dupliquer"

#: includes/admin/admin-internal-post-type-list.php:419
msgid "Duplicate this item"
msgstr "Dupliquer cet élément"

#: includes/admin/views/acf-post-type/advanced-settings.php:37
msgid "Supports"
msgstr "Prend en charge"

#: includes/admin/views/browse-fields-modal.php:92
msgid "Documentation"
msgstr "Documentation"

#: includes/admin/post-types/admin-field-groups.php:94
#: includes/admin/post-types/admin-post-types.php:102
#: includes/admin/post-types/admin-taxonomies.php:102
#: includes/admin/views/acf-field-group/options.php:234
#: includes/admin/views/acf-post-type/advanced-settings.php:58
#: includes/admin/views/acf-taxonomy/advanced-settings.php:110
#: includes/admin/views/upgrade/network.php:38
#: includes/admin/views/upgrade/network.php:49
msgid "Description"
msgstr "Description"

#: includes/admin/admin-internal-post-type-list.php:381
#: includes/admin/admin-internal-post-type-list.php:730
msgid "Sync available"
msgstr "Synchronisation disponible"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:359
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Groupe de champs synchronisé."
msgstr[1] "%s groupes de champs synchronisés."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:352
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Groupe de champs dupliqué."
msgstr[1] "%s groupes de champs dupliqués."

#: includes/admin/admin-internal-post-type-list.php:131
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Actif <span class=\"count\">(%s)</span>"
msgstr[1] "Actifs <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:254
msgid "Review sites & upgrade"
msgstr "Examiner les sites et mettre à niveau"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:93
#: includes/admin/admin-upgrade.php:94 includes/admin/admin-upgrade.php:230
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/upgrade.php:26
msgid "Upgrade Database"
msgstr "Mettre à niveau la base de données"

#: includes/admin/views/acf-field-group/options.php:160
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Custom Fields"
msgstr "Champs personnalisés"

#: includes/admin/post-types/admin-field-group.php:590
msgid "Move Field"
msgstr "Déplacer le champ"

#: includes/admin/post-types/admin-field-group.php:579
#: includes/admin/post-types/admin-field-group.php:583
msgid "Please select the destination for this field"
msgstr "Veuillez sélectionner la destination pour ce champ"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:541
msgid "The %1$s field can now be found in the %2$s field group"
msgstr ""
"Le champ %1$s peut maintenant être trouvé dans le groupe de champs %2$s"

#: includes/admin/post-types/admin-field-group.php:538
msgid "Move Complete."
msgstr "Déplacement effectué."

#: includes/admin/views/acf-field-group/field.php:35
#: includes/admin/views/acf-field-group/options.php:202
#: includes/admin/views/acf-post-type/advanced-settings.php:74
#: includes/admin/views/acf-taxonomy/advanced-settings.php:126
msgid "Active"
msgstr "Actif"

#: includes/admin/post-types/admin-field-group.php:240
msgid "Field Keys"
msgstr "Clés des champs"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/admin/tools/class-acf-admin-tool-export.php:320
msgid "Settings"
msgstr "Réglages"

#: includes/admin/post-types/admin-field-groups.php:96
msgid "Location"
msgstr "Emplacement"

#: includes/admin/post-types/admin-field-group.php:104
#: assets/build/js/acf-input.js:983 assets/build/js/acf-input.js:1075
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:101
#: includes/class-acf-internal-post-type.php:729
#: includes/post-types/class-acf-field-group.php:345
#: assets/build/js/acf-field-group.js:1501
#: assets/build/js/acf-field-group.js:1808
msgid "copy"
msgstr "copier"

#: includes/admin/post-types/admin-field-group.php:100
#: assets/build/js/acf-field-group.js:623
#: assets/build/js/acf-field-group.js:778
msgid "(this field)"
msgstr "(ce champ)"

#: includes/admin/post-types/admin-field-group.php:98
#: assets/build/js/acf-input.js:918 assets/build/js/acf-input.js:943
#: assets/build/js/acf-input.js:1002 assets/build/js/acf-input.js:1030
msgid "Checked"
msgstr "Coché"

#: includes/admin/post-types/admin-field-group.php:94
#: assets/build/js/acf-field-group.js:1606
#: assets/build/js/acf-field-group.js:1920
msgid "Move Custom Field"
msgstr "Déplacer le champ personnalisé"

#: includes/admin/post-types/admin-field-group.php:93
#: assets/build/js/acf-field-group.js:649
#: assets/build/js/acf-field-group.js:804
msgid "No toggle fields available"
msgstr "Aucun champ de sélection disponible"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Field group title is required"
msgstr "Le titre du groupe de champ est requis"

#: includes/admin/post-types/admin-field-group.php:90
#: assets/build/js/acf-field-group.js:1595
#: assets/build/js/acf-field-group.js:1906
msgid "This field cannot be moved until its changes have been saved"
msgstr ""
"Ce champ ne peut pas être déplacé tant que ses modifications n’ont pas été "
"enregistrées"

#: includes/admin/post-types/admin-field-group.php:89
#: assets/build/js/acf-field-group.js:1405
#: assets/build/js/acf-field-group.js:1703
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr ""
"La chaine « field_ » ne peut pas être utilisée au début du nom d’un champ"

#: includes/admin/post-types/admin-field-group.php:71
msgid "Field group draft updated."
msgstr "Brouillon du groupe de champs mis à jour."

#: includes/admin/post-types/admin-field-group.php:70
msgid "Field group scheduled for."
msgstr "Groupe de champs programmé."

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group submitted."
msgstr "Groupe de champs envoyé."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group saved."
msgstr "Groupe de champs sauvegardé."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group published."
msgstr "Groupe de champs publié."

#: includes/admin/post-types/admin-field-group.php:64
msgid "Field group deleted."
msgstr "Groupe de champs supprimé."

#: includes/admin/post-types/admin-field-group.php:62
#: includes/admin/post-types/admin-field-group.php:63
#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group updated."
msgstr "Groupe de champs mis à jour."

#: includes/admin/admin-tools.php:118
#: includes/admin/views/global/navigation.php:138
#: includes/admin/views/tools/tools.php:21
msgid "Tools"
msgstr "Outils"

#: includes/locations/abstract-acf-location.php:106
msgid "is not equal to"
msgstr "n’est pas égal à"

#: includes/locations/abstract-acf-location.php:105
msgid "is equal to"
msgstr "est égal à"

#: includes/locations.php:102
msgid "Forms"
msgstr "Formulaires"

#: includes/admin/post-types/admin-post-type.php:124 includes/locations.php:100
#: includes/locations/class-acf-location-page.php:22
#: assets/build/js/acf-internal-post-type.js:137
#: assets/build/js/acf-internal-post-type.js:197
msgid "Page"
msgstr "Page"

#: includes/admin/post-types/admin-post-type.php:122 includes/locations.php:99
#: includes/locations/class-acf-location-post.php:22
#: assets/build/js/acf-internal-post-type.js:134
#: assets/build/js/acf-internal-post-type.js:194
msgid "Post"
msgstr "Publication"

#: includes/fields.php:354
msgid "Relational"
msgstr "Relationnel"

#: includes/fields.php:353
msgid "Choice"
msgstr "Choix"

#: includes/fields.php:351
msgid "Basic"
msgstr "Basique"

#: includes/fields.php:320
msgid "Unknown"
msgstr "Inconnu"

#: includes/fields.php:320
msgid "Field type does not exist"
msgstr "Le type de champ n’existe pas"

#: includes/forms/form-front.php:236
msgid "Spam Detected"
msgstr "Indésirable détecté"

#: includes/forms/form-front.php:107
msgid "Post updated"
msgstr "Publication mise à jour"

#: includes/forms/form-front.php:106
msgid "Update"
msgstr "Mise à jour"

#: includes/forms/form-front.php:57
msgid "Validate Email"
msgstr "Valider l’e-mail"

#: includes/fields.php:352 includes/forms/form-front.php:49
msgid "Content"
msgstr "Contenu"

#: includes/admin/views/acf-post-type/advanced-settings.php:17
#: includes/forms/form-front.php:40
msgid "Title"
msgstr "Titre"

#: includes/assets.php:372 includes/forms/form-comment.php:160
#: assets/build/js/acf-input.js:7348 assets/build/js/acf-input.js:7934
msgid "Edit field group"
msgstr "Modifier le groupe de champs"

#: includes/admin/post-types/admin-field-group.php:117
#: assets/build/js/acf-input.js:1125 assets/build/js/acf-input.js:1230
msgid "Selection is less than"
msgstr "La sélection est inférieure à"

#: includes/admin/post-types/admin-field-group.php:116
#: assets/build/js/acf-input.js:1106 assets/build/js/acf-input.js:1202
msgid "Selection is greater than"
msgstr "La sélection est supérieure à"

#: includes/admin/post-types/admin-field-group.php:115
#: assets/build/js/acf-input.js:1075 assets/build/js/acf-input.js:1170
msgid "Value is less than"
msgstr "La valeur est plus petite que"

#: includes/admin/post-types/admin-field-group.php:114
#: assets/build/js/acf-input.js:1045 assets/build/js/acf-input.js:1139
msgid "Value is greater than"
msgstr "La valeur est plus grande que"

#: includes/admin/post-types/admin-field-group.php:113
#: assets/build/js/acf-input.js:888 assets/build/js/acf-input.js:960
msgid "Value contains"
msgstr "La valeur contient"

#: includes/admin/post-types/admin-field-group.php:112
#: assets/build/js/acf-input.js:862 assets/build/js/acf-input.js:926
msgid "Value matches pattern"
msgstr "La valeur correspond au modèle"

#: includes/admin/post-types/admin-field-group.php:111
#: assets/build/js/acf-input.js:840 assets/build/js/acf-input.js:1023
#: assets/build/js/acf-input.js:903 assets/build/js/acf-input.js:1116
msgid "Value is not equal to"
msgstr "La valeur n’est pas égale à"

#: includes/admin/post-types/admin-field-group.php:110
#: assets/build/js/acf-input.js:810 assets/build/js/acf-input.js:964
#: assets/build/js/acf-input.js:864 assets/build/js/acf-input.js:1053
msgid "Value is equal to"
msgstr "La valeur est égale à"

#: includes/admin/post-types/admin-field-group.php:109
#: assets/build/js/acf-input.js:788 assets/build/js/acf-input.js:841
msgid "Has no value"
msgstr "A aucune valeur"

#: includes/admin/post-types/admin-field-group.php:108
#: assets/build/js/acf-input.js:758 assets/build/js/acf-input.js:783
msgid "Has any value"
msgstr "A n’importe quelle valeur"

#: includes/admin/admin-internal-post-type.php:327
#: includes/admin/views/browse-fields-modal.php:62 includes/assets.php:353
#: assets/build/js/acf.js:1567 assets/build/js/acf.js:1662
msgid "Cancel"
msgstr "Annuler"

#: includes/assets.php:349 assets/build/js/acf.js:1741
#: assets/build/js/acf.js:1859
msgid "Are you sure?"
msgstr "Confirmez-vous ?"

#: includes/assets.php:369 assets/build/js/acf-input.js:9409
#: assets/build/js/acf-input.js:10260
msgid "%d fields require attention"
msgstr "%d champs nécessitent votre attention"

#: includes/assets.php:368 assets/build/js/acf-input.js:9407
#: assets/build/js/acf-input.js:10256
msgid "1 field requires attention"
msgstr "Un champ nécessite votre attention"

#: includes/assets.php:367 includes/validation.php:286
#: includes/validation.php:296 assets/build/js/acf-input.js:9402
#: assets/build/js/acf-input.js:10251
msgid "Validation failed"
msgstr "Échec de la validation"

#: includes/assets.php:366 assets/build/js/acf-input.js:9565
#: assets/build/js/acf-input.js:10434
msgid "Validation successful"
msgstr "Validation réussie"

#: includes/media.php:54 assets/build/js/acf-input.js:7176
#: assets/build/js/acf-input.js:7738
msgid "Restricted"
msgstr "Limité"

#: includes/media.php:53 assets/build/js/acf-input.js:6991
#: assets/build/js/acf-input.js:7502
msgid "Collapse Details"
msgstr "Replier les détails"

#: includes/media.php:52 assets/build/js/acf-input.js:6991
#: assets/build/js/acf-input.js:7499
msgid "Expand Details"
msgstr "Déplier les détails"

#: includes/admin/views/acf-post-type/advanced-settings.php:461
#: includes/media.php:51 assets/build/js/acf-input.js:6858
#: assets/build/js/acf-input.js:7347
msgid "Uploaded to this post"
msgstr "Téléversé sur cette publication"

#: includes/media.php:50 assets/build/js/acf-input.js:6897
#: assets/build/js/acf-input.js:7386
msgctxt "verb"
msgid "Update"
msgstr "Mettre à jour"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Modifier"

#: includes/assets.php:363 assets/build/js/acf-input.js:9179
#: assets/build/js/acf-input.js:10022
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Les modifications que vous avez effectuées seront perdues si vous quittez "
"cette page"

#: includes/api/api-helpers.php:3482
msgid "File type must be %s."
msgstr "Le type de fichier doit être %s."

#: includes/admin/post-types/admin-field-group.php:102
#: includes/admin/views/acf-field-group/conditional-logic.php:59
#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:36
#: includes/api/api-helpers.php:3478 assets/build/js/acf-field-group.js:771
#: assets/build/js/acf-field-group.js:2361
#: assets/build/js/acf-field-group.js:933
#: assets/build/js/acf-field-group.js:2769
msgid "or"
msgstr "ou"

#: includes/api/api-helpers.php:3451
msgid "File size must not exceed %s."
msgstr "La taille du fichier ne doit pas excéder %s."

#: includes/api/api-helpers.php:3446
msgid "File size must be at least %s."
msgstr "La taille du fichier doit être d’au moins %s."

#: includes/api/api-helpers.php:3431
msgid "Image height must not exceed %dpx."
msgstr "La hauteur de l’image ne doit pas excéder %d px."

#: includes/api/api-helpers.php:3426
msgid "Image height must be at least %dpx."
msgstr "La hauteur de l’image doit être au minimum de %d px."

#: includes/api/api-helpers.php:3412
msgid "Image width must not exceed %dpx."
msgstr "La largeur de l’image ne doit pas excéder %d px."

#: includes/api/api-helpers.php:3407
msgid "Image width must be at least %dpx."
msgstr "La largeur de l’image doit être au minimum de %d px."

#: includes/api/api-helpers.php:1653 includes/api/api-term.php:147
msgid "(no title)"
msgstr "(aucun titre)"

#: includes/api/api-helpers.php:944
msgid "Full Size"
msgstr "Taille originale"

#: includes/api/api-helpers.php:903
msgid "Large"
msgstr "Grand"

#: includes/api/api-helpers.php:902
msgid "Medium"
msgstr "Moyen"

#: includes/api/api-helpers.php:901
msgid "Thumbnail"
msgstr "Miniature"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:99
#: assets/build/js/acf-field-group.js:1077
#: assets/build/js/acf-field-group.js:1260
msgid "(no label)"
msgstr "(aucun libellé)"

#: includes/fields/class-acf-field-textarea.php:145
msgid "Sets the textarea height"
msgstr "Définir la hauteur de la zone de texte"

#: includes/fields/class-acf-field-textarea.php:144
msgid "Rows"
msgstr "Lignes"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Zone de texte"

#: includes/fields/class-acf-field-checkbox.php:451
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Ajouter une case à cocher pour basculer tous les choix"

#: includes/fields/class-acf-field-checkbox.php:413
msgid "Save 'custom' values to the field's choices"
msgstr "Enregistrez les valeurs « personnalisées » dans les choix du champ"

#: includes/fields/class-acf-field-checkbox.php:402
msgid "Allow 'custom' values to be added"
msgstr "Autoriser l’ajout de valeurs personnalisées"

#: includes/fields/class-acf-field-checkbox.php:38
msgid "Add new choice"
msgstr "Ajouter un nouveau choix"

#: includes/fields/class-acf-field-checkbox.php:174
msgid "Toggle All"
msgstr "Tout (dé)sélectionner"

#: includes/fields/class-acf-field-page_link.php:506
msgid "Allow Archives URLs"
msgstr "Afficher les URL des archives"

#: includes/fields/class-acf-field-page_link.php:179
msgid "Archives"
msgstr "Archives"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Lien vers la page"

#: includes/fields/class-acf-field-taxonomy.php:948
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Ajouter"

#: includes/admin/views/acf-field-group/fields.php:42
#: includes/fields/class-acf-field-taxonomy.php:913
msgid "Name"
msgstr "Nom"

#: includes/fields/class-acf-field-taxonomy.php:897
msgid "%s added"
msgstr "%s ajouté"

#: includes/fields/class-acf-field-taxonomy.php:861
msgid "%s already exists"
msgstr "%s existe déjà"

#: includes/fields/class-acf-field-taxonomy.php:849
msgid "User unable to add new %s"
msgstr "Compte incapable d’ajouter un nouveau %s"

#: includes/fields/class-acf-field-taxonomy.php:759
msgid "Term ID"
msgstr "ID de terme"

#: includes/fields/class-acf-field-taxonomy.php:758
msgid "Term Object"
msgstr "Objet de terme"

#: includes/fields/class-acf-field-taxonomy.php:743
msgid "Load value from posts terms"
msgstr "Charger une valeur depuis les termes de publications"

#: includes/fields/class-acf-field-taxonomy.php:742
msgid "Load Terms"
msgstr "Charger les termes"

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "Connect selected terms to the post"
msgstr "Lier les termes sélectionnés à la publication"

#: includes/fields/class-acf-field-taxonomy.php:731
msgid "Save Terms"
msgstr "Enregistrer les termes"

#: includes/fields/class-acf-field-taxonomy.php:721
msgid "Allow new terms to be created whilst editing"
msgstr "Autoriser la création de nouveaux termes pendant la modification"

#: includes/fields/class-acf-field-taxonomy.php:720
msgid "Create Terms"
msgstr "Créer des termes"

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Radio Buttons"
msgstr "Boutons radio"

#: includes/fields/class-acf-field-taxonomy.php:778
msgid "Single Value"
msgstr "Valeur unique"

#: includes/fields/class-acf-field-taxonomy.php:776
msgid "Multi Select"
msgstr "Liste déroulante à multiple choix"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Checkbox"
msgstr "Case à cocher"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Multiple Values"
msgstr "Valeurs multiples"

#: includes/fields/class-acf-field-taxonomy.php:769
msgid "Select the appearance of this field"
msgstr "Sélectionner l’apparence de ce champ"

#: includes/fields/class-acf-field-taxonomy.php:768
msgid "Appearance"
msgstr "Apparence"

#: includes/fields/class-acf-field-taxonomy.php:710
msgid "Select the taxonomy to be displayed"
msgstr "Sélectionner la taxonomie à afficher"

#: includes/fields/class-acf-field-taxonomy.php:671
msgctxt "No Terms"
msgid "No %s"
msgstr "N° %s"

#: includes/fields/class-acf-field-number.php:266
msgid "Value must be equal to or lower than %d"
msgstr "La valeur doit être inférieure ou égale à %d"

#: includes/fields/class-acf-field-number.php:259
msgid "Value must be equal to or higher than %d"
msgstr "La valeur doit être être supérieure ou égale à %d"

#: includes/fields/class-acf-field-number.php:244
msgid "Value must be a number"
msgstr "La valeur doit être un nombre"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Nombre"

#: includes/fields/class-acf-field-radio.php:264
msgid "Save 'other' values to the field's choices"
msgstr "Sauvegarder les valeurs « Autres » dans les choix des champs"

#: includes/fields/class-acf-field-radio.php:253
msgid "Add 'other' choice to allow for custom values"
msgstr "Ajouter le choix « Autre » pour autoriser les valeurs personnalisées"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Bouton radio"

#: includes/fields/class-acf-field-accordion.php:107
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Définir un point de terminaison pour l’accordéon précédent. Cet accordéon ne "
"sera pas visible."

#: includes/fields/class-acf-field-accordion.php:96
msgid "Allow this accordion to open without closing others."
msgstr "Autoriser l’ouverture de cet accordéon sans fermer les autres."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Multi-expand"
msgstr "Ouverture multiple"

#: includes/fields/class-acf-field-accordion.php:85
msgid "Display this accordion as open on page load."
msgstr "Ouvrir l’accordéon au chargement de la page."

#: includes/fields/class-acf-field-accordion.php:84
msgid "Open"
msgstr "Ouvrir"

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "Accordéon"

#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-file.php:279
msgid "Restrict which files can be uploaded"
msgstr "Restreindre quels fichiers peuvent être téléversés"

#: includes/fields/class-acf-field-file.php:220
msgid "File ID"
msgstr "ID du fichier"

#: includes/fields/class-acf-field-file.php:219
msgid "File URL"
msgstr "URL du fichier"

#: includes/fields/class-acf-field-file.php:218
msgid "File Array"
msgstr "Tableau de fichiers"

#: includes/fields/class-acf-field-file.php:186
msgid "Add File"
msgstr "Ajouter un fichier"

#: includes/admin/tools/class-acf-admin-tool-import.php:156
#: includes/fields/class-acf-field-file.php:186
msgid "No file selected"
msgstr "Aucun fichier sélectionné"

#: includes/fields/class-acf-field-file.php:150
msgid "File name"
msgstr "Nom du fichier"

#: includes/fields/class-acf-field-file.php:63
#: assets/build/js/acf-input.js:2474 assets/build/js/acf-input.js:2625
msgid "Update File"
msgstr "Mettre à jour le fichier"

#: includes/fields/class-acf-field-file.php:62
#: assets/build/js/acf-input.js:2473 assets/build/js/acf-input.js:2624
msgid "Edit File"
msgstr "Modifier le fichier"

#: includes/admin/tools/class-acf-admin-tool-import.php:58
#: includes/fields/class-acf-field-file.php:61
#: assets/build/js/acf-input.js:2447 assets/build/js/acf-input.js:2597
msgid "Select File"
msgstr "Sélectionner un fichier"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Fichier"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Mot de Passe"

#: includes/fields/class-acf-field-select.php:398
msgid "Specify the value returned"
msgstr "Spécifier la valeur retournée"

#: includes/fields/class-acf-field-select.php:467
msgid "Use AJAX to lazy load choices?"
msgstr "Utiliser AJAX pour un chargement différé des choix ?"

#: includes/fields/class-acf-field-checkbox.php:362
#: includes/fields/class-acf-field-select.php:387
msgid "Enter each default value on a new line"
msgstr "Saisir chaque valeur par défaut sur une nouvelle ligne"

#: includes/fields/class-acf-field-select.php:258 includes/media.php:48
#: assets/build/js/acf-input.js:6756 assets/build/js/acf-input.js:7232
msgctxt "verb"
msgid "Select"
msgstr "Sélectionner"

#: includes/fields/class-acf-field-select.php:121
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Le chargement a échoué"

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Recherche en cours..."

#: includes/fields/class-acf-field-select.php:119
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Chargement de résultats supplémentaires…"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Vous ne pouvez choisir que %d éléments"

#: includes/fields/class-acf-field-select.php:117
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Vous ne pouvez choisir qu’un seul élément"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Veuillez supprimer %d caractères"

#: includes/fields/class-acf-field-select.php:115
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Veuillez supprimer 1 caractère"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Veuillez saisir %d caractères ou plus"

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Veuillez saisir au minimum 1 caractère"

#: includes/fields/class-acf-field-select.php:112
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Aucun résultat"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d résultats disponibles, utilisez les flèches haut et bas pour naviguer "
"parmi ceux-ci."

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Un résultat est disponible, appuyez sur Entrée pour le sélectionner."

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:780
msgctxt "noun"
msgid "Select"
msgstr "Liste déroulante"

#: includes/fields/class-acf-field-user.php:77
msgid "User ID"
msgstr "ID du compte"

#: includes/fields/class-acf-field-user.php:76
msgid "User Object"
msgstr "Objet du compte"

#: includes/fields/class-acf-field-user.php:75
msgid "User Array"
msgstr "Tableau de comptes"

#: includes/fields/class-acf-field-user.php:63
msgid "All user roles"
msgstr "Tous les rôles de comptes"

#: includes/fields/class-acf-field-user.php:55
msgid "Filter by role"
msgstr "Filtrer par rôle"

#: includes/fields/class-acf-field-user.php:20 includes/locations.php:101
msgid "User"
msgstr "Compte"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Séparateur"

#: includes/fields/class-acf-field-color_picker.php:76
msgid "Select Color"
msgstr "Sélectionner une couleur"

#: includes/admin/post-types/admin-post-type.php:126
#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/fields/class-acf-field-color_picker.php:74
#: assets/build/js/acf-internal-post-type.js:54
#: assets/build/js/acf-internal-post-type.js:59
msgid "Default"
msgstr "Par défaut"

#: includes/admin/views/acf-post-type/advanced-settings.php:85
#: includes/admin/views/acf-taxonomy/advanced-settings.php:137
#: includes/fields/class-acf-field-color_picker.php:72
msgid "Clear"
msgstr "Effacer"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Sélecteur de couleur"

#: includes/fields/class-acf-field-date_time_picker.php:88
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:87
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:83
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Sélectionner"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Terminé"

#: includes/fields/class-acf-field-date_time_picker.php:79
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Maintenant"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Fuseau horaire"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microseconde"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milliseconde"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Seconde"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minute"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Heure"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Heure"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Choisir l’heure"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Sélecteur de date et heure"

#: includes/fields/class-acf-field-accordion.php:106
msgid "Endpoint"
msgstr "Point de terminaison"

#: includes/admin/views/acf-field-group/options.php:115
#: includes/fields/class-acf-field-tab.php:115
msgid "Left aligned"
msgstr "Aligné à gauche"

#: includes/admin/views/acf-field-group/options.php:114
#: includes/fields/class-acf-field-tab.php:114
msgid "Top aligned"
msgstr "Aligné en haut"

#: includes/fields/class-acf-field-tab.php:110
msgid "Placement"
msgstr "Positionnement"

#: includes/fields/class-acf-field-tab.php:26
msgid "Tab"
msgstr "Onglet"

#: includes/fields/class-acf-field-url.php:162
msgid "Value must be a valid URL"
msgstr "Le champ doit contenir une URL valide"

#: includes/fields/class-acf-field-link.php:177
msgid "Link URL"
msgstr "URL du lien"

#: includes/fields/class-acf-field-link.php:176
msgid "Link Array"
msgstr "Tableau de liens"

#: includes/fields/class-acf-field-link.php:145
msgid "Opens in a new window/tab"
msgstr "Ouvrir dans une nouvelle fenêtre/onglet"

#: includes/fields/class-acf-field-link.php:140
msgid "Select Link"
msgstr "Sélectionner un lien"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Lien"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "E-mail"

#: includes/fields/class-acf-field-number.php:188
#: includes/fields/class-acf-field-range.php:217
msgid "Step Size"
msgstr "Taille de l’incrément"

#: includes/fields/class-acf-field-number.php:158
#: includes/fields/class-acf-field-range.php:195
msgid "Maximum Value"
msgstr "Valeur maximum"

#: includes/fields/class-acf-field-number.php:148
#: includes/fields/class-acf-field-range.php:184
msgid "Minimum Value"
msgstr "Valeur minimum"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Plage"

#: includes/fields/class-acf-field-button-group.php:175
#: includes/fields/class-acf-field-checkbox.php:379
#: includes/fields/class-acf-field-radio.php:220
#: includes/fields/class-acf-field-select.php:405
msgid "Both (Array)"
msgstr "Les deux (tableau)"

#: includes/admin/views/acf-field-group/fields.php:41
#: includes/fields/class-acf-field-button-group.php:174
#: includes/fields/class-acf-field-checkbox.php:378
#: includes/fields/class-acf-field-radio.php:219
#: includes/fields/class-acf-field-select.php:404
msgid "Label"
msgstr "Libellé"

#: includes/fields/class-acf-field-button-group.php:173
#: includes/fields/class-acf-field-checkbox.php:377
#: includes/fields/class-acf-field-radio.php:218
#: includes/fields/class-acf-field-select.php:403
msgid "Value"
msgstr "Valeur"

#: includes/fields/class-acf-field-button-group.php:222
#: includes/fields/class-acf-field-checkbox.php:441
#: includes/fields/class-acf-field-radio.php:292
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:221
#: includes/fields/class-acf-field-checkbox.php:442
#: includes/fields/class-acf-field-radio.php:293
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-checkbox.php:352
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-select.php:376
msgid "red : Red"
msgstr "rouge : Rouge"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-checkbox.php:352
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-select.php:376
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Pour plus de contrôle, vous pouvez spécifier une valeur et un libellé de "
"cette manière :"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-checkbox.php:352
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-select.php:376
msgid "Enter each choice on a new line."
msgstr "Saisir chaque choix sur une nouvelle ligne."

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:351
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-select.php:375
msgid "Choices"
msgstr "Choix"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Groupe de boutons"

#: includes/fields/class-acf-field-page_link.php:517
#: includes/fields/class-acf-field-post_object.php:433
#: includes/fields/class-acf-field-select.php:413
#: includes/fields/class-acf-field-user.php:86
msgid "Select multiple values?"
msgstr "Sélectionner des valeurs multiples ?"

#: includes/fields/class-acf-field-button-group.php:194
#: includes/fields/class-acf-field-page_link.php:538
#: includes/fields/class-acf-field-post_object.php:455
#: includes/fields/class-acf-field-radio.php:238
#: includes/fields/class-acf-field-select.php:435
#: includes/fields/class-acf-field-taxonomy.php:789
#: includes/fields/class-acf-field-user.php:107
msgid "Allow Null?"
msgstr "Autoriser une valeur vide ?"

#: includes/fields/class-acf-field-page_link.php:263
#: includes/fields/class-acf-field-post_object.php:264
#: includes/fields/class-acf-field-taxonomy.php:935
msgid "Parent"
msgstr "Parent"

#: includes/fields/class-acf-field-wysiwyg.php:397
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE ne sera pas initialisé avant un clic dans le champ"

#: includes/fields/class-acf-field-wysiwyg.php:396
msgid "Delay initialization?"
msgstr "Retarder l’initialisation ?"

#: includes/fields/class-acf-field-wysiwyg.php:385
msgid "Show Media Upload Buttons?"
msgstr "Afficher les boutons de téléversement de médias ?"

#: includes/fields/class-acf-field-wysiwyg.php:369
msgid "Toolbar"
msgstr "Barre d’outils"

#: includes/fields/class-acf-field-wysiwyg.php:361
msgid "Text Only"
msgstr "Texte Uniquement"

#: includes/fields/class-acf-field-wysiwyg.php:360
msgid "Visual Only"
msgstr "Visuel uniquement"

#: includes/fields/class-acf-field-wysiwyg.php:359
msgid "Visual & Text"
msgstr "Visuel & texte"

#: includes/fields/class-acf-field-wysiwyg.php:354
msgid "Tabs"
msgstr "Onglets"

#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Click to initialize TinyMCE"
msgstr "Cliquer pour initialiser TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:286
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Texte"

#: includes/fields/class-acf-field-wysiwyg.php:285
msgid "Visual"
msgstr "Visuel"

#: includes/fields/class-acf-field-text.php:183
#: includes/fields/class-acf-field-textarea.php:236
msgid "Value must not exceed %d characters"
msgstr "La valeur ne doit pas excéder %d caractères"

#: includes/fields/class-acf-field-text.php:118
#: includes/fields/class-acf-field-textarea.php:124
msgid "Leave blank for no limit"
msgstr "Laisser vide pour ne fixer aucune limite"

#: includes/fields/class-acf-field-text.php:117
#: includes/fields/class-acf-field-textarea.php:123
msgid "Character Limit"
msgstr "Limite de caractères"

#: includes/fields/class-acf-field-email.php:158
#: includes/fields/class-acf-field-number.php:209
#: includes/fields/class-acf-field-password.php:105
#: includes/fields/class-acf-field-range.php:239
#: includes/fields/class-acf-field-text.php:158
msgid "Appears after the input"
msgstr "Apparait après le champ"

#: includes/fields/class-acf-field-email.php:157
#: includes/fields/class-acf-field-number.php:208
#: includes/fields/class-acf-field-password.php:104
#: includes/fields/class-acf-field-range.php:238
#: includes/fields/class-acf-field-text.php:157
msgid "Append"
msgstr "Ajouter après"

#: includes/fields/class-acf-field-email.php:148
#: includes/fields/class-acf-field-number.php:199
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:229
#: includes/fields/class-acf-field-text.php:148
msgid "Appears before the input"
msgstr "Apparait avant le champ"

#: includes/fields/class-acf-field-email.php:147
#: includes/fields/class-acf-field-number.php:198
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:147
msgid "Prepend"
msgstr "Ajouter avant"

#: includes/fields/class-acf-field-email.php:138
#: includes/fields/class-acf-field-number.php:179
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-text.php:138
#: includes/fields/class-acf-field-textarea.php:156
#: includes/fields/class-acf-field-url.php:122
msgid "Appears within the input"
msgstr "Apparaît dans l’entrée"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:178
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-text.php:137
#: includes/fields/class-acf-field-textarea.php:155
#: includes/fields/class-acf-field-url.php:121
msgid "Placeholder Text"
msgstr "Texte indicatif"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:129
#: includes/fields/class-acf-field-radio.php:203
#: includes/fields/class-acf-field-range.php:165
#: includes/fields/class-acf-field-text.php:98
#: includes/fields/class-acf-field-textarea.php:104
#: includes/fields/class-acf-field-url.php:102
#: includes/fields/class-acf-field-wysiwyg.php:319
msgid "Appears when creating a new post"
msgstr "Apparaît à la création d’une nouvelle publication"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Texte"

#: includes/fields/class-acf-field-relationship.php:789
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s requiert au moins %2$s sélection"
msgstr[1] "%1$s requiert au moins %2$s sélections"

#: includes/fields/class-acf-field-post_object.php:424
#: includes/fields/class-acf-field-relationship.php:651
msgid "Post ID"
msgstr "ID de la publication"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:423
#: includes/fields/class-acf-field-relationship.php:650
msgid "Post Object"
msgstr "Objet de la publication"

#: includes/fields/class-acf-field-relationship.php:683
msgid "Maximum posts"
msgstr "Maximum de publications"

#: includes/fields/class-acf-field-relationship.php:673
msgid "Minimum posts"
msgstr "Minimum de publications"

#: includes/admin/views/acf-field-group/options.php:168
#: includes/admin/views/acf-post-type/advanced-settings.php:25
#: includes/fields/class-acf-field-relationship.php:708
msgid "Featured Image"
msgstr "Image mise en avant"

#: includes/fields/class-acf-field-relationship.php:704
msgid "Selected elements will be displayed in each result"
msgstr "Les éléments sélectionnés seront affichés dans chaque résultat"

#: includes/fields/class-acf-field-relationship.php:703
msgid "Elements"
msgstr "Éléments"

#: includes/fields/class-acf-field-relationship.php:637
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:709
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomie"

#: includes/fields/class-acf-field-relationship.php:636
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Type"
msgstr "Type de publication"

#: includes/fields/class-acf-field-relationship.php:630
msgid "Filters"
msgstr "Filtres"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-relationship.php:623
msgid "All taxonomies"
msgstr "Toutes les taxonomies"

#: includes/fields/class-acf-field-page_link.php:491
#: includes/fields/class-acf-field-post_object.php:403
#: includes/fields/class-acf-field-relationship.php:615
msgid "Filter by Taxonomy"
msgstr "Filtrer par taxonomie"

#: includes/fields/class-acf-field-page_link.php:469
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:593
msgid "All post types"
msgstr "Tous les types de publication"

#: includes/fields/class-acf-field-page_link.php:461
#: includes/fields/class-acf-field-post_object.php:373
#: includes/fields/class-acf-field-relationship.php:585
msgid "Filter by Post Type"
msgstr "Filtrer par type de publication"

#: includes/fields/class-acf-field-relationship.php:483
msgid "Search..."
msgstr "Rechercher…"

#: includes/fields/class-acf-field-relationship.php:413
msgid "Select taxonomy"
msgstr "Sélectionner la taxonomie"

#: includes/fields/class-acf-field-relationship.php:404
msgid "Select post type"
msgstr "Choisissez le type de publication"

#: includes/fields/class-acf-field-relationship.php:68
#: assets/build/js/acf-input.js:3925 assets/build/js/acf-input.js:4208
msgid "No matches found"
msgstr "Aucune correspondance trouvée"

#: includes/fields/class-acf-field-relationship.php:67
#: assets/build/js/acf-input.js:3908 assets/build/js/acf-input.js:4187
msgid "Loading"
msgstr "Chargement"

#: includes/fields/class-acf-field-relationship.php:66
#: assets/build/js/acf-input.js:3817 assets/build/js/acf-input.js:4083
msgid "Maximum values reached ( {max} values )"
msgstr "Valeurs maximum atteintes ({max} valeurs)"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Relation"

#: includes/fields/class-acf-field-file.php:291
#: includes/fields/class-acf-field-image.php:317
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Séparez les valeurs par une virgule. Laissez blanc pour tout autoriser."

#: includes/fields/class-acf-field-file.php:290
#: includes/fields/class-acf-field-image.php:316
msgid "Allowed file types"
msgstr "Types de fichiers autorisés"

#: includes/fields/class-acf-field-file.php:278
#: includes/fields/class-acf-field-image.php:280
msgid "Maximum"
msgstr "Maximum"

#: includes/fields/class-acf-field-file.php:154
#: includes/fields/class-acf-field-file.php:270
#: includes/fields/class-acf-field-file.php:282
#: includes/fields/class-acf-field-image.php:271
#: includes/fields/class-acf-field-image.php:307
msgid "File size"
msgstr "Taille du fichier"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:281
msgid "Restrict which images can be uploaded"
msgstr "Restreindre quelles images peuvent être téléversées"

#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:244
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:235
#: includes/fields/class-acf-field-image.php:210
msgid "Uploaded to post"
msgstr "Téléversé dans la publication"

#: includes/fields/class-acf-field-file.php:234
#: includes/fields/class-acf-field-image.php:209
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Tous"

#: includes/fields/class-acf-field-file.php:229
#: includes/fields/class-acf-field-image.php:204
msgid "Limit the media library choice"
msgstr "Limiter le choix de la médiathèque"

#: includes/fields/class-acf-field-file.php:228
#: includes/fields/class-acf-field-image.php:203
msgid "Library"
msgstr "Médiathèque"

#: includes/fields/class-acf-field-image.php:336
msgid "Preview Size"
msgstr "Taille de prévisualisation"

#: includes/fields/class-acf-field-image.php:195
msgid "Image ID"
msgstr "ID de l’image"

#: includes/fields/class-acf-field-image.php:194
msgid "Image URL"
msgstr "URL de l’image"

#: includes/fields/class-acf-field-image.php:193
msgid "Image Array"
msgstr "Tableau de l’image"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:372
#: includes/fields/class-acf-field-file.php:213
#: includes/fields/class-acf-field-link.php:171
#: includes/fields/class-acf-field-radio.php:213
msgid "Specify the returned value on front end"
msgstr "Spécifier la valeur renvoyée publiquement"

#: includes/fields/class-acf-field-button-group.php:167
#: includes/fields/class-acf-field-checkbox.php:371
#: includes/fields/class-acf-field-file.php:212
#: includes/fields/class-acf-field-link.php:170
#: includes/fields/class-acf-field-radio.php:212
#: includes/fields/class-acf-field-taxonomy.php:753
msgid "Return Value"
msgstr "Valeur de retour"

#: includes/fields/class-acf-field-image.php:162
msgid "Add Image"
msgstr "Ajouter image"

#: includes/fields/class-acf-field-image.php:162
msgid "No image selected"
msgstr "Aucune image sélectionnée"

#: includes/assets.php:352 includes/fields/class-acf-field-file.php:162
#: includes/fields/class-acf-field-image.php:142
#: includes/fields/class-acf-field-link.php:145 assets/build/js/acf.js:1566
#: assets/build/js/acf.js:1661
msgid "Remove"
msgstr "Retirer"

#: includes/admin/views/acf-field-group/field.php:72
#: includes/fields/class-acf-field-file.php:160
#: includes/fields/class-acf-field-image.php:140
#: includes/fields/class-acf-field-link.php:145
msgid "Edit"
msgstr "Modifier"

#: includes/fields/class-acf-field-image.php:70 includes/media.php:55
#: assets/build/js/acf-input.js:6803 assets/build/js/acf-input.js:7286
msgid "All images"
msgstr "Toutes les images"

#: includes/fields/class-acf-field-image.php:69
#: assets/build/js/acf-input.js:3181 assets/build/js/acf-input.js:3399
msgid "Update Image"
msgstr "Mettre à jour l’image"

#: includes/fields/class-acf-field-image.php:68
#: assets/build/js/acf-input.js:3180 assets/build/js/acf-input.js:3398
msgid "Edit Image"
msgstr "Modifier l’image"

#: includes/fields/class-acf-field-image.php:67
#: assets/build/js/acf-input.js:3156 assets/build/js/acf-input.js:3373
msgid "Select Image"
msgstr "Sélectionner une image"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Image"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Permet l’affichage du code HTML à l’écran au lieu de l’interpréter"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Autoriser le code HTML"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:172
msgid "No Formatting"
msgstr "Aucun formatage"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:171
msgid "Automatically add &lt;br&gt;"
msgstr "Ajouter automatiquement &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:114
#: includes/fields/class-acf-field-textarea.php:170
msgid "Automatically add paragraphs"
msgstr "Ajouter automatiquement des paragraphes"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:166
msgid "Controls how new lines are rendered"
msgstr "Contrôle comment les nouvelles lignes sont rendues"

#: includes/fields/class-acf-field-message.php:109
#: includes/fields/class-acf-field-textarea.php:165
msgid "New Lines"
msgstr "Nouvelles lignes"

#: includes/fields/class-acf-field-date_picker.php:232
#: includes/fields/class-acf-field-date_time_picker.php:220
msgid "Week Starts On"
msgstr "La semaine débute le"

#: includes/fields/class-acf-field-date_picker.php:201
msgid "The format used when saving a value"
msgstr "Le format utilisé lors de la sauvegarde d’une valeur"

#: includes/fields/class-acf-field-date_picker.php:200
msgid "Save Format"
msgstr "Enregistrer le format"

#: includes/fields/class-acf-field-date_picker.php:67
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Sem."

#: includes/fields/class-acf-field-date_picker.php:66
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Préc."

#: includes/fields/class-acf-field-date_picker.php:65
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Suivant"

#: includes/fields/class-acf-field-date_picker.php:64
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Aujourd’hui"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Terminé"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Sélecteur de date"

#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:284
#: includes/fields/class-acf-field-oembed.php:268
msgid "Width"
msgstr "Largeur"

#: includes/fields/class-acf-field-oembed.php:265
#: includes/fields/class-acf-field-oembed.php:277
msgid "Embed Size"
msgstr "Taille d’intégration"

#: includes/fields/class-acf-field-oembed.php:222
msgid "Enter URL"
msgstr "Saisissez l’URL"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "Contenu oEmbed"

#: includes/fields/class-acf-field-true_false.php:184
msgid "Text shown when inactive"
msgstr "Texte affiché lorsque inactif"

#: includes/fields/class-acf-field-true_false.php:183
msgid "Off Text"
msgstr "Texte « Inactif »"

#: includes/fields/class-acf-field-true_false.php:168
msgid "Text shown when active"
msgstr "Texte affiché lorsque actif"

#: includes/fields/class-acf-field-true_false.php:167
msgid "On Text"
msgstr "Texte « Actif »"

#: includes/fields/class-acf-field-select.php:456
#: includes/fields/class-acf-field-true_false.php:199
msgid "Stylized UI"
msgstr "Interface (UI) stylisée"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:361
#: includes/fields/class-acf-field-color_picker.php:158
#: includes/fields/class-acf-field-email.php:117
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-range.php:164
#: includes/fields/class-acf-field-select.php:386
#: includes/fields/class-acf-field-text.php:97
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-true_false.php:147
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:318
msgid "Default Value"
msgstr "Valeur par défaut"

#: includes/fields/class-acf-field-true_false.php:138
msgid "Displays text alongside the checkbox"
msgstr "Affiche le texte à côté de la case à cocher"

#: includes/fields/class-acf-field-message.php:26
#: includes/fields/class-acf-field-message.php:99
#: includes/fields/class-acf-field-true_false.php:137
msgid "Message"
msgstr "Message"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:86
#: includes/fields/class-acf-field-true_false.php:187
#: assets/build/js/acf.js:1743 assets/build/js/acf.js:1861
msgid "No"
msgstr "Non"

#: includes/assets.php:350 includes/fields/class-acf-field-true_false.php:83
#: includes/fields/class-acf-field-true_false.php:171
#: assets/build/js/acf.js:1742 assets/build/js/acf.js:1860
msgid "Yes"
msgstr "Oui"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Vrai/Faux"

#: includes/fields/class-acf-field-group.php:474
msgid "Row"
msgstr "Ligne"

#: includes/fields/class-acf-field-group.php:473
msgid "Table"
msgstr "Tableau"

#: includes/admin/post-types/admin-field-group.php:131
#: includes/fields/class-acf-field-group.php:472
msgid "Block"
msgstr "Bloc"

#: includes/fields/class-acf-field-group.php:467
msgid "Specify the style used to render the selected fields"
msgstr "Spécifier le style utilisé pour afficher les champs sélectionnés"

#: includes/fields.php:356 includes/fields/class-acf-field-button-group.php:215
#: includes/fields/class-acf-field-checkbox.php:435
#: includes/fields/class-acf-field-group.php:466
#: includes/fields/class-acf-field-radio.php:286
msgid "Layout"
msgstr "Mise en page"

#: includes/fields/class-acf-field-group.php:450
msgid "Sub Fields"
msgstr "Sous-champs"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Groupe"

#: includes/fields/class-acf-field-google-map.php:235
msgid "Customize the map height"
msgstr "Personnaliser la hauteur de la carte"

#: includes/fields/class-acf-field-google-map.php:234
#: includes/fields/class-acf-field-image.php:259
#: includes/fields/class-acf-field-image.php:295
#: includes/fields/class-acf-field-oembed.php:280
msgid "Height"
msgstr "Hauteur"

#: includes/fields/class-acf-field-google-map.php:223
msgid "Set the initial zoom level"
msgstr "Définir le niveau de zoom initial"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:196
#: includes/fields/class-acf-field-google-map.php:209
msgid "Center the initial map"
msgstr "Centrer la carte initiale"

#: includes/fields/class-acf-field-google-map.php:195
#: includes/fields/class-acf-field-google-map.php:208
msgid "Center"
msgstr "Centrer"

#: includes/fields/class-acf-field-google-map.php:163
msgid "Search for address..."
msgstr "Rechercher une adresse…"

#: includes/fields/class-acf-field-google-map.php:160
msgid "Find current location"
msgstr "Obtenir l’emplacement actuel"

#: includes/fields/class-acf-field-google-map.php:159
msgid "Clear location"
msgstr "Effacer la position"

#: includes/fields/class-acf-field-google-map.php:158
#: includes/fields/class-acf-field-relationship.php:635
msgid "Search"
msgstr "Rechercher"

#: includes/fields/class-acf-field-google-map.php:63
#: assets/build/js/acf-input.js:2840 assets/build/js/acf-input.js:3026
msgid "Sorry, this browser does not support geolocation"
msgstr "Désolé, ce navigateur ne prend pas en charge la géolocalisation"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google Map"

#: includes/fields/class-acf-field-date_picker.php:212
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:132
msgid "The format returned via template functions"
msgstr "Le format retourné via les fonctions du modèle"

#: includes/fields/class-acf-field-color_picker.php:182
#: includes/fields/class-acf-field-date_picker.php:211
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-image.php:187
#: includes/fields/class-acf-field-post_object.php:418
#: includes/fields/class-acf-field-relationship.php:645
#: includes/fields/class-acf-field-select.php:397
#: includes/fields/class-acf-field-time_picker.php:131
#: includes/fields/class-acf-field-user.php:70
msgid "Return Format"
msgstr "Format de retour"

#: includes/fields/class-acf-field-date_picker.php:190
#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:192
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:123
#: includes/fields/class-acf-field-time_picker.php:139
msgid "Custom:"
msgstr "Personnalisé :"

#: includes/fields/class-acf-field-date_picker.php:182
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:116
msgid "The format displayed when editing a post"
msgstr "Le format affiché lors de la modification d’une publication"

#: includes/fields/class-acf-field-date_picker.php:181
#: includes/fields/class-acf-field-date_time_picker.php:182
#: includes/fields/class-acf-field-time_picker.php:115
msgid "Display Format"
msgstr "Format d’affichage"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Sélecteur d’heure"

#. translators: counts for inactive field groups
#: acf.php:491
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "<span class=\"count\">(%s)</span> inactif"
msgstr[1] "<span class=\"count\">(%s)</span> inactifs"

#: acf.php:450
msgid "No Fields found in Trash"
msgstr "Aucun champ trouvé dans la corbeille"

#: acf.php:449
msgid "No Fields found"
msgstr "Aucun champ trouvé"

#: acf.php:448
msgid "Search Fields"
msgstr "Rechercher des champs"

#: acf.php:447
msgid "View Field"
msgstr "Voir le champ"

#: acf.php:446 includes/admin/views/acf-field-group/fields.php:104
msgid "New Field"
msgstr "Nouveau champ"

#: acf.php:445
msgid "Edit Field"
msgstr "Modifier le champ"

#: acf.php:444
msgid "Add New Field"
msgstr "Ajouter un nouveau champ"

#: acf.php:442
msgid "Field"
msgstr "Champ"

#: acf.php:441 includes/admin/post-types/admin-field-group.php:154
#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/views/acf-field-group/fields.php:21
msgid "Fields"
msgstr "Champs"

#: acf.php:416
msgid "No Field Groups found in Trash"
msgstr "Aucun groupe de champs trouvé dans la corbeille"

#: acf.php:415
msgid "No Field Groups found"
msgstr "Aucun groupe de champs trouvé"

#: acf.php:414
msgid "Search Field Groups"
msgstr "Rechercher des groupes de champs"

#: acf.php:413
msgid "View Field Group"
msgstr "Voir le groupe de champs"

#: acf.php:412
msgid "New Field Group"
msgstr "Nouveau groupe de champs"

#: acf.php:411
msgid "Edit Field Group"
msgstr "Modifier le groupe de champs"

#: acf.php:410
msgid "Add New Field Group"
msgstr "Ajouter un groupe de champs"

#: acf.php:409 acf.php:443
#: includes/admin/views/acf-post-type/advanced-settings.php:215
#: includes/admin/views/acf-post-type/advanced-settings.php:217
#: includes/post-types/class-acf-post-type.php:92
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Ajouter"

#: acf.php:408
msgid "Field Group"
msgstr "Groupe de champs"

#: acf.php:407 includes/admin/post-types/admin-field-groups.php:56
#: includes/admin/post-types/admin-post-types.php:105
#: includes/admin/post-types/admin-taxonomies.php:105
msgid "Field Groups"
msgstr "Groupes de champs"

#. Description of the plugin
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Personnalisez WordPress avec des champs intuitifs, puissants et "
"professionnels."

#. Plugin URI of the plugin
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com/"

#. Plugin Name of the plugin
#: acf.php:92
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

# @ acf
#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:170
msgid "Block type name is required."
msgstr "Le nom du type de bloc est obligatoire."

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr "Le type de bloc \"%s\" est déjà déclaré."

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr "Passer en mode Édition"

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr "Passer en mode Aperçu"

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr "Modifier l’alignement du contenu"

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr "Réglages de %s "

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr "Ce bloc ne contient aucun champ éditable."

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""
"Assignez un <a href=\"%s\" target=\"_blank\">groupe de champs</a> pour "
"ajouter des champs à ce bloc."

# @ acf
#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Options mises à jour"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a href=\"%1$s"
"\">Updates</a> page. If you don't have a licence key, please see <a href="
"\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Pour activer les mises à jour, veuillez indiquer votre clé de licence sur la "
"page <a href=\"%1$s\">Mises à jour</a>. Si vous n’en possédez pas encore "
"une, jetez un oeil à nos <a href=\"%2$s\" target=\"_blank\">détails & "
"tarifs</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""
"<b>Erreur d’activation d’ACF</b>. Votre clé de licence a été modifiée, mais "
"une erreur est survenue lors de la désactivation de votre précédente licence"

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""
"<b>Erreur d’activation d’ACF</b>. Votre clé de licence définie a été "
"modifiée, mais une erreur est survenue lors de la connexion au serveur "
"d’activation"

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr "<b>Erreur d’activation d’ACF</b>"

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""
"<b>Erreur d’activation d’ACF</b>. Une erreur est survenue lors de la "
"connexion au serveur d’activation"

#: pro/updates.php:279
msgid "Check Again"
msgstr "Vérifier à nouveau"

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""
"<b>Erreur d’activation d’ACF</b>. Impossible de se connecter au serveur "
"d’activation"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Publier"

# @ default
#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Aucun groupe de champs trouvé pour cette page options. <a href=\"%s\">Créer "
"un groupe de champs</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Erreur</b>. Impossible de joindre le serveur"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Erreur</b>. Impossible d’authentifier la mise à jour. Merci d’essayer à "
"nouveau et si le problème persiste, désactivez et réactivez votre licence "
"ACF PRO."

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Erreur</b>. La licence pour ce site a expiré ou a été désactivée. "
"Veuillez réactiver votre licence ACF PRO."

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr "Sélectionnez un ou plusieurs champs à cloner"

# @ acf
#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "Format d’affichage"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr "Définit le style utilisé pour générer le champ dupliqué"

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Groupe (affiche les champs sélectionnés dans un groupe à l’intérieur de ce "
"champ)"

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr "Remplace ce champ par les champs sélectionnés"

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr "Les libellés seront affichés en tant que %s"

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr "Préfixer les libellés de champs"

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr "Les valeurs seront enregistrées en tant que %s"

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr "Préfixer les noms de champs"

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr "Champ inconnu"

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr "Groupe de champ inconnu"

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr "Tous les champs du groupe %s"

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "Ajouter un élément"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
msgid "layout"
msgid_plural "layouts"
msgstr[0] "disposition"
msgstr[1] "dispositions"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "dispositions"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Ce champ requiert au moins {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Ce champ a une limite de {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} disponible (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} required (min {min})"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "Le contenu flexible nécessite au moins une disposition"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""
"Cliquez sur le bouton \"%s\" ci-dessous pour créer votre première disposition"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Ajouter une disposition"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Duplicate layout"
msgstr "Dupliquer la disposition"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "Retirer la disposition"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr "Cliquer pour afficher/cacher"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "Supprimer la disposition"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "Dupliquer la disposition"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "Ajouter une disposition"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:564
#, fuzzy
#| msgid "Add layout"
msgid "Add Layout"
msgstr "Ajouter une disposition"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "Max"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "Nombre minimum de dispositions"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "Nombre maximum de dispositions"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "Intitulé du bouton"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr "la valeur de %s doit être un tableau ou null."

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] "Le champ %1$s doit contenir au moins %2$s %3$s disposition."
msgstr[1] "Le champ %1$s doit contenir au moins %2$s %3$s dispositions."

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] "Le champ %1$s doit contenir au maximum %2$s %3$s disposition."
msgstr[1] "Le champ %1$s doit contenir au maximum %2$s %3$s dispositions."

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

# @ acf
#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "Ajouter l’image à la galerie"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "Nombre de sélections maximales atteint"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "Longueur"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "Légende"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr "Texte alternatif"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "Ajouter à la galerie"

# @ acf
#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "Actions de groupe"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "Ranger par date d’import"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "Ranger par date de modification"

# @ acf
#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "Ranger par titre"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "Inverser l’ordre actuel"

# @ acf
#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "Appliquer"

# @ acf
#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "Minimum d’images"

# @ acf
#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "Maximum d’images"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr "Insérer"

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr "Définir comment les images sont insérées"

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr "Insérer à la fin"

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr "Insérer au début"

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
#, fuzzy
#| msgid "Minimum rows reached ({min} rows)"
msgid "Minimum rows not reached ({min} rows)"
msgstr "Nombre minimal d’éléments atteint ({min} éléments)"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "Nombre maximal d’éléments atteint ({max} éléments)"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr "Erreur de chargement de la page"

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr "Utile pour les champs avec un grand nombre de lignes."

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr "Lignes par Page"

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr "Définir le nombre de lignes à afficher sur une page."

# @ acf
#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "Nombre minimum d’éléments"

# @ acf
#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "Nombre maximum d’éléments"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr "Replié"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr "Choisir un sous champ à montrer lorsque la ligne est refermée"

#: pro/fields/class-acf-field-repeater.php:1060
#, fuzzy
#| msgid "Invalid field key."
msgid "Invalid field key or name."
msgstr "Clé de champ invalide"

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr "Il y a une erreur lors de la récupération du champ."

#: pro/fields/class-acf-repeater-table.php:369
#, fuzzy
#| msgid "Drag to reorder"
msgid "Click to reorder"
msgstr "Glisser pour réorganiser"

# @ acf
#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "Ajouter un élément"

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr "Dupliquer la ligne"

# @ acf
#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "Retirer l’élément"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr "Page actuelle"

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
#, fuzzy
#| msgid "First page"
msgid "First Page"
msgstr "Première page"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
#, fuzzy
#| msgid "Previous page"
msgid "Previous Page"
msgstr "Page précédente"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr "%1$s sur %2$s"

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
#, fuzzy
#| msgid "Next page"
msgid "Next Page"
msgstr "Page suivante"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
#, fuzzy
#| msgid "Last page"
msgid "Last Page"
msgstr "Dernière page"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr "Aucun type de blocs existant"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Aucune page d’option créée"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Désactiver la licence"

# @ acf
#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Activer votre licence"

# @ acf
#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informations sur la licence"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Pour débloquer les mises à jour, veuillez entrer votre clé de licence ci-"
"dessous. Si vous n’en possédez pas encore une, jetez un oeil à nos <a href="
"\"%s\" target=\"_blank\">détails & tarifs</a>."

# @ acf
#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Clé de licence"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr "Votre clé de licence est définie dans le fichier wp-config.php"

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr "Retenter l’activation"

# @ acf
#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informations de mise à jour"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Version actuelle"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Dernière version"

# @ acf
#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Mise à jour disponible"

# @ wp3i
#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "Améliorations"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Enter your license key to unlock updates"
msgstr "Entrez votre clé de licence ci-dessous pour activer les mises à jour"

# @ acf
#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "Mettre à jour l’extension"

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr "Veuillez réactiver votre licence afin de débloquer les mises à jour"
