# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-06-27T14:09:59+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: en_GB\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:17
msgid "%s fields"
msgstr "%s fields"

#: includes/admin/post-types/admin-taxonomies.php:260
msgid "No terms"
msgstr "No terms"

#: includes/admin/post-types/admin-taxonomies.php:233
msgid "No post types"
msgstr "No post types"

#: includes/admin/post-types/admin-post-types.php:256
msgid "No posts"
msgstr "No posts"

#: includes/admin/post-types/admin-post-types.php:230
msgid "No taxonomies"
msgstr "No taxonomies"

#: includes/admin/post-types/admin-post-types.php:175
#: includes/admin/post-types/admin-taxonomies.php:175
msgid "No field groups"
msgstr "No field groups"

#: includes/admin/post-types/admin-field-groups.php:259
msgid "No fields"
msgstr "No fields"

#: includes/admin/post-types/admin-field-groups.php:132
#: includes/admin/post-types/admin-post-types.php:139
#: includes/admin/post-types/admin-taxonomies.php:139
msgid "No description"
msgstr "No description"

#: includes/fields/class-acf-field-page_link.php:484
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:608
msgid "Any post status"
msgstr "Any post status"

#: includes/post-types/class-acf-taxonomy.php:278
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."

#: includes/post-types/class-acf-taxonomy.php:273
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."

#: includes/post-types/class-acf-taxonomy.php:246
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."

#: includes/post-types/class-acf-taxonomy.php:241
msgid "The taxonomy key must be under 20 characters."
msgstr "The taxonomy key must be under 20 characters."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "No Taxonomies found in the bin"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "No Taxonomies found"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Search Taxonomies"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "View Taxonomy"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "New Taxonomy"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Edit Taxonomy"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Add New Taxonomy"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found in Trash"
msgstr "No Post Types found in the bin"

#: includes/post-types/class-acf-post-type.php:98
msgid "No Post Types found"
msgstr "No Post Types found"

#: includes/post-types/class-acf-post-type.php:97
msgid "Search Post Types"
msgstr "Search Post Types"

#: includes/post-types/class-acf-post-type.php:96
msgid "View Post Type"
msgstr "View Post Type"

#: includes/post-types/class-acf-post-type.php:95
msgid "New Post Type"
msgstr "New Post Type"

#: includes/post-types/class-acf-post-type.php:94
msgid "Edit Post Type"
msgstr "Edit Post Type"

#: includes/post-types/class-acf-post-type.php:93
msgid "Add New Post Type"
msgstr "Add New Post Type"

#: includes/post-types/class-acf-post-type.php:338
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:312
#: includes/post-types/class-acf-taxonomy.php:252
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."

#: includes/post-types/class-acf-post-type.php:306
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."

#: includes/post-types/class-acf-post-type.php:301
msgid "The post type key must be under 20 characters."
msgstr "The post type key must be under 20 characters."

#: includes/fields/class-acf-field-wysiwyg.php:27
msgid "We do not recommend using this field in ACF Blocks."
msgstr "We do not recommend using this field in ACF Blocks."

#: includes/fields/class-acf-field-wysiwyg.php:27
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "WYSIWYG Editor"
msgstr "WYSIWYG Editor"

#: includes/fields/class-acf-field-user.php:22
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."

#: includes/fields/class-acf-field-url.php:26
msgid "A text input specifically designed for storing web addresses."
msgstr "A text input specifically designed for storing web addresses."

#: includes/fields/class-acf-field-url.php:25
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:27
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylised switch or checkbox."

#: includes/fields/class-acf-field-time_picker.php:27
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"An interactive UI for picking a time. The time format can be customised "
"using the field settings."

#: includes/fields/class-acf-field-textarea.php:26
msgid "A basic textarea input for storing paragraphs of text."
msgstr "A basic textarea input for storing paragraphs of text."

#: includes/fields/class-acf-field-text.php:26
msgid "A basic text input, useful for storing single string values."
msgstr "A basic text input, useful for storing single string values."

#: includes/fields/class-acf-field-taxonomy.php:30
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."

#: includes/fields/class-acf-field-tab.php:28
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organised and structured."

#: includes/fields/class-acf-field-select.php:27
msgid "A dropdown list with a selection of choices that you specify."
msgstr "A dropdown list with a selection of choices that you specify."

#: includes/fields/class-acf-field-relationship.php:27
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."

#: includes/fields/class-acf-field-range.php:26
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."

#: includes/fields/class-acf-field-radio.php:27
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."

#: includes/fields/class-acf-field-post_object.php:27
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"An interactive and customisable UI for picking one or many posts, pages or "
"post type items with the option to search. "

#: includes/fields/class-acf-field-password.php:26
msgid "An input for providing a password using a masked field."
msgstr "An input for providing a password using a masked field."

#: includes/fields/class-acf-field-page_link.php:476
#: includes/fields/class-acf-field-post_object.php:388
#: includes/fields/class-acf-field-relationship.php:600
msgid "Filter by Post Status"
msgstr "Filter by Post Status"

#: includes/fields/class-acf-field-page_link.php:27
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."

#: includes/fields/class-acf-field-oembed.php:27
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."

#: includes/fields/class-acf-field-number.php:26
msgid "An input limited to numerical values."
msgstr "An input limited to numerical values."

#: includes/fields/class-acf-field-message.php:28
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."

#: includes/fields/class-acf-field-link.php:27
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."

#: includes/fields/class-acf-field-image.php:27
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr "Uses the native WordPress media picker to upload, or choose images."

#: includes/fields/class-acf-field-group.php:27
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"Provides a way to structure fields into groups to better organise the data "
"and the edit screen."

#: includes/fields/class-acf-field-google-map.php:27
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."

#: includes/fields/class-acf-field-file.php:27
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr "Uses the native WordPress media picker to upload, or choose files."

#: includes/fields/class-acf-field-email.php:26
msgid "A text input specifically designed for storing email addresses."
msgstr "A text input specifically designed for storing email addresses."

#: includes/fields/class-acf-field-date_time_picker.php:27
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"An interactive UI for picking a date and time. The date return format can be "
"customised using the field settings."

#: includes/fields/class-acf-field-date_picker.php:27
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"An interactive UI for picking a date. The date return format can be "
"customised using the field settings."

#: includes/fields/class-acf-field-color_picker.php:27
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr "An interactive UI for selecting a colour, or specifying a Hex value."

#: includes/fields/class-acf-field-checkbox.php:27
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."

#: includes/fields/class-acf-field-button-group.php:26
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."

#: includes/fields/class-acf-field-accordion.php:27
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"Allows you to group and organise custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."

#: includes/fields.php:473
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."

#: includes/fields.php:463
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."

#: includes/fields.php:453
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."

#: includes/fields.php:444
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."

#: includes/fields.php:441
msgctxt "noun"
msgid "Clone"
msgstr "Clone"

#: includes/fields.php:357
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:355
msgid "Advanced"
msgstr "Advanced"

#: includes/ajax/class-acf-ajax-local-json-diff.php:85
msgid "JSON (newer)"
msgstr "JSON (newer)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Original"
msgstr "Original"

#: includes/ajax/class-acf-ajax-local-json-diff.php:55
msgid "Invalid post ID."
msgstr "Invalid post ID."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid post type selected for review."
msgstr "Invalid post type selected for review."

#: includes/admin/views/global/navigation.php:104
msgid "More"
msgstr "More"

#: includes/admin/views/browse-fields-modal.php:86
msgid "Tutorial"
msgstr "Tutorial"

#: includes/admin/views/browse-fields-modal.php:75
msgid "Available with ACF PRO"
msgstr "Available with ACF PRO"

#: includes/admin/views/browse-fields-modal.php:63
msgid "Select Field"
msgstr "Select Field"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:50
msgid "Try a different search term or browse %s"
msgstr "Try a different search term or browse %s"

#: includes/admin/views/browse-fields-modal.php:47
msgid "Popular fields"
msgstr "Popular fields"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:40
msgid "No search results for '%s'"
msgstr "No search results for '%s'"

#: includes/admin/views/browse-fields-modal.php:13
msgid "Search fields..."
msgstr "Search fields..."

#: includes/admin/views/browse-fields-modal.php:11
msgid "Select Field Type"
msgstr "Select Field Type"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Popular"

#: includes/admin/views/acf-taxonomy/list-empty.php:7
msgid "Add Taxonomy"
msgstr "Add Taxonomy"

#: includes/admin/views/acf-taxonomy/list-empty.php:6
msgid "Create custom taxonomies to classify post type content"
msgstr "Create custom taxonomies to classify post type content"

#: includes/admin/views/acf-taxonomy/list-empty.php:5
msgid "Add Your First Taxonomy"
msgstr "Add Your First Taxonomy"

#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr "Hierarchical taxonomies can have descendants (like categories)."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr "Makes a taxonomy visible on the frontend and in the admin dashboard."

#: includes/admin/views/acf-taxonomy/basic-settings.php:75
msgid "One or many post types that can be classified with this taxonomy."
msgstr "One or many post types that can be classified with this taxonomy."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:44
msgid "genre"
msgstr "genre"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:26
msgid "Genre"
msgstr "Genre"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:9
msgid "Genres"
msgstr "Genres"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1129
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1073
msgid "Expose this post type in the REST API."
msgstr "Expose this post type in the REST API."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Customize the query variable name"
msgstr "Customise the query variable name"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1024
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:977
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "Parent-child terms in URLs for hierarchical taxonomies."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:937
msgid "Customize the slug used in the URL"
msgstr "Customise the slug used in the URL"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:920
msgid "Permalinks for this taxonomy are disabled."
msgstr "Permalinks for this taxonomy are disabled."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:917
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:909
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1026
#: includes/admin/views/acf-taxonomy/basic-settings.php:41
msgid "Taxonomy Key"
msgstr "Taxonomy Key"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:907
msgid "Select the type of permalink to use for this taxonomy."
msgstr "Select the type of permalink to use for this taxonomy."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:892
msgid "Display a column for the taxonomy on post type listing screens."
msgstr "Display a column for the taxonomy on post type listing screens."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:891
msgid "Show Admin Column"
msgstr "Show Admin Column"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:878
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "Show the taxonomy in the quick/bulk edit panel."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:877
msgid "Quick Edit"
msgstr "Quick Edit"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:864
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "List the taxonomy in the Tag Cloud Widget controls."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:863
msgid "Tag Cloud"
msgstr "Tag Cloud"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:820
msgid ""
"A PHP function name to be called tor sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"A PHP function name to be called tor sanitising taxonomy data saved from a "
"meta box."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:819
msgid "Meta Box Sanitization Callback"
msgstr "Meta Box Sanitisation Callback"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:801
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy."
msgstr ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:800
msgid "Register Meta Box Callback"
msgstr "Register Meta Box Callback"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:759
msgid "No Meta Box"
msgstr "No Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:758
msgid "Custom Meta Box"
msgstr "Custom Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:754
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:753
msgid "Meta Box"
msgstr "Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:742
#: includes/admin/views/acf-taxonomy/advanced-settings.php:763
msgid "Categories Meta Box"
msgstr "Categories Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:741
#: includes/admin/views/acf-taxonomy/advanced-settings.php:762
msgid "Tags Meta Box"
msgstr "Tags Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:700
msgid "A link to a tag"
msgstr "A link to a tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:699
msgid "Describes a navigation link block variation used in the block editor."
msgstr "Describes a navigation link block variation used in the block editor."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:694
msgid "A link to a %s"
msgstr "A link to a %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:679
msgid "Tag Link"
msgstr "Tag Link"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""
"Assigns a title for navigation link block variation used in the block editor."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:659
msgid "← Go to tags"
msgstr "← Go to tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:658
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""
"Assigns the text used to link back to the main index after updating a term."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "Back To Items"
msgstr "Back To Items"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:653
msgid "← Go to %s"
msgstr "← Go to %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:638
msgid "Tags list"
msgstr "Tags list"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "Assigns text to the table hidden heading."
msgstr "Assigns text to the table hidden heading."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:618
msgid "Tags list navigation"
msgstr "Tags list navigation"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "Assigns text to the table pagination hidden heading."
msgstr "Assigns text to the table pagination hidden heading."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:593
msgid "Filter by category"
msgstr "Filter by category"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:592
msgid "Assigns text to the filter button in the posts lists table."
msgstr "Assigns text to the filter button in the posts lists table."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter By Item"
msgstr "Filter By Item"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:587
msgid "Filter by %s"
msgstr "Filter by %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:571
#: includes/admin/views/acf-taxonomy/advanced-settings.php:572
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""
"The description is not prominent by default; however, some themes may show "
"it."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:570
msgid "Describes the Description field on the Edit Tags screen."
msgstr "Describes the Description field on the Edit Tags screen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:569
msgid "Description Field Description"
msgstr "Description Field Description"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:550
#: includes/admin/views/acf-taxonomy/advanced-settings.php:551
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:549
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "Describes the Parent field on the Edit Tags screen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:548
msgid "Parent Field Description"
msgstr "Parent Field Description"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:534
#: includes/admin/views/acf-taxonomy/advanced-settings.php:535
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:533
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "Describes the Slug field on the Edit Tags screen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:532
msgid "Slug Field Description"
msgstr "Slug Field Description"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:518
#: includes/admin/views/acf-taxonomy/advanced-settings.php:519
msgid "The name is how it appears on your site"
msgstr "The name is how it appears on your site"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:517
msgid "Describes the Name field on the Edit Tags screen."
msgstr "Describes the Name field on the Edit Tags screen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:516
msgid "Name Field Description"
msgstr "Name Field Description"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:503
msgid "No tags"
msgstr "No tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:502
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No Terms"
msgstr "No Terms"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:497
msgid "No %s"
msgstr "No %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:482
msgid "No tags found"
msgstr "No tags found"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:481
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "Not Found"
msgstr "Not Found"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:459
msgid "Assigns text to the Title field of the Most Used tab."
msgstr "Assigns text to the Title field of the Most Used tab."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:458
#: includes/admin/views/acf-taxonomy/advanced-settings.php:460
#: includes/admin/views/acf-taxonomy/advanced-settings.php:461
msgid "Most Used"
msgstr "Most Used"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:440
msgid "Choose from the most used tags"
msgstr "Choose from the most used tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:439
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose From Most Used"
msgstr "Choose From Most Used"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:434
msgid "Choose from the most used %s"
msgstr "Choose from the most used %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:414
msgid "Add or remove tags"
msgstr "Add or remove tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:413
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add Or Remove Items"
msgstr "Add Or Remove Items"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:408
msgid "Add or remove %s"
msgstr "Add or remove %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:388
msgid "Separate tags with commas"
msgstr "Separate tags with commas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:387
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate Items With Commas"
msgstr "Separate Items With Commas"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:382
msgid "Separate %s with commas"
msgstr "Separate %s with commas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:362
msgid "Popular Tags"
msgstr "Popular Tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr "Assigns popular items text. Only used for non-hierarchical taxonomies."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:360
msgid "Popular Items"
msgstr "Popular Items"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:357
msgid "Popular %s"
msgstr "Popular %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:343
msgid "Search Tags"
msgstr "Search Tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Assigns search items text."
msgstr "Assigns search items text."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:319
msgid "Parent Category:"
msgstr "Parent Category:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr "Assigns parent item text, but with a colon (:) added to the end."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:317
msgid "Parent Item With Colon"
msgstr "Parent Item With Colon"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:294
msgid "Parent Category"
msgstr "Parent Category"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr "Assigns parent item text. Only used on hierarchical taxonomies."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:292
msgid "Parent Item"
msgstr "Parent Item"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:289
msgid "Parent %s"
msgstr "Parent %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:274
msgid "New Tag Name"
msgstr "New Tag Name"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "Assigns the new item name text."
msgstr "Assigns the new item name text."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:272
msgid "New Item Name"
msgstr "New Item Name"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:269
msgid "New %s Name"
msgstr "New %s Name"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:254
msgid "Add New Tag"
msgstr "Add New Tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Assigns the add new item text."
msgstr "Assigns the add new item text."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:234
msgid "Update Tag"
msgstr "Update Tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Assigns the update item text."
msgstr "Assigns the update item text."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:232
msgid "Update Item"
msgstr "Update Item"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:229
msgid "Update %s"
msgstr "Update %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:214
msgid "View Tag"
msgstr "View Tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "In the admin bar to view term during editing."
msgstr "In the admin bar to view term during editing."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:194
msgid "Edit Tag"
msgstr "Edit Tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "At the top of the editor screen when editing a term."
msgstr "At the top of the editor screen when editing a term."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:174
msgid "All Tags"
msgstr "All Tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "Assigns the all items text."
msgstr "Assigns the all items text."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:154
msgid "Assigns the menu name text."
msgstr "Assigns the menu name text."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:153
msgid "Menu Label"
msgstr "Menu Label"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:127
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "Active taxonomies are enabled and registered with WordPress."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:111
msgid "A descriptive summary of the taxonomy."
msgstr "A descriptive summary of the taxonomy."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:91
msgid "A descriptive summary of the term."
msgstr "A descriptive summary of the term."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:90
msgid "Term Description"
msgstr "Term Description"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:72
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "Single word, no spaces. Underscores and dashes allowed."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:71
msgid "Term Slug"
msgstr "Term Slug"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:52
msgid "The name of the default term."
msgstr "The name of the default term."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:51
msgid "Term Name"
msgstr "Term Name"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:37
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:36
msgid "Default Term"
msgstr "Default Term"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:24
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:23
msgid "Sort Terms"
msgstr "Sort Terms"

#: includes/admin/views/acf-post-type/list-empty.php:7
msgid "Add Post Type"
msgstr "Add Post Type"

#: includes/admin/views/acf-post-type/list-empty.php:6
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."

#: includes/admin/views/acf-post-type/list-empty.php:5
msgid "Add Your First Post Type"
msgstr "Add Your First Post Type"

#: includes/admin/views/acf-post-type/basic-settings.php:120
#: includes/admin/views/acf-taxonomy/basic-settings.php:119
msgid "I know what I'm doing, show me all the options."
msgstr "I know what I'm doing, show me all the options."

#: includes/admin/views/acf-post-type/basic-settings.php:119
#: includes/admin/views/acf-taxonomy/basic-settings.php:118
msgid "Advanced Configuration"
msgstr "Advanced Configuration"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Hierarchical post types can have descendants (like pages)."
msgstr "Hierarchical post types can have descendants (like pages)."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/advanced-settings.php:976
#: includes/admin/views/acf-taxonomy/basic-settings.php:105
msgid "Hierarchical"
msgstr "Hierarchical"

#: includes/admin/views/acf-post-type/basic-settings.php:91
msgid "Visible on the frontend and in the admin dashboard."
msgstr "Visible on the frontend and in the admin dashboard."

#: includes/admin/views/acf-post-type/basic-settings.php:90
#: includes/admin/views/acf-taxonomy/basic-settings.php:90
msgid "Public"
msgstr "Public"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:43
msgid "movie"
msgstr "movie"

#: includes/admin/views/acf-post-type/basic-settings.php:41
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr "Lower case letters, underscores and dashes only, Max 20 characters."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:25
msgid "Movie"
msgstr "Movie"

#: includes/admin/views/acf-post-type/basic-settings.php:23
#: includes/admin/views/acf-taxonomy/basic-settings.php:24
msgid "Singular Label"
msgstr "Singular Label"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:8
msgid "Movies"
msgstr "Movies"

#: includes/admin/views/acf-post-type/basic-settings.php:6
#: includes/admin/views/acf-taxonomy/basic-settings.php:7
msgid "Plural Label"
msgstr "Plural Label"

#: includes/admin/views/acf-post-type/advanced-settings.php:1250
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."

#: includes/admin/views/acf-post-type/advanced-settings.php:1249
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1128
msgid "Controller Class"
msgstr "Controller Class"

#: includes/admin/views/acf-post-type/advanced-settings.php:1231
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1109
msgid "The namespace part of the REST API URL."
msgstr "The namespace part of the REST API URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1230
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1108
msgid "Namespace Route"
msgstr "Namespace Route"

#: includes/admin/views/acf-post-type/advanced-settings.php:1212
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1090
msgid "The base URL for the post type REST API URLs."
msgstr "The base URL for the post type REST API URLs."

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1089
msgid "Base URL"
msgstr "Base URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1197
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"Exposes this post type in the REST API. Required to use the block editor."

#: includes/admin/views/acf-post-type/advanced-settings.php:1196
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1072
msgid "Show In REST API"
msgstr "Show In REST API"

#: includes/admin/views/acf-post-type/advanced-settings.php:1175
msgid "Customize the query variable name."
msgstr "Customise the query variable name."

#: includes/admin/views/acf-post-type/advanced-settings.php:1174
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
msgid "Query Variable"
msgstr "Query Variable"

#: includes/admin/views/acf-post-type/advanced-settings.php:1152
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1028
msgid "No Query Variable Support"
msgstr "No Query Variable Support"

#: includes/admin/views/acf-post-type/advanced-settings.php:1151
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1027
msgid "Custom Query Variable"
msgstr "Custom Query Variable"

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."

#: includes/admin/views/acf-post-type/advanced-settings.php:1147
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "Query Variable Support"
msgstr "Query Variable Support"

#: includes/admin/views/acf-post-type/advanced-settings.php:1122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:999
msgid "URLs for an item and items can be accessed with a query string."
msgstr "URLs for an item and items can be accessed with a query string."

#: includes/admin/views/acf-post-type/advanced-settings.php:1121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:998
msgid "Publicly Queryable"
msgstr "Publicly Queryable"

#: includes/admin/views/acf-post-type/advanced-settings.php:1100
msgid "Custom slug for the Archive URL."
msgstr "Custom slug for the Archive URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1099
msgid "Archive Slug"
msgstr "Archive Slug"

#: includes/admin/views/acf-post-type/advanced-settings.php:1086
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""
"Has an item archive that can be customised with an archive template file in "
"your theme."

#: includes/admin/views/acf-post-type/advanced-settings.php:1085
msgid "Archive"
msgstr "Archive"

#: includes/admin/views/acf-post-type/advanced-settings.php:1065
msgid "Pagination support for the items URLs such as the archives."
msgstr "Pagination support for the items URLs such as the archives."

#: includes/admin/views/acf-post-type/advanced-settings.php:1064
msgid "Pagination"
msgstr "Pagination"

#: includes/admin/views/acf-post-type/advanced-settings.php:1047
msgid "RSS feed URL for the post type items."
msgstr "RSS feed URL for the post type items."

#: includes/admin/views/acf-post-type/advanced-settings.php:1046
msgid "Feed URL"
msgstr "Feed URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1028
#: includes/admin/views/acf-taxonomy/advanced-settings.php:957
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."

#: includes/admin/views/acf-post-type/advanced-settings.php:1027
#: includes/admin/views/acf-taxonomy/advanced-settings.php:956
msgid "Front URL Prefix"
msgstr "Front URL Prefix"

#: includes/admin/views/acf-post-type/advanced-settings.php:1008
msgid "Customize the slug used in the URL."
msgstr "Customise the slug used in the URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1007
#: includes/admin/views/acf-taxonomy/advanced-settings.php:936
msgid "URL Slug"
msgstr "URL Slug"

#: includes/admin/views/acf-post-type/advanced-settings.php:991
msgid "Permalinks for this post type are disabled."
msgstr "Permalinks for this post type are disabled."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:990
#: includes/admin/views/acf-taxonomy/advanced-settings.php:919
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"

#: includes/admin/views/acf-post-type/advanced-settings.php:982
#: includes/admin/views/acf-taxonomy/advanced-settings.php:911
msgid "No Permalink (prevent URL rewriting)"
msgstr "No Permalink (prevent URL rewriting)"

#: includes/admin/views/acf-post-type/advanced-settings.php:981
#: includes/admin/views/acf-taxonomy/advanced-settings.php:910
msgid "Custom Permalink"
msgstr "Custom Permalink"

#: includes/admin/views/acf-post-type/advanced-settings.php:980
#: includes/admin/views/acf-post-type/advanced-settings.php:1150
#: includes/admin/views/acf-post-type/basic-settings.php:40
msgid "Post Type Key"
msgstr "Post Type Key"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:978
#: includes/admin/views/acf-post-type/advanced-settings.php:988
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"

#: includes/admin/views/acf-post-type/advanced-settings.php:976
#: includes/admin/views/acf-taxonomy/advanced-settings.php:906
msgid "Permalink Rewrite"
msgstr "Permalink Rewrite"

#: includes/admin/views/acf-post-type/advanced-settings.php:962
msgid "Delete items by a user when that user is deleted."
msgstr "Delete items by a user when that user is deleted."

#: includes/admin/views/acf-post-type/advanced-settings.php:961
msgid "Delete With User"
msgstr "Delete With User"

#: includes/admin/views/acf-post-type/advanced-settings.php:947
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr "Allow the post type to be exported from 'Tools' > 'Export'."

#: includes/admin/views/acf-post-type/advanced-settings.php:946
msgid "Can Export"
msgstr "Can Export"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
msgid "Optionally provide a plural to be used in capabilities."
msgstr "Optionally provide a plural to be used in capabilities."

#: includes/admin/views/acf-post-type/advanced-settings.php:914
msgid "Plural Capability Name"
msgstr "Plural Capability Name"

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Choose another post type to base the capabilities for this post type."
msgstr "Choose another post type to base the capabilities for this post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Singular Capability Name"
msgstr "Singular Capability Name"

#: includes/admin/views/acf-post-type/advanced-settings.php:881
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."

#: includes/admin/views/acf-post-type/advanced-settings.php:880
msgid "Rename Capabilities"
msgstr "Rename Capabilities"

#: includes/admin/views/acf-post-type/advanced-settings.php:866
msgid "Sets whether posts should be excluded from search results."
msgstr "Sets whether posts should be excluded from search results."

#: includes/admin/views/acf-post-type/advanced-settings.php:865
msgid "Exclude From Search"
msgstr "Exclude From Search"

#: includes/admin/views/acf-post-type/advanced-settings.php:852
#: includes/admin/views/acf-taxonomy/advanced-settings.php:850
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."

#: includes/admin/views/acf-post-type/advanced-settings.php:851
#: includes/admin/views/acf-taxonomy/advanced-settings.php:849
msgid "Appearance Menus Support"
msgstr "Appearance Menus Support"

#: includes/admin/views/acf-post-type/advanced-settings.php:833
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr "Appears as an item in the 'New' menu in the admin bar."

#: includes/admin/views/acf-post-type/advanced-settings.php:832
msgid "Show In Admin Bar"
msgstr "Show In Admin Bar"

#: includes/admin/views/acf-post-type/advanced-settings.php:801
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen."
msgstr ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen."

#: includes/admin/views/acf-post-type/advanced-settings.php:800
msgid "Custom Meta Box Callback"
msgstr "Custom Meta Box Callback"

#: includes/admin/views/acf-post-type/advanced-settings.php:780
msgid "Menu Icon"
msgstr "Menu Icon"

#: includes/admin/views/acf-post-type/advanced-settings.php:762
msgid "The position in the sidebar menu in the admin dashboard."
msgstr "The position in the sidebar menu in the admin dashboard."

#: includes/admin/views/acf-post-type/advanced-settings.php:761
msgid "Menu Position"
msgstr "Menu Position"

#: includes/admin/views/acf-post-type/advanced-settings.php:743
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."

#: includes/admin/views/acf-post-type/advanced-settings.php:742
msgid "Admin Menu Parent"
msgstr "Admin Menu Parent"

#. translators: %s = "dashicon class name", link to the WordPress dashicon
#. documentation.
#: includes/admin/views/acf-post-type/advanced-settings.php:730
msgid ""
"The icon used for the post type menu item in the admin dashboard. Can be a "
"URL or %s to use for the icon."
msgstr ""
"The icon used for the post type menu item in the admin dashboard. Can be a "
"URL or %s to use for the icon."

#: includes/admin/views/acf-post-type/advanced-settings.php:725
msgid "Dashicon class name"
msgstr "Dashicon class name"

#: includes/admin/views/acf-post-type/advanced-settings.php:714
#: includes/admin/views/acf-taxonomy/advanced-settings.php:730
msgid "Admin editor navigation in the sidebar menu."
msgstr "Admin editor navigation in the sidebar menu."

#: includes/admin/views/acf-post-type/advanced-settings.php:713
#: includes/admin/views/acf-taxonomy/advanced-settings.php:729
msgid "Show In Admin Menu"
msgstr "Show In Admin Menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:700
#: includes/admin/views/acf-taxonomy/advanced-settings.php:715
msgid "Items can be edited and managed in the admin dashboard."
msgstr "Items can be edited and managed in the admin dashboard."

#: includes/admin/views/acf-post-type/advanced-settings.php:699
#: includes/admin/views/acf-taxonomy/advanced-settings.php:714
msgid "Show In UI"
msgstr "Show In UI"

#: includes/admin/views/acf-post-type/advanced-settings.php:685
msgid "A link to a post."
msgstr "A link to a post."

#: includes/admin/views/acf-post-type/advanced-settings.php:684
msgid "Description for a navigation link block variation."
msgstr "Description for a navigation link block variation."

#: includes/admin/views/acf-post-type/advanced-settings.php:683
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "Item Link Description"
msgstr "Item Link Description"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:679
msgid "A link to a %s."
msgstr "A link to a %s."

#: includes/admin/views/acf-post-type/advanced-settings.php:664
msgid "Post Link"
msgstr "Post Link"

#: includes/admin/views/acf-post-type/advanced-settings.php:663
msgid "Title for a navigation link block variation."
msgstr "Title for a navigation link block variation."

#: includes/admin/views/acf-post-type/advanced-settings.php:662
#: includes/admin/views/acf-taxonomy/advanced-settings.php:677
msgid "Item Link"
msgstr "Item Link"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:659
#: includes/admin/views/acf-taxonomy/advanced-settings.php:674
msgid "%s Link"
msgstr "%s Link"

#: includes/admin/views/acf-post-type/advanced-settings.php:644
msgid "Post updated."
msgstr "Post updated."

#: includes/admin/views/acf-post-type/advanced-settings.php:643
msgid "In the editor notice after an item is updated."
msgstr "In the editor notice after an item is updated."

#: includes/admin/views/acf-post-type/advanced-settings.php:642
msgid "Item Updated"
msgstr "Item Updated"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:639
msgid "%s updated."
msgstr "%s updated."

#: includes/admin/views/acf-post-type/advanced-settings.php:624
msgid "Post scheduled."
msgstr "Post scheduled."

#: includes/admin/views/acf-post-type/advanced-settings.php:623
msgid "In the editor notice after scheduling an item."
msgstr "In the editor notice after scheduling an item."

#: includes/admin/views/acf-post-type/advanced-settings.php:622
msgid "Item Scheduled"
msgstr "Item Scheduled"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:619
msgid "%s scheduled."
msgstr "%s scheduled."

#: includes/admin/views/acf-post-type/advanced-settings.php:604
msgid "Post reverted to draft."
msgstr "Post reverted to draft."

#: includes/admin/views/acf-post-type/advanced-settings.php:603
msgid "In the editor notice after reverting an item to draft."
msgstr "In the editor notice after reverting an item to draft."

#: includes/admin/views/acf-post-type/advanced-settings.php:602
msgid "Item Reverted To Draft"
msgstr "Item Reverted To Draft"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:599
msgid "%s reverted to draft."
msgstr "%s reverted to draft."

#: includes/admin/views/acf-post-type/advanced-settings.php:584
msgid "Post published privately."
msgstr "Post published privately."

#: includes/admin/views/acf-post-type/advanced-settings.php:583
msgid "In the editor notice after publishing a private item."
msgstr "In the editor notice after publishing a private item."

#: includes/admin/views/acf-post-type/advanced-settings.php:582
msgid "Item Published Privately"
msgstr "Item Published Privately"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:579
msgid "%s published privately."
msgstr "%s published privately."

#: includes/admin/views/acf-post-type/advanced-settings.php:564
msgid "Post published."
msgstr "Post published."

#: includes/admin/views/acf-post-type/advanced-settings.php:563
msgid "In the editor notice after publishing an item."
msgstr "In the editor notice after publishing an item."

#: includes/admin/views/acf-post-type/advanced-settings.php:562
msgid "Item Published"
msgstr "Item Published"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:559
msgid "%s published."
msgstr "%s published."

#: includes/admin/views/acf-post-type/advanced-settings.php:544
msgid "Posts list"
msgstr "Posts list"

#: includes/admin/views/acf-post-type/advanced-settings.php:543
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""
"Used by screen readers for the items list on the post type list screen."

#: includes/admin/views/acf-post-type/advanced-settings.php:542
#: includes/admin/views/acf-taxonomy/advanced-settings.php:636
msgid "Items List"
msgstr "Items List"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:539
#: includes/admin/views/acf-taxonomy/advanced-settings.php:633
msgid "%s list"
msgstr "%s list"

#: includes/admin/views/acf-post-type/advanced-settings.php:524
msgid "Posts list navigation"
msgstr "Posts list navigation"

#: includes/admin/views/acf-post-type/advanced-settings.php:523
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."

#: includes/admin/views/acf-post-type/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:616
msgid "Items List Navigation"
msgstr "Items List Navigation"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:519
#: includes/admin/views/acf-taxonomy/advanced-settings.php:613
msgid "%s list navigation"
msgstr "%s list navigation"

#: includes/admin/views/acf-post-type/advanced-settings.php:503
msgid "Filter posts by date"
msgstr "Filter posts by date"

#: includes/admin/views/acf-post-type/advanced-settings.php:502
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."

#: includes/admin/views/acf-post-type/advanced-settings.php:501
msgid "Filter Items By Date"
msgstr "Filter Items By Date"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:497
msgid "Filter %s by date"
msgstr "Filter %s by date"

#: includes/admin/views/acf-post-type/advanced-settings.php:482
msgid "Filter posts list"
msgstr "Filter posts list"

#: includes/admin/views/acf-post-type/advanced-settings.php:481
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""
"Used by screen readers for the filter links heading on the post type list "
"screen."

#: includes/admin/views/acf-post-type/advanced-settings.php:480
msgid "Filter Items List"
msgstr "Filter Items List"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:476
msgid "Filter %s list"
msgstr "Filter %s list"

#: includes/admin/views/acf-post-type/advanced-settings.php:460
msgid "In the media modal showing all media uploaded to this item."
msgstr "In the media modal showing all media uploaded to this item."

#: includes/admin/views/acf-post-type/advanced-settings.php:459
msgid "Uploaded To This Item"
msgstr "Uploaded To This Item"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:455
msgid "Uploaded to this %s"
msgstr "Uploaded to this %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:440
msgid "Insert into post"
msgstr "Insert into post"

#: includes/admin/views/acf-post-type/advanced-settings.php:439
msgid "As the button label when adding media to content."
msgstr "As the button label when adding media to content."

#: includes/admin/views/acf-post-type/advanced-settings.php:438
msgid "Insert Into Media Button"
msgstr "Insert Into Media Button"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:434
msgid "Insert into %s"
msgstr "Insert into %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:419
msgid "Use as featured image"
msgstr "Use as featured image"

#: includes/admin/views/acf-post-type/advanced-settings.php:418
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""
"As the button label for selecting to use an image as the featured image."

#: includes/admin/views/acf-post-type/advanced-settings.php:417
msgid "Use Featured Image"
msgstr "Use Featured Image"

#: includes/admin/views/acf-post-type/advanced-settings.php:404
msgid "Remove featured image"
msgstr "Remove featured image"

#: includes/admin/views/acf-post-type/advanced-settings.php:403
msgid "As the button label when removing the featured image."
msgstr "As the button label when removing the featured image."

#: includes/admin/views/acf-post-type/advanced-settings.php:402
msgid "Remove Featured Image"
msgstr "Remove Featured Image"

#: includes/admin/views/acf-post-type/advanced-settings.php:389
msgid "Set featured image"
msgstr "Set featured image"

#: includes/admin/views/acf-post-type/advanced-settings.php:388
msgid "As the button label when setting the featured image."
msgstr "As the button label when setting the featured image."

#: includes/admin/views/acf-post-type/advanced-settings.php:387
msgid "Set Featured Image"
msgstr "Set Featured Image"

#: includes/admin/views/acf-post-type/advanced-settings.php:374
msgid "Featured image"
msgstr "Featured image"

#: includes/admin/views/acf-post-type/advanced-settings.php:373
msgid "In the editor used for the title of the featured image meta box."
msgstr "In the editor used for the title of the featured image meta box."

#: includes/admin/views/acf-post-type/advanced-settings.php:372
msgid "Featured Image Meta Box"
msgstr "Featured Image Meta Box"

#: includes/admin/views/acf-post-type/advanced-settings.php:359
msgid "Post Attributes"
msgstr "Post Attributes"

#: includes/admin/views/acf-post-type/advanced-settings.php:358
msgid "In the editor used for the title of the post attributes meta box."
msgstr "In the editor used for the title of the post attributes meta box."

#: includes/admin/views/acf-post-type/advanced-settings.php:357
msgid "Attributes Meta Box"
msgstr "Attributes Meta Box"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:354
msgid "%s Attributes"
msgstr "%s Attributes"

#: includes/admin/views/acf-post-type/advanced-settings.php:339
msgid "Post Archives"
msgstr "Post Archives"

#: includes/admin/views/acf-post-type/advanced-settings.php:338
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a post type with archives enabled. "
"Only appears when editing menus in 'Live Preview' mode and a custom archive "
"slug has been provided."

#: includes/admin/views/acf-post-type/advanced-settings.php:337
msgid "Archives Nav Menu"
msgstr "Archives Nav Menu"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:334
msgid "%s Archives"
msgstr "%s Archives"

#: includes/admin/views/acf-post-type/advanced-settings.php:319
msgid "No posts found in Trash"
msgstr "No posts found in the bin"

#: includes/admin/views/acf-post-type/advanced-settings.php:318
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""
"At the top of the post type list screen when there are no posts in the bin."

#: includes/admin/views/acf-post-type/advanced-settings.php:317
msgid "No Items Found in Trash"
msgstr "No Items Found in the bin"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:313
msgid "No %s found in Trash"
msgstr "No %s found in the bin"

#: includes/admin/views/acf-post-type/advanced-settings.php:298
msgid "No posts found"
msgstr "No posts found"

#: includes/admin/views/acf-post-type/advanced-settings.php:297
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""
"At the top of the post type list screen when there are no posts to display."

#: includes/admin/views/acf-post-type/advanced-settings.php:296
msgid "No Items Found"
msgstr "No Items Found"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:292
#: includes/admin/views/acf-taxonomy/advanced-settings.php:476
msgid "No %s found"
msgstr "No %s found"

#: includes/admin/views/acf-post-type/advanced-settings.php:277
msgid "Search Posts"
msgstr "Search Posts"

#: includes/admin/views/acf-post-type/advanced-settings.php:276
msgid "At the top of the items screen when searching for an item."
msgstr "At the top of the items screen when searching for an item."

#: includes/admin/views/acf-post-type/advanced-settings.php:275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:341
msgid "Search Items"
msgstr "Search Items"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:272
#: includes/admin/views/acf-taxonomy/advanced-settings.php:338
msgid "Search %s"
msgstr "Search %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:257
msgid "Parent Page:"
msgstr "Parent Page:"

#: includes/admin/views/acf-post-type/advanced-settings.php:256
msgid "For hierarchical types in the post type list screen."
msgstr "For hierarchical types in the post type list screen."

#: includes/admin/views/acf-post-type/advanced-settings.php:255
msgid "Parent Item Prefix"
msgstr "Parent Item Prefix"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:252
#: includes/admin/views/acf-taxonomy/advanced-settings.php:314
msgid "Parent %s:"
msgstr "Parent %s:"

#: includes/admin/views/acf-post-type/advanced-settings.php:237
msgid "New Post"
msgstr "New Post"

#: includes/admin/views/acf-post-type/advanced-settings.php:235
msgid "New Item"
msgstr "New Item"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:232
msgid "New %s"
msgstr "New %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:202
msgid "Add New Post"
msgstr "Add New Post"

#: includes/admin/views/acf-post-type/advanced-settings.php:201
msgid "At the top of the editor screen when adding a new item."
msgstr "At the top of the editor screen when adding a new item."

#: includes/admin/views/acf-post-type/advanced-settings.php:200
#: includes/admin/views/acf-taxonomy/advanced-settings.php:252
msgid "Add New Item"
msgstr "Add New Item"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:197
#: includes/admin/views/acf-taxonomy/advanced-settings.php:249
msgid "Add New %s"
msgstr "Add New %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:182
msgid "View Posts"
msgstr "View Posts"

#: includes/admin/views/acf-post-type/advanced-settings.php:181
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:180
msgid "View Items"
msgstr "View Items"

#: includes/admin/views/acf-post-type/advanced-settings.php:162
msgid "View Post"
msgstr "View Post"

#: includes/admin/views/acf-post-type/advanced-settings.php:161
msgid "In the admin bar to view item when editing it."
msgstr "In the admin bar to view item when editing it."

#: includes/admin/views/acf-post-type/advanced-settings.php:160
#: includes/admin/views/acf-taxonomy/advanced-settings.php:212
msgid "View Item"
msgstr "View Item"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:157
#: includes/admin/views/acf-post-type/advanced-settings.php:177
#: includes/admin/views/acf-taxonomy/advanced-settings.php:209
msgid "View %s"
msgstr "View %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:142
msgid "Edit Post"
msgstr "Edit Post"

#: includes/admin/views/acf-post-type/advanced-settings.php:141
msgid "At the top of the editor screen when editing an item."
msgstr "At the top of the editor screen when editing an item."

#: includes/admin/views/acf-post-type/advanced-settings.php:140
#: includes/admin/views/acf-taxonomy/advanced-settings.php:192
msgid "Edit Item"
msgstr "Edit Item"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:137
#: includes/admin/views/acf-taxonomy/advanced-settings.php:189
msgid "Edit %s"
msgstr "Edit %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:122
msgid "All Posts"
msgstr "All Posts"

#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-post-type/advanced-settings.php:216
#: includes/admin/views/acf-post-type/advanced-settings.php:236
msgid "In the post type submenu in the admin dashboard."
msgstr "In the post type submenu in the admin dashboard."

#: includes/admin/views/acf-post-type/advanced-settings.php:120
#: includes/admin/views/acf-taxonomy/advanced-settings.php:172
msgid "All Items"
msgstr "All Items"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:117
#: includes/admin/views/acf-taxonomy/advanced-settings.php:169
msgid "All %s"
msgstr "All %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:101
msgid "Admin menu name for the post type."
msgstr "Admin menu name for the post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:100
msgid "Menu Name"
msgstr "Menu Name"

#: includes/admin/views/acf-post-type/advanced-settings.php:86
#: includes/admin/views/acf-taxonomy/advanced-settings.php:138
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr "Regenerate all labels using the Singular and Plural labels"

#: includes/admin/views/acf-post-type/advanced-settings.php:84
#: includes/admin/views/acf-taxonomy/advanced-settings.php:136
msgid "Regenerate"
msgstr "Regenerate"

#: includes/admin/views/acf-post-type/advanced-settings.php:75
msgid "Active post types are enabled and registered with WordPress."
msgstr "Active post types are enabled and registered with WordPress."

#: includes/admin/views/acf-post-type/advanced-settings.php:59
msgid "A descriptive summary of the post type."
msgstr "A descriptive summary of the post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:44
msgid "Add Custom"
msgstr "Add Custom"

#: includes/admin/views/acf-post-type/advanced-settings.php:38
msgid "Enable various features in the content editor."
msgstr "Enable various features in the content editor."

#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Post Formats"
msgstr "Post Formats"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
msgid "Editor"
msgstr "Editor"

#: includes/admin/views/acf-post-type/advanced-settings.php:20
msgid "Trackbacks"
msgstr "Trackbacks"

#: includes/admin/views/acf-post-type/basic-settings.php:71
msgid "Select existing taxonomies to classify items of the post type."
msgstr "Select existing taxonomies to classify items of the post type."

#: includes/admin/views/acf-field-group/field.php:141
msgid "Browse Fields"
msgstr "Browse Fields"

#: includes/admin/tools/class-acf-admin-tool-import.php:292
msgid "Nothing to import"
msgstr "Nothing to import"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ". The Custom Post Type UI plugin can be deactivated."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:278
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "Imported %d item from Custom Post Type UI -"
msgstr[1] "Imported %d items from Custom Post Type UI -"

#: includes/admin/tools/class-acf-admin-tool-import.php:262
msgid "Failed to import taxonomies."
msgstr "Failed to import taxonomies."

#: includes/admin/tools/class-acf-admin-tool-import.php:244
msgid "Failed to import post types."
msgstr "Failed to import post types."

#: includes/admin/tools/class-acf-admin-tool-import.php:233
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr "Nothing from Custom Post Type UI plugin selected for import."

#: includes/admin/tools/class-acf-admin-tool-import.php:209
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "Imported 1 item"
msgstr[1] "Imported %s items"

#: includes/admin/tools/class-acf-admin-tool-import.php:122
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."

#: includes/admin/tools/class-acf-admin-tool-import.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:127
msgid "Import from Custom Post Type UI"
msgstr "Import from Custom Post Type UI"

#: includes/admin/tools/class-acf-admin-tool-export.php:390
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's functions."
"php file or include it within an external file, then deactivate or delete "
"the items from the ACF admin."
msgstr ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's functions."
"php file or include it within an external file, then deactivate or delete "
"the items from the ACF admin."

#: includes/admin/tools/class-acf-admin-tool-export.php:389
msgid "Export - Generate PHP"
msgstr "Export - Generate PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:362
msgid "Export"
msgstr "Export"

#: includes/admin/tools/class-acf-admin-tool-export.php:276
msgid "Select Taxonomies"
msgstr "Select Taxonomies"

#: includes/admin/tools/class-acf-admin-tool-export.php:254
msgid "Select Post Types"
msgstr "Select Post Types"

#: includes/admin/tools/class-acf-admin-tool-export.php:167
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "Exported 1 item."
msgstr[1] "Exported %s items."

#: includes/admin/post-types/admin-taxonomy.php:124
#: assets/build/js/acf-internal-post-type.js:144
#: assets/build/js/acf-internal-post-type.js:204
msgid "Category"
msgstr "Category"

#: includes/admin/post-types/admin-taxonomy.php:122
#: assets/build/js/acf-internal-post-type.js:141
#: assets/build/js/acf-internal-post-type.js:201
msgid "Tag"
msgstr "Tag"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:103
msgid "Create new post type"
msgstr "Create new post type"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "%s taxonomy created"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "%s taxonomy updated"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "Taxonomy draft updated."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "Taxonomy scheduled for."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "Taxonomy submitted."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "Taxonomy saved."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "Taxonomy deleted."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "Taxonomy updated."

#: includes/admin/post-types/admin-taxonomies.php:344
#: includes/admin/post-types/admin-taxonomy.php:152
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "Taxonomy synchronised."
msgstr[1] "%s taxonomies synchronised."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "Taxonomy duplicated."
msgstr[1] "%s taxonomies duplicated."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "Taxonomy deactivated."
msgstr[1] "%s taxonomies deactivated."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:305
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "Taxonomy activated."
msgstr[1] "%s taxonomies activated."

#: includes/admin/post-types/admin-taxonomies.php:106
msgid "Terms"
msgstr "Terms"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:319
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "Post type synchronised."
msgstr[1] "%s post types synchronised."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:312
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "Post type duplicated."
msgstr[1] "%s post types duplicated."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:305
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "Post type deactivated."
msgstr[1] "%s post types deactivated."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:298
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "Post type activated."
msgstr[1] "%s post types activated."

#: includes/admin/post-types/admin-post-types.php:79
#: includes/admin/post-types/admin-taxonomies.php:104
#: includes/admin/tools/class-acf-admin-tool-import.php:82
#: includes/admin/views/acf-taxonomy/basic-settings.php:66
#: includes/post-types/class-acf-post-type.php:90
msgid "Post Types"
msgstr "Post Types"

#: includes/admin/post-types/admin-post-type.php:159
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Advanced Settings"
msgstr "Advanced Settings"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:158
msgid "Basic Settings"
msgstr "Basic Settings"

#: includes/admin/post-types/admin-post-type.php:152
#: includes/admin/post-types/admin-post-types.php:337
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."

#: includes/admin/post-types/admin-post-type.php:125
#: assets/build/js/acf-internal-post-type.js:138
#: assets/build/js/acf-internal-post-type.js:198
msgid "Pages"
msgstr "Pages"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:102
msgid "Create new taxonomy"
msgstr "Create new taxonomy"

#: includes/admin/post-types/admin-post-type.php:101
#: includes/admin/post-types/admin-taxonomy.php:101
msgid "Link existing field groups"
msgstr "Link existing field groups"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:82
msgid "%s post type created"
msgstr "%s post type created"

#. translators: %s post type name
#. translators: %s taxonomy name
#: includes/admin/post-types/admin-post-type.php:78
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "Add fields to %s"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "%s post type updated"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "Post type draft updated."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "Post type scheduled for."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "Post type submitted."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "Post type saved."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "Post type updated."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "Post type deleted."

#: includes/admin/post-types/admin-field-group.php:120
#: assets/build/js/acf-field-group.js:1146
#: assets/build/js/acf-field-group.js:1366
msgid "Type to search..."
msgstr "Type to search..."

#: includes/admin/post-types/admin-field-group.php:105
#: assets/build/js/acf-field-group.js:1172
#: assets/build/js/acf-field-group.js:2295
#: assets/build/js/acf-field-group.js:1414
#: assets/build/js/acf-field-group.js:2689
msgid "PRO Only"
msgstr "PRO Only"

#: includes/admin/post-types/admin-field-group.php:97
#: assets/build/js/acf-internal-post-type.js:270
#: assets/build/js/acf-internal-post-type.js:365
msgid "Field groups linked successfully."
msgstr "Field groups linked successfully."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:194
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."

#: includes/admin/admin.php:48
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:337
msgid "taxonomy"
msgstr "taxonomy"

#: includes/admin/admin-internal-post-type.php:337
msgid "post type"
msgstr "post type"

#. translators: %1$s - name of newly created post. %2$s - either "post type" or
#. "taxonomy".
#: includes/admin/admin-internal-post-type.php:335
msgid "Link %1$s %2$s to field groups"
msgstr "Link %1$s %2$s to field groups"

#: includes/admin/admin-internal-post-type.php:328
msgid "Done"
msgstr "Done"

#: includes/admin/admin-internal-post-type.php:315
msgid "Field group(s)"
msgstr "Field group(s)"

#: includes/admin/admin-internal-post-type.php:314
msgid "Select one or many field groups..."
msgstr "Select one or many field groups..."

#: includes/admin/admin-internal-post-type.php:313
msgid "Please select the field groups to link."
msgstr "Please select the field groups to link."

#: includes/admin/admin-internal-post-type.php:277
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "Field group linked successfully."
msgstr[1] "Field groups linked successfully."

#: includes/admin/admin-internal-post-type-list.php:255
#: includes/admin/post-types/admin-post-types.php:338
#: includes/admin/post-types/admin-taxonomies.php:345
msgctxt "post status"
msgid "Registration Failed"
msgstr "Registration Failed"

#: includes/admin/admin-internal-post-type-list.php:254
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."

#: includes/acf-internal-post-type-functions.php:482
#: includes/acf-internal-post-type-functions.php:510
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:481
msgid "Permissions"
msgstr "Permissions"

#: includes/acf-internal-post-type-functions.php:480
#: includes/acf-internal-post-type-functions.php:509
msgid "URLs"
msgstr "URLs"

#: includes/acf-internal-post-type-functions.php:479
#: includes/acf-internal-post-type-functions.php:508
msgid "Visibility"
msgstr "Visibility"

#: includes/acf-internal-post-type-functions.php:478
#: includes/acf-internal-post-type-functions.php:507
msgid "Labels"
msgstr "Labels"

#: includes/admin/post-types/admin-field-group.php:243
msgid "Field Settings Tabs"
msgstr "Field Settings Tabs"

#. Author URI of the plugin
msgid ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:867
msgid "[ACF shortcode value disabled for preview]"
msgstr "[ACF shortcode value disabled for preview]"

#: includes/admin/admin-internal-post-type.php:287
#: includes/admin/post-types/admin-field-group.php:545
msgid "Close Modal"
msgstr "Close Modal"

#: includes/admin/post-types/admin-field-group.php:96
#: assets/build/js/acf-field-group.js:1661
#: assets/build/js/acf-field-group.js:1980
msgid "Field moved to other group"
msgstr "Field moved to other group"

#: includes/admin/post-types/admin-field-group.php:95
#: assets/build/js/acf.js:1440 assets/build/js/acf.js:1521
msgid "Close modal"
msgstr "Close modal"

#: includes/fields/class-acf-field-tab.php:125
msgid "Start a new group of tabs at this tab."
msgstr "Start a new group of tabs at this tab."

#: includes/fields/class-acf-field-tab.php:124
msgid "New Tab Group"
msgstr "New Tab Group"

#: includes/fields/class-acf-field-select.php:457
#: includes/fields/class-acf-field-true_false.php:200
msgid "Use a stylized checkbox using select2"
msgstr "Use a stylised checkbox using select2"

#: includes/fields/class-acf-field-radio.php:260
msgid "Save Other Choice"
msgstr "Save Other Choice"

#: includes/fields/class-acf-field-radio.php:249
msgid "Allow Other Choice"
msgstr "Allow Other Choice"

#: includes/fields/class-acf-field-checkbox.php:450
msgid "Add Toggle All"
msgstr "Add Toggle All"

#: includes/fields/class-acf-field-checkbox.php:409
msgid "Save Custom Values"
msgstr "Save Custom Values"

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Allow Custom Values"
msgstr "Allow Custom Values"

#: includes/fields/class-acf-field-checkbox.php:148
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr "Checkbox custom values cannot be empty. Uncheck any empty values."

#: includes/admin/views/global/navigation.php:140
msgid "Updates"
msgstr "Updates"

#: includes/admin/views/global/navigation.php:83
msgid "Advanced Custom Fields logo"
msgstr "Advanced Custom Fields logo"

#: includes/admin/views/global/form-top.php:57
msgid "Save Changes"
msgstr "Save Changes"

#: includes/admin/views/global/form-top.php:44
msgid "Field Group Title"
msgstr "Field Group Title"

#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Add title"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:20
#: includes/admin/views/acf-post-type/list-empty.php:12
#: includes/admin/views/acf-taxonomy/list-empty.php:12
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."

#: includes/admin/views/acf-field-group/list-empty.php:15
msgid "Add Field Group"
msgstr "Add Field Group"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:10
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."

#: includes/admin/views/acf-field-group/list-empty.php:5
msgid "Add Your First Field Group"
msgstr "Add Your First Field Group"

#: includes/admin/views/acf-field-group/pro-features.php:16
msgid "Upgrade Now"
msgstr "Upgrade Now"

#: includes/admin/views/acf-field-group/pro-features.php:11
msgid "Options Pages"
msgstr "Options Pages"

#: includes/admin/views/acf-field-group/pro-features.php:10
msgid "ACF Blocks"
msgstr "ACF Blocks"

#: includes/admin/views/acf-field-group/pro-features.php:8
msgid "Gallery Field"
msgstr "Gallery Field"

#: includes/admin/views/acf-field-group/pro-features.php:7
msgid "Flexible Content Field"
msgstr "Flexible Content Field"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "Repeater Field"
msgstr "Repeater Field"

#: includes/admin/views/acf-field-group/pro-features.php:4
#: includes/admin/views/global/navigation.php:125
msgid "Unlock Extra Features with ACF PRO"
msgstr "Unlock Extra Features with ACF PRO"

#: includes/admin/views/acf-field-group/options.php:252
msgid "Delete Field Group"
msgstr "Delete Field Group"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:246
msgid "Created on %1$s at %2$s"
msgstr "Created on %1$s at %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Group Settings"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Location Rules"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:61
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."

#: includes/admin/views/acf-field-group/fields.php:54
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."

#: includes/admin/views/acf-field-group/fields.php:53
msgid "Add Your First Field"
msgstr "Add Your First Field"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:32
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:22
#: includes/admin/views/acf-field-group/fields.php:56
#: includes/admin/views/acf-field-group/fields.php:92
#: includes/admin/views/global/form-top.php:53
msgid "Add Field"
msgstr "Add Field"

#: includes/acf-field-group-functions.php:496 includes/fields.php:410
msgid "Presentation"
msgstr "Presentation"

#: includes/fields.php:409
msgid "Validation"
msgstr "Validation"

#: includes/acf-internal-post-type-functions.php:477
#: includes/acf-internal-post-type-functions.php:506 includes/fields.php:408
msgid "General"
msgstr "General"

#: includes/admin/tools/class-acf-admin-tool-import.php:70
msgid "Import JSON"
msgstr "Import JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:370
msgid "Export As JSON"
msgstr "Export As JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:345
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "Field group deactivated."
msgstr[1] "%s field groups deactivated."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:338
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "Field group activated."
msgstr[1] "%s field groups activated."

#: includes/admin/admin-internal-post-type-list.php:430
#: includes/admin/admin-internal-post-type-list.php:461
msgid "Deactivate"
msgstr "Deactivate"

#: includes/admin/admin-internal-post-type-list.php:430
msgid "Deactivate this item"
msgstr "Deactivate this item"

#: includes/admin/admin-internal-post-type-list.php:426
#: includes/admin/admin-internal-post-type-list.php:460
msgid "Activate"
msgstr "Activate"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Activate this item"
msgstr "Activate this item"

#: includes/admin/post-types/admin-field-group.php:92
#: assets/build/js/acf-field-group.js:2741
#: assets/build/js/acf-field-group.js:3180
msgid "Move field group to trash?"
msgstr "Move field group to trash?"

#: acf.php:485 includes/admin/admin-internal-post-type-list.php:242
#: includes/admin/post-types/admin-field-group.php:271
#: includes/admin/post-types/admin-post-type.php:292
#: includes/admin/post-types/admin-taxonomy.php:292
msgctxt "post status"
msgid "Inactive"
msgstr "Inactive"

#. Author of the plugin
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:543
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."

#: acf.php:541
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."

#: includes/acf-value-functions.php:374
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
"\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialised. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
"\">Learn how to fix this</a>."

#: includes/fields/class-acf-field-user.php:540
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s must have a user with the %2$s role."
msgstr[1] "%1$s must have a user with one of the following roles: %2$s"

#: includes/fields/class-acf-field-user.php:531
msgid "%1$s must have a valid user ID."
msgstr "%1$s must have a valid user ID."

#: includes/fields/class-acf-field-user.php:369
msgid "Invalid request."
msgstr "Invalid request."

#: includes/fields/class-acf-field-select.php:690
msgid "%1$s is not one of %2$s"
msgstr "%1$s is not one of %2$s"

#: includes/fields/class-acf-field-post_object.php:698
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s must have term %2$s."
msgstr[1] "%1$s must have one of the following terms: %2$s"

#: includes/fields/class-acf-field-post_object.php:682
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s must be of post type %2$s."
msgstr[1] "%1$s must be of one of the following post types: %2$s"

#: includes/fields/class-acf-field-post_object.php:673
msgid "%1$s must have a valid post ID."
msgstr "%1$s must have a valid post ID."

#: includes/fields/class-acf-field-file.php:475
msgid "%s requires a valid attachment ID."
msgstr "%s requires a valid attachment ID."

#: includes/admin/views/acf-field-group/options.php:218
msgid "Show in REST API"
msgstr "Show in REST API"

#: includes/fields/class-acf-field-color_picker.php:170
msgid "Enable Transparency"
msgstr "Enable Transparency"

#: includes/fields/class-acf-field-color_picker.php:189
msgid "RGBA Array"
msgstr "RGBA Array"

#: includes/fields/class-acf-field-color_picker.php:99
msgid "RGBA String"
msgstr "RGBA String"

#: includes/fields/class-acf-field-color_picker.php:98
#: includes/fields/class-acf-field-color_picker.php:188
msgid "Hex String"
msgstr "Hex String"

#: includes/admin/views/browse-fields-modal.php:65
msgid "Upgrade to PRO"
msgstr "Upgrade to PRO"

#: includes/admin/post-types/admin-field-group.php:271
#: includes/admin/post-types/admin-post-type.php:292
#: includes/admin/post-types/admin-taxonomy.php:292
msgctxt "post status"
msgid "Active"
msgstr "Active"

#: includes/fields/class-acf-field-email.php:181
msgid "'%s' is not a valid email address"
msgstr "'%s' is not a valid email address"

#: includes/fields/class-acf-field-color_picker.php:77
msgid "Color value"
msgstr "Colour value"

#: includes/fields/class-acf-field-color_picker.php:75
msgid "Select default color"
msgstr "Select default colour"

#: includes/fields/class-acf-field-color_picker.php:73
msgid "Clear color"
msgstr "Clear colour"

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr "Blocks"

#: includes/acf-wp-functions.php:83
msgid "Options"
msgstr "Options"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr "Users"

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr "Menu items"

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr "Widgets"

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr "Attachments"

#: includes/acf-wp-functions.php:54
#: includes/admin/post-types/admin-post-types.php:104
#: includes/admin/post-types/admin-taxonomies.php:79
#: includes/admin/tools/class-acf-admin-tool-import.php:93
#: includes/admin/views/acf-post-type/basic-settings.php:70
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Taxonomies"

#: includes/acf-wp-functions.php:41
#: includes/admin/post-types/admin-post-type.php:123
#: includes/admin/post-types/admin-post-types.php:106
#: includes/admin/views/acf-post-type/advanced-settings.php:102
#: assets/build/js/acf-internal-post-type.js:135
#: assets/build/js/acf-internal-post-type.js:195
msgid "Posts"
msgstr "Posts"

#: includes/ajax/class-acf-ajax-local-json-diff.php:76
msgid "Last updated: %s"
msgstr "Last updated: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:70
msgid "Sorry, this post is unavailable for diff comparison."
msgstr "Sorry, this post is unavailable for diff comparison."

#: includes/ajax/class-acf-ajax-local-json-diff.php:42
msgid "Invalid field group parameter(s)."
msgstr "Invalid field group parameter(s)."

#: includes/admin/admin-internal-post-type-list.php:396
msgid "Awaiting save"
msgstr "Awaiting save"

#: includes/admin/admin-internal-post-type-list.php:393
msgid "Saved"
msgstr "Saved"

#: includes/admin/admin-internal-post-type-list.php:389
#: includes/admin/tools/class-acf-admin-tool-import.php:49
msgid "Import"
msgstr "Import"

#: includes/admin/admin-internal-post-type-list.php:385
msgid "Review changes"
msgstr "Review changes"

#: includes/admin/admin-internal-post-type-list.php:361
msgid "Located in: %s"
msgstr "Located in: %s"

#: includes/admin/admin-internal-post-type-list.php:358
msgid "Located in plugin: %s"
msgstr "Located in plugin: %s"

#: includes/admin/admin-internal-post-type-list.php:355
msgid "Located in theme: %s"
msgstr "Located in theme: %s"

#: includes/admin/post-types/admin-field-groups.php:239
msgid "Various"
msgstr "Various"

#: includes/admin/admin-internal-post-type-list.php:210
#: includes/admin/admin-internal-post-type-list.php:468
msgid "Sync changes"
msgstr "Sync changes"

#: includes/admin/admin-internal-post-type-list.php:209
msgid "Loading diff"
msgstr "Loading diff"

#: includes/admin/admin-internal-post-type-list.php:208
msgid "Review local JSON changes"
msgstr "Review local JSON changes"

#: includes/admin/admin.php:169
msgid "Visit website"
msgstr "Visit website"

#: includes/admin/admin.php:168
msgid "View details"
msgstr "View details"

#: includes/admin/admin.php:167
msgid "Version %s"
msgstr "Version %s"

#: includes/admin/admin.php:166
msgid "Information"
msgstr "Information"

#: includes/admin/admin.php:157
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."

#: includes/admin/admin.php:153
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."

#: includes/admin/admin.php:149
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."

#: includes/admin/admin.php:146
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"

#: includes/admin/admin.php:143 includes/admin/admin.php:145
msgid "Help & Support"
msgstr "Help & Support"

#: includes/admin/admin.php:134
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."

#: includes/admin/admin.php:131
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarise "
"yourself with the plugin's philosophy and best practises."

#: includes/admin/admin.php:129
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customise WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."

#: includes/admin/admin.php:126 includes/admin/admin.php:128
msgid "Overview"
msgstr "Overview"

#: includes/locations.php:36
msgid "Location type \"%s\" is already registered."
msgstr "Location type \"%s\" is already registered."

#: includes/locations.php:25
msgid "Class \"%s\" does not exist."
msgstr "Class \"%s\" does not exist."

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Invalid nonce."

#: includes/fields/class-acf-field-user.php:364
msgid "Error loading field."
msgstr "Error loading field."

#: assets/build/js/acf-input.js:2750 assets/build/js/acf-input.js:2819
#: assets/build/js/acf-input.js:2926 assets/build/js/acf-input.js:3000
msgid "Location not found: %s"
msgstr "Location not found: %s"

#: includes/forms/form-user.php:353
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Error</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "User Role"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Comment"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Post Format"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Menu Item"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Post Status"

#: includes/acf-wp-functions.php:71
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menus"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Menu Locations"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Post Taxonomy"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Child Page (has parent)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Parent Page (has children)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Top Level Page (no parent)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Posts Page"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Front Page"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Page Type"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Viewing back end"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Viewing front end"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Logged in"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Current User"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Page Template"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Register"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Add / Edit"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "User Form"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Page Parent"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super Admin"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Current User Role"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Default Template"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Post Template"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Post Category"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "All %s formats"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Attachment"

#: includes/validation.php:364
msgid "%s value is required"
msgstr "%s value is required"

#: includes/admin/views/acf-field-group/conditional-logic.php:59
msgid "Show this field if"
msgstr "Show this field if"

#: includes/admin/views/acf-field-group/conditional-logic.php:26
#: includes/admin/views/acf-field-group/field.php:105 includes/fields.php:411
msgid "Conditional Logic"
msgstr "Conditional Logic"

#: includes/admin/admin.php:234
#: includes/admin/views/acf-field-group/conditional-logic.php:156
#: includes/admin/views/acf-field-group/location-rule.php:91
msgid "and"
msgstr "and"

#: includes/admin/post-types/admin-field-groups.php:101
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:110
msgid "Local JSON"
msgstr "Local JSON"

#: includes/admin/views/acf-field-group/pro-features.php:9
msgid "Clone Field"
msgstr "Clone Field"

#: includes/admin/views/upgrade/notice.php:30
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Please also check all premium add-ons (%s) are updated to the latest version."

#: includes/admin/views/upgrade/notice.php:28
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"This version contains improvements to your database and requires an upgrade."

#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Thank you for updating to %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:27
msgid "Database Upgrade Required"
msgstr "Database Upgrade Required"

#: includes/admin/post-types/admin-field-group.php:132
#: includes/admin/views/upgrade/notice.php:18
msgid "Options Page"
msgstr "Options Page"

#: includes/admin/views/upgrade/notice.php:15 includes/fields.php:460
msgid "Gallery"
msgstr "Gallery"

#: includes/admin/views/upgrade/notice.php:12 includes/fields.php:450
msgid "Flexible Content"
msgstr "Flexible Content"

#: includes/admin/views/upgrade/notice.php:9 includes/fields.php:470
msgid "Repeater"
msgstr "Repeater"

#: includes/admin/views/tools/tools.php:24
msgid "Back to all tools"
msgstr "Back to all tools"

#: includes/admin/views/acf-field-group/options.php:180
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"

#: includes/admin/views/acf-field-group/options.php:180
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Select</b> items to <b>hide</b> them from the edit screen."

#: includes/admin/views/acf-field-group/options.php:179
msgid "Hide on screen"
msgstr "Hide on screen"

#: includes/admin/views/acf-field-group/options.php:171
msgid "Send Trackbacks"
msgstr "Send Trackbacks"

#: includes/admin/post-types/admin-taxonomy.php:123
#: includes/admin/views/acf-field-group/options.php:170
#: includes/admin/views/acf-taxonomy/advanced-settings.php:155
#: assets/build/js/acf-internal-post-type.js:142
#: assets/build/js/acf-internal-post-type.js:202
msgid "Tags"
msgstr "Tags"

#: includes/admin/post-types/admin-taxonomy.php:125
#: includes/admin/views/acf-field-group/options.php:169
#: assets/build/js/acf-internal-post-type.js:145
#: assets/build/js/acf-internal-post-type.js:205
msgid "Categories"
msgstr "Categories"

#: includes/admin/views/acf-field-group/options.php:167
#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Page Attributes"
msgstr "Page Attributes"

#: includes/admin/views/acf-field-group/options.php:166
msgid "Format"
msgstr "Format"

#: includes/admin/views/acf-field-group/options.php:165
#: includes/admin/views/acf-post-type/advanced-settings.php:18
msgid "Author"
msgstr "Author"

#: includes/admin/views/acf-field-group/options.php:164
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/acf-field-group/options.php:163
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Revisions"
msgstr "Revisions"

#: includes/acf-wp-functions.php:63
#: includes/admin/views/acf-field-group/options.php:162
#: includes/admin/views/acf-post-type/advanced-settings.php:19
msgid "Comments"
msgstr "Comments"

#: includes/admin/views/acf-field-group/options.php:161
msgid "Discussion"
msgstr "Discussion"

#: includes/admin/views/acf-field-group/options.php:159
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Excerpt"
msgstr "Excerpt"

#: includes/admin/views/acf-field-group/options.php:158
msgid "Content Editor"
msgstr "Content Editor"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Permalink"
msgstr "Permalink"

#: includes/admin/views/acf-field-group/options.php:235
msgid "Shown in field group list"
msgstr "Shown in field group list"

#: includes/admin/views/acf-field-group/options.php:142
msgid "Field groups with a lower order will appear first"
msgstr "Field groups with a lower order will appear first"

#: includes/admin/views/acf-field-group/options.php:141
msgid "Order No."
msgstr "Order No."

#: includes/admin/views/acf-field-group/options.php:132
msgid "Below fields"
msgstr "Below fields"

#: includes/admin/views/acf-field-group/options.php:131
msgid "Below labels"
msgstr "Below labels"

#: includes/admin/views/acf-field-group/options.php:124
msgid "Instruction placement"
msgstr "Instruction placement"

#: includes/admin/views/acf-field-group/options.php:107
msgid "Label placement"
msgstr "Label placement"

#: includes/admin/views/acf-field-group/options.php:97
msgid "Side"
msgstr "Side"

#: includes/admin/views/acf-field-group/options.php:96
msgid "Normal (after content)"
msgstr "Normal (after content)"

#: includes/admin/views/acf-field-group/options.php:95
msgid "High (after title)"
msgstr "High (after title)"

#: includes/admin/views/acf-field-group/options.php:88
msgid "Position"
msgstr "Position"

#: includes/admin/views/acf-field-group/options.php:79
msgid "Seamless (no metabox)"
msgstr "Seamless (no metabox)"

#: includes/admin/views/acf-field-group/options.php:78
msgid "Standard (WP metabox)"
msgstr "Standard (WP metabox)"

#: includes/admin/views/acf-field-group/options.php:71
msgid "Style"
msgstr "Style"

#: includes/admin/views/acf-field-group/fields.php:44
msgid "Type"
msgstr "Type"

#: includes/admin/post-types/admin-field-groups.php:95
#: includes/admin/post-types/admin-post-types.php:103
#: includes/admin/post-types/admin-taxonomies.php:103
#: includes/admin/views/acf-field-group/fields.php:43
msgid "Key"
msgstr "Key"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:37
msgid "Order"
msgstr "Order"

#: includes/admin/views/acf-field-group/field.php:318
msgid "Close Field"
msgstr "Close Field"

#: includes/admin/views/acf-field-group/field.php:249
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:233
msgid "class"
msgstr "class"

#: includes/admin/views/acf-field-group/field.php:275
msgid "width"
msgstr "width"

#: includes/admin/views/acf-field-group/field.php:269
msgid "Wrapper Attributes"
msgstr "Wrapper Attributes"

#: includes/admin/views/acf-field-group/field.php:192
msgid "Required"
msgstr "Required"

#: includes/admin/views/acf-field-group/field.php:217
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instructions for authors. Shown when submitting data"

#: includes/admin/views/acf-field-group/field.php:216
msgid "Instructions"
msgstr "Instructions"

#: includes/admin/views/acf-field-group/field.php:125
msgid "Field Type"
msgstr "Field Type"

#: includes/admin/views/acf-field-group/field.php:166
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Single word, no spaces. Underscores and dashes allowed"

#: includes/admin/views/acf-field-group/field.php:165
msgid "Field Name"
msgstr "Field Name"

#: includes/admin/views/acf-field-group/field.php:153
msgid "This is the name which will appear on the EDIT page"
msgstr "This is the name which will appear on the EDIT page"

#: includes/admin/views/acf-field-group/field.php:152
#: includes/admin/views/browse-fields-modal.php:59
msgid "Field Label"
msgstr "Field Label"

#: includes/admin/views/acf-field-group/field.php:77
msgid "Delete"
msgstr "Delete"

#: includes/admin/views/acf-field-group/field.php:77
msgid "Delete field"
msgstr "Delete field"

#: includes/admin/views/acf-field-group/field.php:75
msgid "Move"
msgstr "Move"

#: includes/admin/views/acf-field-group/field.php:75
msgid "Move field to another group"
msgstr "Move field to another group"

#: includes/admin/views/acf-field-group/field.php:73
msgid "Duplicate field"
msgstr "Duplicate field"

#: includes/admin/views/acf-field-group/field.php:69
#: includes/admin/views/acf-field-group/field.php:72
msgid "Edit field"
msgstr "Edit field"

#: includes/admin/views/acf-field-group/field.php:65
msgid "Drag to reorder"
msgstr "Drag to reorder"

#: includes/admin/post-types/admin-field-group.php:103
#: includes/admin/views/acf-field-group/location-group.php:3
#: assets/build/js/acf-field-group.js:2323
#: assets/build/js/acf-field-group.js:2725
msgid "Show this field group if"
msgstr "Show this field group if"

#: includes/admin/views/upgrade/upgrade.php:94
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "No updates available."

#: includes/admin/views/upgrade/upgrade.php:33
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Database upgrade complete. <a href=\"%s\">See what's new</a>"

#: includes/admin/views/upgrade/upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Reading upgrade tasks..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:65
msgid "Upgrade failed."
msgstr "Upgrade failed."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Upgrade complete."

#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:31
msgid "Upgrading data to version %s"
msgstr "Upgrading data to version %s"

#: includes/admin/views/upgrade/network.php:121
#: includes/admin/views/upgrade/notice.php:44
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"

#: includes/admin/views/upgrade/network.php:117
msgid "Please select at least one site to upgrade."
msgstr "Please select at least one site to upgrade."

#: includes/admin/views/upgrade/network.php:97
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"

#: includes/admin/views/upgrade/network.php:80
msgid "Site is up to date"
msgstr "Site is up to date"

#: includes/admin/views/upgrade/network.php:78
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Site requires database upgrade from %1$s to %2$s"

#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Site"
msgstr "Site"

#: includes/admin/views/upgrade/network.php:26
#: includes/admin/views/upgrade/network.php:27
#: includes/admin/views/upgrade/network.php:96
msgid "Upgrade Sites"
msgstr "Upgrade Sites"

#: includes/admin/views/upgrade/network.php:26
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:171
#: includes/admin/views/acf-field-group/locations.php:38
msgid "Add rule group"
msgstr "Add rule group"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Rules"

#: includes/admin/tools/class-acf-admin-tool-export.php:482
msgid "Copied"
msgstr "Copied"

#: includes/admin/tools/class-acf-admin-tool-export.php:458
msgid "Copy to clipboard"
msgstr "Copy to clipboard"

#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."

#: includes/admin/tools/class-acf-admin-tool-export.php:233
msgid "Select Field Groups"
msgstr "Select Field Groups"

#: includes/admin/tools/class-acf-admin-tool-export.php:96
#: includes/admin/tools/class-acf-admin-tool-export.php:131
msgid "No field groups selected"
msgstr "No field groups selected"

#: includes/admin/tools/class-acf-admin-tool-export.php:39
#: includes/admin/tools/class-acf-admin-tool-export.php:371
#: includes/admin/tools/class-acf-admin-tool-export.php:399
msgid "Generate PHP"
msgstr "Generate PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:35
msgid "Export Field Groups"
msgstr "Export Field Groups"

#: includes/admin/tools/class-acf-admin-tool-import.php:177
msgid "Import file empty"
msgstr "Import file empty"

#: includes/admin/tools/class-acf-admin-tool-import.php:168
msgid "Incorrect file type"
msgstr "Incorrect file type"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Error uploading file. Please try again"
msgstr "Error uploading file. Please try again"

#: includes/admin/tools/class-acf-admin-tool-import.php:50
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."

#: includes/admin/tools/class-acf-admin-tool-import.php:27
msgid "Import Field Groups"
msgstr "Import Field Groups"

#: includes/admin/admin-internal-post-type-list.php:384
msgid "Sync"
msgstr "Sync"

#: includes/admin/admin-internal-post-type-list.php:841
msgid "Select %s"
msgstr "Select %s"

#: includes/admin/admin-internal-post-type-list.php:419
#: includes/admin/admin-internal-post-type-list.php:457
#: includes/admin/views/acf-field-group/field.php:73
msgid "Duplicate"
msgstr "Duplicate"

#: includes/admin/admin-internal-post-type-list.php:419
msgid "Duplicate this item"
msgstr "Duplicate this item"

#: includes/admin/views/acf-post-type/advanced-settings.php:37
msgid "Supports"
msgstr "Supports"

#: includes/admin/views/browse-fields-modal.php:92
msgid "Documentation"
msgstr "Documentation"

#: includes/admin/post-types/admin-field-groups.php:94
#: includes/admin/post-types/admin-post-types.php:102
#: includes/admin/post-types/admin-taxonomies.php:102
#: includes/admin/views/acf-field-group/options.php:234
#: includes/admin/views/acf-post-type/advanced-settings.php:58
#: includes/admin/views/acf-taxonomy/advanced-settings.php:110
#: includes/admin/views/upgrade/network.php:38
#: includes/admin/views/upgrade/network.php:49
msgid "Description"
msgstr "Description"

#: includes/admin/admin-internal-post-type-list.php:381
#: includes/admin/admin-internal-post-type-list.php:730
msgid "Sync available"
msgstr "Sync available"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:359
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Field group synchronised."
msgstr[1] "%s field groups synchronised."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:352
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Field group duplicated."
msgstr[1] "%s field groups duplicated."

#: includes/admin/admin-internal-post-type-list.php:131
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Active <span class=\"count\">(%s)</span>"
msgstr[1] "Active <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:254
msgid "Review sites & upgrade"
msgstr "Review sites & upgrade"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:93
#: includes/admin/admin-upgrade.php:94 includes/admin/admin-upgrade.php:230
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/upgrade.php:26
msgid "Upgrade Database"
msgstr "Upgrade Database"

#: includes/admin/views/acf-field-group/options.php:160
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Custom Fields"
msgstr "Custom Fields"

#: includes/admin/post-types/admin-field-group.php:590
msgid "Move Field"
msgstr "Move Field"

#: includes/admin/post-types/admin-field-group.php:579
#: includes/admin/post-types/admin-field-group.php:583
msgid "Please select the destination for this field"
msgstr "Please select the destination for this field"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:541
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "The %1$s field can now be found in the %2$s field group"

#: includes/admin/post-types/admin-field-group.php:538
msgid "Move Complete."
msgstr "Move Complete."

#: includes/admin/views/acf-field-group/field.php:35
#: includes/admin/views/acf-field-group/options.php:202
#: includes/admin/views/acf-post-type/advanced-settings.php:74
#: includes/admin/views/acf-taxonomy/advanced-settings.php:126
msgid "Active"
msgstr "Active"

#: includes/admin/post-types/admin-field-group.php:240
msgid "Field Keys"
msgstr "Field Keys"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/admin/tools/class-acf-admin-tool-export.php:320
msgid "Settings"
msgstr "Settings"

#: includes/admin/post-types/admin-field-groups.php:96
msgid "Location"
msgstr "Location"

#: includes/admin/post-types/admin-field-group.php:104
#: assets/build/js/acf-input.js:983 assets/build/js/acf-input.js:1075
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:101
#: includes/class-acf-internal-post-type.php:729
#: includes/post-types/class-acf-field-group.php:345
#: assets/build/js/acf-field-group.js:1501
#: assets/build/js/acf-field-group.js:1808
msgid "copy"
msgstr "copy"

#: includes/admin/post-types/admin-field-group.php:100
#: assets/build/js/acf-field-group.js:623
#: assets/build/js/acf-field-group.js:778
msgid "(this field)"
msgstr "(this field)"

#: includes/admin/post-types/admin-field-group.php:98
#: assets/build/js/acf-input.js:918 assets/build/js/acf-input.js:943
#: assets/build/js/acf-input.js:1002 assets/build/js/acf-input.js:1030
msgid "Checked"
msgstr "Checked"

#: includes/admin/post-types/admin-field-group.php:94
#: assets/build/js/acf-field-group.js:1606
#: assets/build/js/acf-field-group.js:1920
msgid "Move Custom Field"
msgstr "Move Custom Field"

#: includes/admin/post-types/admin-field-group.php:93
#: assets/build/js/acf-field-group.js:649
#: assets/build/js/acf-field-group.js:804
msgid "No toggle fields available"
msgstr "No toggle fields available"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Field group title is required"
msgstr "Field group title is required"

#: includes/admin/post-types/admin-field-group.php:90
#: assets/build/js/acf-field-group.js:1595
#: assets/build/js/acf-field-group.js:1906
msgid "This field cannot be moved until its changes have been saved"
msgstr "This field cannot be moved until its changes have been saved"

#: includes/admin/post-types/admin-field-group.php:89
#: assets/build/js/acf-field-group.js:1405
#: assets/build/js/acf-field-group.js:1703
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "The string \"field_\" may not be used at the start of a field name"

#: includes/admin/post-types/admin-field-group.php:71
msgid "Field group draft updated."
msgstr "Field group draft updated."

#: includes/admin/post-types/admin-field-group.php:70
msgid "Field group scheduled for."
msgstr "Field group scheduled for."

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group submitted."
msgstr "Field group submitted."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group saved."
msgstr "Field group saved."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group published."
msgstr "Field group published."

#: includes/admin/post-types/admin-field-group.php:64
msgid "Field group deleted."
msgstr "Field group deleted."

#: includes/admin/post-types/admin-field-group.php:62
#: includes/admin/post-types/admin-field-group.php:63
#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group updated."
msgstr "Field group updated."

#: includes/admin/admin-tools.php:118
#: includes/admin/views/global/navigation.php:138
#: includes/admin/views/tools/tools.php:21
msgid "Tools"
msgstr "Tools"

#: includes/locations/abstract-acf-location.php:106
msgid "is not equal to"
msgstr "is not equal to"

#: includes/locations/abstract-acf-location.php:105
msgid "is equal to"
msgstr "is equal to"

#: includes/locations.php:102
msgid "Forms"
msgstr "Forms"

#: includes/admin/post-types/admin-post-type.php:124 includes/locations.php:100
#: includes/locations/class-acf-location-page.php:22
#: assets/build/js/acf-internal-post-type.js:137
#: assets/build/js/acf-internal-post-type.js:197
msgid "Page"
msgstr "Page"

#: includes/admin/post-types/admin-post-type.php:122 includes/locations.php:99
#: includes/locations/class-acf-location-post.php:22
#: assets/build/js/acf-internal-post-type.js:134
#: assets/build/js/acf-internal-post-type.js:194
msgid "Post"
msgstr "Post"

#: includes/fields.php:354
msgid "Relational"
msgstr "Relational"

#: includes/fields.php:353
msgid "Choice"
msgstr "Choice"

#: includes/fields.php:351
msgid "Basic"
msgstr "Basic"

#: includes/fields.php:320
msgid "Unknown"
msgstr "Unknown"

#: includes/fields.php:320
msgid "Field type does not exist"
msgstr "Field type does not exist"

#: includes/forms/form-front.php:236
msgid "Spam Detected"
msgstr "Spam Detected"

#: includes/forms/form-front.php:107
msgid "Post updated"
msgstr "Post updated"

#: includes/forms/form-front.php:106
msgid "Update"
msgstr "Update"

#: includes/forms/form-front.php:57
msgid "Validate Email"
msgstr "Validate Email"

#: includes/fields.php:352 includes/forms/form-front.php:49
msgid "Content"
msgstr "Content"

#: includes/admin/views/acf-post-type/advanced-settings.php:17
#: includes/forms/form-front.php:40
msgid "Title"
msgstr "Title"

#: includes/assets.php:372 includes/forms/form-comment.php:160
#: assets/build/js/acf-input.js:7348 assets/build/js/acf-input.js:7934
msgid "Edit field group"
msgstr "Edit field group"

#: includes/admin/post-types/admin-field-group.php:117
#: assets/build/js/acf-input.js:1125 assets/build/js/acf-input.js:1230
msgid "Selection is less than"
msgstr "Selection is less than"

#: includes/admin/post-types/admin-field-group.php:116
#: assets/build/js/acf-input.js:1106 assets/build/js/acf-input.js:1202
msgid "Selection is greater than"
msgstr "Selection is greater than"

#: includes/admin/post-types/admin-field-group.php:115
#: assets/build/js/acf-input.js:1075 assets/build/js/acf-input.js:1170
msgid "Value is less than"
msgstr "Value is less than"

#: includes/admin/post-types/admin-field-group.php:114
#: assets/build/js/acf-input.js:1045 assets/build/js/acf-input.js:1139
msgid "Value is greater than"
msgstr "Value is greater than"

#: includes/admin/post-types/admin-field-group.php:113
#: assets/build/js/acf-input.js:888 assets/build/js/acf-input.js:960
msgid "Value contains"
msgstr "Value contains"

#: includes/admin/post-types/admin-field-group.php:112
#: assets/build/js/acf-input.js:862 assets/build/js/acf-input.js:926
msgid "Value matches pattern"
msgstr "Value matches pattern"

#: includes/admin/post-types/admin-field-group.php:111
#: assets/build/js/acf-input.js:840 assets/build/js/acf-input.js:1023
#: assets/build/js/acf-input.js:903 assets/build/js/acf-input.js:1116
msgid "Value is not equal to"
msgstr "Value is not equal to"

#: includes/admin/post-types/admin-field-group.php:110
#: assets/build/js/acf-input.js:810 assets/build/js/acf-input.js:964
#: assets/build/js/acf-input.js:864 assets/build/js/acf-input.js:1053
msgid "Value is equal to"
msgstr "Value is equal to"

#: includes/admin/post-types/admin-field-group.php:109
#: assets/build/js/acf-input.js:788 assets/build/js/acf-input.js:841
msgid "Has no value"
msgstr "Has no value"

#: includes/admin/post-types/admin-field-group.php:108
#: assets/build/js/acf-input.js:758 assets/build/js/acf-input.js:783
msgid "Has any value"
msgstr "Has any value"

#: includes/admin/admin-internal-post-type.php:327
#: includes/admin/views/browse-fields-modal.php:62 includes/assets.php:353
#: assets/build/js/acf.js:1567 assets/build/js/acf.js:1662
msgid "Cancel"
msgstr "Cancel"

#: includes/assets.php:349 assets/build/js/acf.js:1741
#: assets/build/js/acf.js:1859
msgid "Are you sure?"
msgstr "Are you sure?"

#: includes/assets.php:369 assets/build/js/acf-input.js:9409
#: assets/build/js/acf-input.js:10260
msgid "%d fields require attention"
msgstr "%d fields require attention"

#: includes/assets.php:368 assets/build/js/acf-input.js:9407
#: assets/build/js/acf-input.js:10256
msgid "1 field requires attention"
msgstr "1 field requires attention"

#: includes/assets.php:367 includes/validation.php:286
#: includes/validation.php:296 assets/build/js/acf-input.js:9402
#: assets/build/js/acf-input.js:10251
msgid "Validation failed"
msgstr "Validation failed"

#: includes/assets.php:366 assets/build/js/acf-input.js:9565
#: assets/build/js/acf-input.js:10434
msgid "Validation successful"
msgstr "Validation successful"

#: includes/media.php:54 assets/build/js/acf-input.js:7176
#: assets/build/js/acf-input.js:7738
msgid "Restricted"
msgstr "Restricted"

#: includes/media.php:53 assets/build/js/acf-input.js:6991
#: assets/build/js/acf-input.js:7502
msgid "Collapse Details"
msgstr "Collapse Details"

#: includes/media.php:52 assets/build/js/acf-input.js:6991
#: assets/build/js/acf-input.js:7499
msgid "Expand Details"
msgstr "Expand Details"

#: includes/admin/views/acf-post-type/advanced-settings.php:461
#: includes/media.php:51 assets/build/js/acf-input.js:6858
#: assets/build/js/acf-input.js:7347
msgid "Uploaded to this post"
msgstr "Uploaded to this post"

#: includes/media.php:50 assets/build/js/acf-input.js:6897
#: assets/build/js/acf-input.js:7386
msgctxt "verb"
msgid "Update"
msgstr "Update"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Edit"

#: includes/assets.php:363 assets/build/js/acf-input.js:9179
#: assets/build/js/acf-input.js:10022
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "The changes you made will be lost if you navigate away from this page"

#: includes/api/api-helpers.php:3482
msgid "File type must be %s."
msgstr "File type must be %s."

#: includes/admin/post-types/admin-field-group.php:102
#: includes/admin/views/acf-field-group/conditional-logic.php:59
#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:36
#: includes/api/api-helpers.php:3478 assets/build/js/acf-field-group.js:771
#: assets/build/js/acf-field-group.js:2361
#: assets/build/js/acf-field-group.js:933
#: assets/build/js/acf-field-group.js:2769
msgid "or"
msgstr "or"

#: includes/api/api-helpers.php:3451
msgid "File size must not exceed %s."
msgstr "File size must not exceed %s."

#: includes/api/api-helpers.php:3446
msgid "File size must be at least %s."
msgstr "File size must be at least %s."

#: includes/api/api-helpers.php:3431
msgid "Image height must not exceed %dpx."
msgstr "Image height must not exceed %dpx."

#: includes/api/api-helpers.php:3426
msgid "Image height must be at least %dpx."
msgstr "Image height must be at least %dpx."

#: includes/api/api-helpers.php:3412
msgid "Image width must not exceed %dpx."
msgstr "Image width must not exceed %dpx."

#: includes/api/api-helpers.php:3407
msgid "Image width must be at least %dpx."
msgstr "Image width must be at least %dpx."

#: includes/api/api-helpers.php:1653 includes/api/api-term.php:147
msgid "(no title)"
msgstr "(no title)"

#: includes/api/api-helpers.php:944
msgid "Full Size"
msgstr "Full Size"

#: includes/api/api-helpers.php:903
msgid "Large"
msgstr "Large"

#: includes/api/api-helpers.php:902
msgid "Medium"
msgstr "Medium"

#: includes/api/api-helpers.php:901
msgid "Thumbnail"
msgstr "Thumbnail"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:99
#: assets/build/js/acf-field-group.js:1077
#: assets/build/js/acf-field-group.js:1260
msgid "(no label)"
msgstr "(no label)"

#: includes/fields/class-acf-field-textarea.php:145
msgid "Sets the textarea height"
msgstr "Sets the textarea height"

#: includes/fields/class-acf-field-textarea.php:144
msgid "Rows"
msgstr "Rows"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Text Area"

#: includes/fields/class-acf-field-checkbox.php:451
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Prepend an extra checkbox to toggle all choices"

#: includes/fields/class-acf-field-checkbox.php:413
msgid "Save 'custom' values to the field's choices"
msgstr "Save 'custom' values to the field's choices"

#: includes/fields/class-acf-field-checkbox.php:402
msgid "Allow 'custom' values to be added"
msgstr "Allow 'custom' values to be added"

#: includes/fields/class-acf-field-checkbox.php:38
msgid "Add new choice"
msgstr "Add new choice"

#: includes/fields/class-acf-field-checkbox.php:174
msgid "Toggle All"
msgstr "Toggle All"

#: includes/fields/class-acf-field-page_link.php:506
msgid "Allow Archives URLs"
msgstr "Allow Archive URLs"

#: includes/fields/class-acf-field-page_link.php:179
msgid "Archives"
msgstr "Archives"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Page Link"

#: includes/fields/class-acf-field-taxonomy.php:948
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Add"

#: includes/admin/views/acf-field-group/fields.php:42
#: includes/fields/class-acf-field-taxonomy.php:913
msgid "Name"
msgstr "Name"

#: includes/fields/class-acf-field-taxonomy.php:897
msgid "%s added"
msgstr "%s added"

#: includes/fields/class-acf-field-taxonomy.php:861
msgid "%s already exists"
msgstr "%s already exists"

#: includes/fields/class-acf-field-taxonomy.php:849
msgid "User unable to add new %s"
msgstr "User unable to add new %s"

#: includes/fields/class-acf-field-taxonomy.php:759
msgid "Term ID"
msgstr "Term ID"

#: includes/fields/class-acf-field-taxonomy.php:758
msgid "Term Object"
msgstr "Term Object"

#: includes/fields/class-acf-field-taxonomy.php:743
msgid "Load value from posts terms"
msgstr "Load value from posts terms"

#: includes/fields/class-acf-field-taxonomy.php:742
msgid "Load Terms"
msgstr "Load Terms"

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "Connect selected terms to the post"
msgstr "Connect selected terms to the post"

#: includes/fields/class-acf-field-taxonomy.php:731
msgid "Save Terms"
msgstr "Save Terms"

#: includes/fields/class-acf-field-taxonomy.php:721
msgid "Allow new terms to be created whilst editing"
msgstr "Allow new terms to be created whilst editing"

#: includes/fields/class-acf-field-taxonomy.php:720
msgid "Create Terms"
msgstr "Create Terms"

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Radio Buttons"
msgstr "Radio Buttons"

#: includes/fields/class-acf-field-taxonomy.php:778
msgid "Single Value"
msgstr "Single Value"

#: includes/fields/class-acf-field-taxonomy.php:776
msgid "Multi Select"
msgstr "Multi Select"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Checkbox"
msgstr "Checkbox"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Multiple Values"
msgstr "Multiple Values"

#: includes/fields/class-acf-field-taxonomy.php:769
msgid "Select the appearance of this field"
msgstr "Select the appearance of this field"

#: includes/fields/class-acf-field-taxonomy.php:768
msgid "Appearance"
msgstr "Appearance"

#: includes/fields/class-acf-field-taxonomy.php:710
msgid "Select the taxonomy to be displayed"
msgstr "Select the taxonomy to be displayed"

#: includes/fields/class-acf-field-taxonomy.php:671
msgctxt "No Terms"
msgid "No %s"
msgstr "No %s"

#: includes/fields/class-acf-field-number.php:266
msgid "Value must be equal to or lower than %d"
msgstr "Value must be equal to or lower than %d"

#: includes/fields/class-acf-field-number.php:259
msgid "Value must be equal to or higher than %d"
msgstr "Value must be equal to or higher than %d"

#: includes/fields/class-acf-field-number.php:244
msgid "Value must be a number"
msgstr "Value must be a number"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Number"

#: includes/fields/class-acf-field-radio.php:264
msgid "Save 'other' values to the field's choices"
msgstr "Save 'other' values to the field's choices"

#: includes/fields/class-acf-field-radio.php:253
msgid "Add 'other' choice to allow for custom values"
msgstr "Add 'other' choice to allow for custom values"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Radio Button"

#: includes/fields/class-acf-field-accordion.php:107
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."

#: includes/fields/class-acf-field-accordion.php:96
msgid "Allow this accordion to open without closing others."
msgstr "Allow this accordion to open without closing others."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Multi-expand"
msgstr "Multi-expand"

#: includes/fields/class-acf-field-accordion.php:85
msgid "Display this accordion as open on page load."
msgstr "Display this accordion as open on page load."

#: includes/fields/class-acf-field-accordion.php:84
msgid "Open"
msgstr "Open"

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "Accordion"

#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-file.php:279
msgid "Restrict which files can be uploaded"
msgstr "Restrict which files can be uploaded"

#: includes/fields/class-acf-field-file.php:220
msgid "File ID"
msgstr "File ID"

#: includes/fields/class-acf-field-file.php:219
msgid "File URL"
msgstr "File URL"

#: includes/fields/class-acf-field-file.php:218
msgid "File Array"
msgstr "File Array"

#: includes/fields/class-acf-field-file.php:186
msgid "Add File"
msgstr "Add File"

#: includes/admin/tools/class-acf-admin-tool-import.php:156
#: includes/fields/class-acf-field-file.php:186
msgid "No file selected"
msgstr "No file selected"

#: includes/fields/class-acf-field-file.php:150
msgid "File name"
msgstr "File name"

#: includes/fields/class-acf-field-file.php:63
#: assets/build/js/acf-input.js:2474 assets/build/js/acf-input.js:2625
msgid "Update File"
msgstr "Update File"

#: includes/fields/class-acf-field-file.php:62
#: assets/build/js/acf-input.js:2473 assets/build/js/acf-input.js:2624
msgid "Edit File"
msgstr "Edit File"

#: includes/admin/tools/class-acf-admin-tool-import.php:58
#: includes/fields/class-acf-field-file.php:61
#: assets/build/js/acf-input.js:2447 assets/build/js/acf-input.js:2597
msgid "Select File"
msgstr "Select File"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "File"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Password"

#: includes/fields/class-acf-field-select.php:398
msgid "Specify the value returned"
msgstr "Specify the value returned"

#: includes/fields/class-acf-field-select.php:467
msgid "Use AJAX to lazy load choices?"
msgstr "Use AJAX to lazy load choices?"

#: includes/fields/class-acf-field-checkbox.php:362
#: includes/fields/class-acf-field-select.php:387
msgid "Enter each default value on a new line"
msgstr "Enter each default value on a new line"

#: includes/fields/class-acf-field-select.php:258 includes/media.php:48
#: assets/build/js/acf-input.js:6756 assets/build/js/acf-input.js:7232
msgctxt "verb"
msgid "Select"
msgstr "Select"

#: includes/fields/class-acf-field-select.php:121
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Loading failed"

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Searching&hellip;"

#: includes/fields/class-acf-field-select.php:119
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Loading more results&hellip;"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "You can only select %d items"

#: includes/fields/class-acf-field-select.php:117
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "You can only select 1 item"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Please delete %d characters"

#: includes/fields/class-acf-field-select.php:115
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Please delete 1 character"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Please enter %d or more characters"

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Please enter 1 or more characters"

#: includes/fields/class-acf-field-select.php:112
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "No matches found"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d results are available, use up and down arrow keys to navigate."

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "One result is available, press enter to select it."

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:780
msgctxt "noun"
msgid "Select"
msgstr "Select"

#: includes/fields/class-acf-field-user.php:77
msgid "User ID"
msgstr "User ID"

#: includes/fields/class-acf-field-user.php:76
msgid "User Object"
msgstr "User Object"

#: includes/fields/class-acf-field-user.php:75
msgid "User Array"
msgstr "User Array"

#: includes/fields/class-acf-field-user.php:63
msgid "All user roles"
msgstr "All user roles"

#: includes/fields/class-acf-field-user.php:55
msgid "Filter by role"
msgstr "Filter by role"

#: includes/fields/class-acf-field-user.php:20 includes/locations.php:101
msgid "User"
msgstr "User"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Separator"

#: includes/fields/class-acf-field-color_picker.php:76
msgid "Select Color"
msgstr "Select Colour"

#: includes/admin/post-types/admin-post-type.php:126
#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/fields/class-acf-field-color_picker.php:74
#: assets/build/js/acf-internal-post-type.js:54
#: assets/build/js/acf-internal-post-type.js:59
msgid "Default"
msgstr "Default"

#: includes/admin/views/acf-post-type/advanced-settings.php:85
#: includes/admin/views/acf-taxonomy/advanced-settings.php:137
#: includes/fields/class-acf-field-color_picker.php:72
msgid "Clear"
msgstr "Clear"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Colour Picker"

#: includes/fields/class-acf-field-date_time_picker.php:88
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:87
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:83
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Select"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Done"

#: includes/fields/class-acf-field-date_time_picker.php:79
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Now"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Time Zone"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsecond"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Millisecond"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Second"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minute"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hour"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Time"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Choose Time"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Date Time Picker"

#: includes/fields/class-acf-field-accordion.php:106
msgid "Endpoint"
msgstr "Endpoint"

#: includes/admin/views/acf-field-group/options.php:115
#: includes/fields/class-acf-field-tab.php:115
msgid "Left aligned"
msgstr "Left aligned"

#: includes/admin/views/acf-field-group/options.php:114
#: includes/fields/class-acf-field-tab.php:114
msgid "Top aligned"
msgstr "Top aligned"

#: includes/fields/class-acf-field-tab.php:110
msgid "Placement"
msgstr "Placement"

#: includes/fields/class-acf-field-tab.php:26
msgid "Tab"
msgstr "Tab"

#: includes/fields/class-acf-field-url.php:162
msgid "Value must be a valid URL"
msgstr "Value must be a valid URL"

#: includes/fields/class-acf-field-link.php:177
msgid "Link URL"
msgstr "Link URL"

#: includes/fields/class-acf-field-link.php:176
msgid "Link Array"
msgstr "Link Array"

#: includes/fields/class-acf-field-link.php:145
msgid "Opens in a new window/tab"
msgstr "Opens in a new window/tab"

#: includes/fields/class-acf-field-link.php:140
msgid "Select Link"
msgstr "Select Link"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Link"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-number.php:188
#: includes/fields/class-acf-field-range.php:217
msgid "Step Size"
msgstr "Step Size"

#: includes/fields/class-acf-field-number.php:158
#: includes/fields/class-acf-field-range.php:195
msgid "Maximum Value"
msgstr "Maximum Value"

#: includes/fields/class-acf-field-number.php:148
#: includes/fields/class-acf-field-range.php:184
msgid "Minimum Value"
msgstr "Minimum Value"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Range"

#: includes/fields/class-acf-field-button-group.php:175
#: includes/fields/class-acf-field-checkbox.php:379
#: includes/fields/class-acf-field-radio.php:220
#: includes/fields/class-acf-field-select.php:405
msgid "Both (Array)"
msgstr "Both (Array)"

#: includes/admin/views/acf-field-group/fields.php:41
#: includes/fields/class-acf-field-button-group.php:174
#: includes/fields/class-acf-field-checkbox.php:378
#: includes/fields/class-acf-field-radio.php:219
#: includes/fields/class-acf-field-select.php:404
msgid "Label"
msgstr "Label"

#: includes/fields/class-acf-field-button-group.php:173
#: includes/fields/class-acf-field-checkbox.php:377
#: includes/fields/class-acf-field-radio.php:218
#: includes/fields/class-acf-field-select.php:403
msgid "Value"
msgstr "Value"

#: includes/fields/class-acf-field-button-group.php:222
#: includes/fields/class-acf-field-checkbox.php:441
#: includes/fields/class-acf-field-radio.php:292
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:221
#: includes/fields/class-acf-field-checkbox.php:442
#: includes/fields/class-acf-field-radio.php:293
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-checkbox.php:352
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-select.php:376
msgid "red : Red"
msgstr "red : Red"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-checkbox.php:352
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-select.php:376
msgid "For more control, you may specify both a value and label like this:"
msgstr "For more control, you may specify both a value and label like this:"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-checkbox.php:352
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-select.php:376
msgid "Enter each choice on a new line."
msgstr "Enter each choice on a new line."

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:351
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-select.php:375
msgid "Choices"
msgstr "Choices"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Button Group"

#: includes/fields/class-acf-field-page_link.php:517
#: includes/fields/class-acf-field-post_object.php:433
#: includes/fields/class-acf-field-select.php:413
#: includes/fields/class-acf-field-user.php:86
msgid "Select multiple values?"
msgstr "Select multiple values?"

#: includes/fields/class-acf-field-button-group.php:194
#: includes/fields/class-acf-field-page_link.php:538
#: includes/fields/class-acf-field-post_object.php:455
#: includes/fields/class-acf-field-radio.php:238
#: includes/fields/class-acf-field-select.php:435
#: includes/fields/class-acf-field-taxonomy.php:789
#: includes/fields/class-acf-field-user.php:107
msgid "Allow Null?"
msgstr "Allow Null?"

#: includes/fields/class-acf-field-page_link.php:263
#: includes/fields/class-acf-field-post_object.php:264
#: includes/fields/class-acf-field-taxonomy.php:935
msgid "Parent"
msgstr "Parent"

#: includes/fields/class-acf-field-wysiwyg.php:397
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE will not be initialised until field is clicked"

#: includes/fields/class-acf-field-wysiwyg.php:396
msgid "Delay initialization?"
msgstr "Delay initialisation?"

#: includes/fields/class-acf-field-wysiwyg.php:385
msgid "Show Media Upload Buttons?"
msgstr "Show Media Upload Buttons?"

#: includes/fields/class-acf-field-wysiwyg.php:369
msgid "Toolbar"
msgstr "Toolbar"

#: includes/fields/class-acf-field-wysiwyg.php:361
msgid "Text Only"
msgstr "Text Only"

#: includes/fields/class-acf-field-wysiwyg.php:360
msgid "Visual Only"
msgstr "Visual Only"

#: includes/fields/class-acf-field-wysiwyg.php:359
msgid "Visual & Text"
msgstr "Visual and Text"

#: includes/fields/class-acf-field-wysiwyg.php:354
msgid "Tabs"
msgstr "Tabs"

#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Click to initialize TinyMCE"
msgstr "Click to initialise TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:286
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-wysiwyg.php:285
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-text.php:183
#: includes/fields/class-acf-field-textarea.php:236
msgid "Value must not exceed %d characters"
msgstr "Value must not exceed %d characters"

#: includes/fields/class-acf-field-text.php:118
#: includes/fields/class-acf-field-textarea.php:124
msgid "Leave blank for no limit"
msgstr "Leave blank for no limit"

#: includes/fields/class-acf-field-text.php:117
#: includes/fields/class-acf-field-textarea.php:123
msgid "Character Limit"
msgstr "Character Limit"

#: includes/fields/class-acf-field-email.php:158
#: includes/fields/class-acf-field-number.php:209
#: includes/fields/class-acf-field-password.php:105
#: includes/fields/class-acf-field-range.php:239
#: includes/fields/class-acf-field-text.php:158
msgid "Appears after the input"
msgstr "Appears after the input"

#: includes/fields/class-acf-field-email.php:157
#: includes/fields/class-acf-field-number.php:208
#: includes/fields/class-acf-field-password.php:104
#: includes/fields/class-acf-field-range.php:238
#: includes/fields/class-acf-field-text.php:157
msgid "Append"
msgstr "Append"

#: includes/fields/class-acf-field-email.php:148
#: includes/fields/class-acf-field-number.php:199
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:229
#: includes/fields/class-acf-field-text.php:148
msgid "Appears before the input"
msgstr "Appears before the input"

#: includes/fields/class-acf-field-email.php:147
#: includes/fields/class-acf-field-number.php:198
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:147
msgid "Prepend"
msgstr "Prepend"

#: includes/fields/class-acf-field-email.php:138
#: includes/fields/class-acf-field-number.php:179
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-text.php:138
#: includes/fields/class-acf-field-textarea.php:156
#: includes/fields/class-acf-field-url.php:122
msgid "Appears within the input"
msgstr "Appears within the input"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:178
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-text.php:137
#: includes/fields/class-acf-field-textarea.php:155
#: includes/fields/class-acf-field-url.php:121
msgid "Placeholder Text"
msgstr "Placeholder Text"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:129
#: includes/fields/class-acf-field-radio.php:203
#: includes/fields/class-acf-field-range.php:165
#: includes/fields/class-acf-field-text.php:98
#: includes/fields/class-acf-field-textarea.php:104
#: includes/fields/class-acf-field-url.php:102
#: includes/fields/class-acf-field-wysiwyg.php:319
msgid "Appears when creating a new post"
msgstr "Appears when creating a new post"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-relationship.php:789
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s requires at least %2$s selection"
msgstr[1] "%1$s requires at least %2$s selections"

#: includes/fields/class-acf-field-post_object.php:424
#: includes/fields/class-acf-field-relationship.php:651
msgid "Post ID"
msgstr "Post ID"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:423
#: includes/fields/class-acf-field-relationship.php:650
msgid "Post Object"
msgstr "Post Object"

#: includes/fields/class-acf-field-relationship.php:683
msgid "Maximum posts"
msgstr "Maximum posts"

#: includes/fields/class-acf-field-relationship.php:673
msgid "Minimum posts"
msgstr "Minimum posts"

#: includes/admin/views/acf-field-group/options.php:168
#: includes/admin/views/acf-post-type/advanced-settings.php:25
#: includes/fields/class-acf-field-relationship.php:708
msgid "Featured Image"
msgstr "Featured Image"

#: includes/fields/class-acf-field-relationship.php:704
msgid "Selected elements will be displayed in each result"
msgstr "Selected elements will be displayed in each result"

#: includes/fields/class-acf-field-relationship.php:703
msgid "Elements"
msgstr "Elements"

#: includes/fields/class-acf-field-relationship.php:637
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:709
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomy"

#: includes/fields/class-acf-field-relationship.php:636
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Type"
msgstr "Post Type"

#: includes/fields/class-acf-field-relationship.php:630
msgid "Filters"
msgstr "Filters"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-relationship.php:623
msgid "All taxonomies"
msgstr "All taxonomies"

#: includes/fields/class-acf-field-page_link.php:491
#: includes/fields/class-acf-field-post_object.php:403
#: includes/fields/class-acf-field-relationship.php:615
msgid "Filter by Taxonomy"
msgstr "Filter by Taxonomy"

#: includes/fields/class-acf-field-page_link.php:469
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:593
msgid "All post types"
msgstr "All post types"

#: includes/fields/class-acf-field-page_link.php:461
#: includes/fields/class-acf-field-post_object.php:373
#: includes/fields/class-acf-field-relationship.php:585
msgid "Filter by Post Type"
msgstr "Filter by Post Type"

#: includes/fields/class-acf-field-relationship.php:483
msgid "Search..."
msgstr "Search..."

#: includes/fields/class-acf-field-relationship.php:413
msgid "Select taxonomy"
msgstr "Select taxonomy"

#: includes/fields/class-acf-field-relationship.php:404
msgid "Select post type"
msgstr "Select post type"

#: includes/fields/class-acf-field-relationship.php:68
#: assets/build/js/acf-input.js:3925 assets/build/js/acf-input.js:4208
msgid "No matches found"
msgstr "No matches found"

#: includes/fields/class-acf-field-relationship.php:67
#: assets/build/js/acf-input.js:3908 assets/build/js/acf-input.js:4187
msgid "Loading"
msgstr "Loading"

#: includes/fields/class-acf-field-relationship.php:66
#: assets/build/js/acf-input.js:3817 assets/build/js/acf-input.js:4083
msgid "Maximum values reached ( {max} values )"
msgstr "Maximum values reached ( {max} values )"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Relationship"

#: includes/fields/class-acf-field-file.php:291
#: includes/fields/class-acf-field-image.php:317
msgid "Comma separated list. Leave blank for all types"
msgstr "Comma separated list. Leave blank for all types"

#: includes/fields/class-acf-field-file.php:290
#: includes/fields/class-acf-field-image.php:316
msgid "Allowed file types"
msgstr "Allowed file types"

#: includes/fields/class-acf-field-file.php:278
#: includes/fields/class-acf-field-image.php:280
msgid "Maximum"
msgstr "Maximum"

#: includes/fields/class-acf-field-file.php:154
#: includes/fields/class-acf-field-file.php:270
#: includes/fields/class-acf-field-file.php:282
#: includes/fields/class-acf-field-image.php:271
#: includes/fields/class-acf-field-image.php:307
msgid "File size"
msgstr "File size"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:281
msgid "Restrict which images can be uploaded"
msgstr "Restrict which images can be uploaded"

#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:244
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:235
#: includes/fields/class-acf-field-image.php:210
msgid "Uploaded to post"
msgstr "Uploaded to post"

#: includes/fields/class-acf-field-file.php:234
#: includes/fields/class-acf-field-image.php:209
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "All"

#: includes/fields/class-acf-field-file.php:229
#: includes/fields/class-acf-field-image.php:204
msgid "Limit the media library choice"
msgstr "Limit the media library choice"

#: includes/fields/class-acf-field-file.php:228
#: includes/fields/class-acf-field-image.php:203
msgid "Library"
msgstr "Library"

#: includes/fields/class-acf-field-image.php:336
msgid "Preview Size"
msgstr "Preview Size"

#: includes/fields/class-acf-field-image.php:195
msgid "Image ID"
msgstr "Image ID"

#: includes/fields/class-acf-field-image.php:194
msgid "Image URL"
msgstr "Image URL"

#: includes/fields/class-acf-field-image.php:193
msgid "Image Array"
msgstr "Image Array"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:372
#: includes/fields/class-acf-field-file.php:213
#: includes/fields/class-acf-field-link.php:171
#: includes/fields/class-acf-field-radio.php:213
msgid "Specify the returned value on front end"
msgstr "Specify the returned value on front end"

#: includes/fields/class-acf-field-button-group.php:167
#: includes/fields/class-acf-field-checkbox.php:371
#: includes/fields/class-acf-field-file.php:212
#: includes/fields/class-acf-field-link.php:170
#: includes/fields/class-acf-field-radio.php:212
#: includes/fields/class-acf-field-taxonomy.php:753
msgid "Return Value"
msgstr "Return Value"

#: includes/fields/class-acf-field-image.php:162
msgid "Add Image"
msgstr "Add Image"

#: includes/fields/class-acf-field-image.php:162
msgid "No image selected"
msgstr "No image selected"

#: includes/assets.php:352 includes/fields/class-acf-field-file.php:162
#: includes/fields/class-acf-field-image.php:142
#: includes/fields/class-acf-field-link.php:145 assets/build/js/acf.js:1566
#: assets/build/js/acf.js:1661
msgid "Remove"
msgstr "Remove"

#: includes/admin/views/acf-field-group/field.php:72
#: includes/fields/class-acf-field-file.php:160
#: includes/fields/class-acf-field-image.php:140
#: includes/fields/class-acf-field-link.php:145
msgid "Edit"
msgstr "Edit"

#: includes/fields/class-acf-field-image.php:70 includes/media.php:55
#: assets/build/js/acf-input.js:6803 assets/build/js/acf-input.js:7286
msgid "All images"
msgstr "All images"

#: includes/fields/class-acf-field-image.php:69
#: assets/build/js/acf-input.js:3181 assets/build/js/acf-input.js:3399
msgid "Update Image"
msgstr "Update Image"

#: includes/fields/class-acf-field-image.php:68
#: assets/build/js/acf-input.js:3180 assets/build/js/acf-input.js:3398
msgid "Edit Image"
msgstr "Edit Image"

#: includes/fields/class-acf-field-image.php:67
#: assets/build/js/acf-input.js:3156 assets/build/js/acf-input.js:3373
msgid "Select Image"
msgstr "Select Image"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Image"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Allow HTML markup to display as visible text instead of rendering"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Escape HTML"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:172
msgid "No Formatting"
msgstr "No Formatting"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:171
msgid "Automatically add &lt;br&gt;"
msgstr "Automatically add &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:114
#: includes/fields/class-acf-field-textarea.php:170
msgid "Automatically add paragraphs"
msgstr "Automatically add paragraphs"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:166
msgid "Controls how new lines are rendered"
msgstr "Controls how new lines are rendered"

#: includes/fields/class-acf-field-message.php:109
#: includes/fields/class-acf-field-textarea.php:165
msgid "New Lines"
msgstr "New Lines"

#: includes/fields/class-acf-field-date_picker.php:232
#: includes/fields/class-acf-field-date_time_picker.php:220
msgid "Week Starts On"
msgstr "Week Starts On"

#: includes/fields/class-acf-field-date_picker.php:201
msgid "The format used when saving a value"
msgstr "The format used when saving a value"

#: includes/fields/class-acf-field-date_picker.php:200
msgid "Save Format"
msgstr "Save Format"

#: includes/fields/class-acf-field-date_picker.php:67
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Wk"

#: includes/fields/class-acf-field-date_picker.php:66
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Prev"

#: includes/fields/class-acf-field-date_picker.php:65
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Next"

#: includes/fields/class-acf-field-date_picker.php:64
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Today"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Done"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Date Picker"

#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:284
#: includes/fields/class-acf-field-oembed.php:268
msgid "Width"
msgstr "Width"

#: includes/fields/class-acf-field-oembed.php:265
#: includes/fields/class-acf-field-oembed.php:277
msgid "Embed Size"
msgstr "Embed Size"

#: includes/fields/class-acf-field-oembed.php:222
msgid "Enter URL"
msgstr "Enter URL"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:184
msgid "Text shown when inactive"
msgstr "Text shown when inactive"

#: includes/fields/class-acf-field-true_false.php:183
msgid "Off Text"
msgstr "Off Text"

#: includes/fields/class-acf-field-true_false.php:168
msgid "Text shown when active"
msgstr "Text shown when active"

#: includes/fields/class-acf-field-true_false.php:167
msgid "On Text"
msgstr "On Text"

#: includes/fields/class-acf-field-select.php:456
#: includes/fields/class-acf-field-true_false.php:199
msgid "Stylized UI"
msgstr "Stylised UI"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:361
#: includes/fields/class-acf-field-color_picker.php:158
#: includes/fields/class-acf-field-email.php:117
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-range.php:164
#: includes/fields/class-acf-field-select.php:386
#: includes/fields/class-acf-field-text.php:97
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-true_false.php:147
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:318
msgid "Default Value"
msgstr "Default Value"

#: includes/fields/class-acf-field-true_false.php:138
msgid "Displays text alongside the checkbox"
msgstr "Displays text alongside the checkbox"

#: includes/fields/class-acf-field-message.php:26
#: includes/fields/class-acf-field-message.php:99
#: includes/fields/class-acf-field-true_false.php:137
msgid "Message"
msgstr "Message"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:86
#: includes/fields/class-acf-field-true_false.php:187
#: assets/build/js/acf.js:1743 assets/build/js/acf.js:1861
msgid "No"
msgstr "No"

#: includes/assets.php:350 includes/fields/class-acf-field-true_false.php:83
#: includes/fields/class-acf-field-true_false.php:171
#: assets/build/js/acf.js:1742 assets/build/js/acf.js:1860
msgid "Yes"
msgstr "Yes"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "True / False"

#: includes/fields/class-acf-field-group.php:474
msgid "Row"
msgstr "Row"

#: includes/fields/class-acf-field-group.php:473
msgid "Table"
msgstr "Table"

#: includes/admin/post-types/admin-field-group.php:131
#: includes/fields/class-acf-field-group.php:472
msgid "Block"
msgstr "Block"

#: includes/fields/class-acf-field-group.php:467
msgid "Specify the style used to render the selected fields"
msgstr "Specify the style used to render the selected fields"

#: includes/fields.php:356 includes/fields/class-acf-field-button-group.php:215
#: includes/fields/class-acf-field-checkbox.php:435
#: includes/fields/class-acf-field-group.php:466
#: includes/fields/class-acf-field-radio.php:286
msgid "Layout"
msgstr "Layout"

#: includes/fields/class-acf-field-group.php:450
msgid "Sub Fields"
msgstr "Sub Fields"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Group"

#: includes/fields/class-acf-field-google-map.php:235
msgid "Customize the map height"
msgstr "Customise the map height"

#: includes/fields/class-acf-field-google-map.php:234
#: includes/fields/class-acf-field-image.php:259
#: includes/fields/class-acf-field-image.php:295
#: includes/fields/class-acf-field-oembed.php:280
msgid "Height"
msgstr "Height"

#: includes/fields/class-acf-field-google-map.php:223
msgid "Set the initial zoom level"
msgstr "Set the initial zoom level"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:196
#: includes/fields/class-acf-field-google-map.php:209
msgid "Center the initial map"
msgstr "Centre the initial map"

#: includes/fields/class-acf-field-google-map.php:195
#: includes/fields/class-acf-field-google-map.php:208
msgid "Center"
msgstr "Centre"

#: includes/fields/class-acf-field-google-map.php:163
msgid "Search for address..."
msgstr "Search for address..."

#: includes/fields/class-acf-field-google-map.php:160
msgid "Find current location"
msgstr "Find current location"

#: includes/fields/class-acf-field-google-map.php:159
msgid "Clear location"
msgstr "Clear location"

#: includes/fields/class-acf-field-google-map.php:158
#: includes/fields/class-acf-field-relationship.php:635
msgid "Search"
msgstr "Search"

#: includes/fields/class-acf-field-google-map.php:63
#: assets/build/js/acf-input.js:2840 assets/build/js/acf-input.js:3026
msgid "Sorry, this browser does not support geolocation"
msgstr "Sorry, this browser does not support geolocation"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google Map"

#: includes/fields/class-acf-field-date_picker.php:212
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:132
msgid "The format returned via template functions"
msgstr "The format returned via template functions"

#: includes/fields/class-acf-field-color_picker.php:182
#: includes/fields/class-acf-field-date_picker.php:211
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-image.php:187
#: includes/fields/class-acf-field-post_object.php:418
#: includes/fields/class-acf-field-relationship.php:645
#: includes/fields/class-acf-field-select.php:397
#: includes/fields/class-acf-field-time_picker.php:131
#: includes/fields/class-acf-field-user.php:70
msgid "Return Format"
msgstr "Return Format"

#: includes/fields/class-acf-field-date_picker.php:190
#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:192
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:123
#: includes/fields/class-acf-field-time_picker.php:139
msgid "Custom:"
msgstr "Custom:"

#: includes/fields/class-acf-field-date_picker.php:182
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:116
msgid "The format displayed when editing a post"
msgstr "The format displayed when editing a post"

#: includes/fields/class-acf-field-date_picker.php:181
#: includes/fields/class-acf-field-date_time_picker.php:182
#: includes/fields/class-acf-field-time_picker.php:115
msgid "Display Format"
msgstr "Display Format"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Time Picker"

#. translators: counts for inactive field groups
#: acf.php:491
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inactive <span class=\"count\">(%s)</span>"
msgstr[1] "Inactive <span class=\"count\">(%s)</span>"

#: acf.php:450
msgid "No Fields found in Trash"
msgstr "No Fields found in bin"

#: acf.php:449
msgid "No Fields found"
msgstr "No Fields found"

#: acf.php:448
msgid "Search Fields"
msgstr "Search Fields"

#: acf.php:447
msgid "View Field"
msgstr "View Field"

#: acf.php:446 includes/admin/views/acf-field-group/fields.php:104
msgid "New Field"
msgstr "New Field"

#: acf.php:445
msgid "Edit Field"
msgstr "Edit Field"

#: acf.php:444
msgid "Add New Field"
msgstr "Add New Field"

#: acf.php:442
msgid "Field"
msgstr "Field"

#: acf.php:441 includes/admin/post-types/admin-field-group.php:154
#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/views/acf-field-group/fields.php:21
msgid "Fields"
msgstr "Fields"

#: acf.php:416
msgid "No Field Groups found in Trash"
msgstr "No Field Groups found in bin"

#: acf.php:415
msgid "No Field Groups found"
msgstr "No Field Groups found"

#: acf.php:414
msgid "Search Field Groups"
msgstr "Search Field Groups"

#: acf.php:413
msgid "View Field Group"
msgstr "View Field Group"

#: acf.php:412
msgid "New Field Group"
msgstr "New Field Group"

#: acf.php:411
msgid "Edit Field Group"
msgstr "Edit Field Group"

#: acf.php:410
msgid "Add New Field Group"
msgstr "Add New Field Group"

#: acf.php:409 acf.php:443
#: includes/admin/views/acf-post-type/advanced-settings.php:215
#: includes/admin/views/acf-post-type/advanced-settings.php:217
#: includes/post-types/class-acf-post-type.php:92
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Add New"

#: acf.php:408
msgid "Field Group"
msgstr "Field Group"

#: acf.php:407 includes/admin/post-types/admin-field-groups.php:56
#: includes/admin/post-types/admin-post-types.php:105
#: includes/admin/post-types/admin-taxonomies.php:105
msgid "Field Groups"
msgstr "Field Groups"

#. Description of the plugin
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Customise WordPress with powerful, professional and intuitive fields."

#. Plugin URI of the plugin
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php:92
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr ""

#: pro/blocks.php:170
msgid "Block type name is required."
msgstr ""

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr ""

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr ""

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a href=\"%1$s"
"\">Updates</a> page. If you don't have a licence key, please see <a href="
"\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""
"<b>ACF Activation Error</b>. Your defined licence key has changed, but an "
"error occurred when deactivating your old licence"

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""
"<b>ACF Activation Error</b>. Your defined licence key has changed, but an "
"error occurred when connecting to activation server"

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""

#: pro/updates.php:279
msgid "Check Again"
msgstr ""

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr ""

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr ""

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Error</b>. Your licence for this site has expired or been deactivated. "
"Please reactivate your ACF PRO licence."

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr ""

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr ""

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
msgid "layout"
msgid_plural "layouts"
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Duplicate layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
msgid "Minimum rows not reached ({min} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1060
msgid "Invalid field key or name."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
msgid "Click to reorder"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
msgid "First Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
msgid "Previous Page"
msgstr ""

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
msgid "Next Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
msgid "Last Page"
msgstr ""

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr ""

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Deactivate Licence"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Activate Licence"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Licence Information"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"To unlock updates, please enter your licence key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Licence Key"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr "Your licence key is defined in wp-config.php."

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr ""

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr ""

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr ""

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr ""

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr ""

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
msgid "Enter your license key to unlock updates"
msgstr "Enter your licence key to unlock updates"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr ""

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr "Please reactivate your licence to unlock updates"
