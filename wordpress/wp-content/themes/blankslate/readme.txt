=== BlankSlate ===

Contributors: tidythemes, bhadaway
Theme link: https://github.com/tidythemes/blankslate
Tags: one-column, two-columns, custom-menu, featured-images, microformats, sticky-post, threaded-comments, translation-ready
Requires at least: 5.1
Tested up to: 5.1
Stable tag: trunk
License: GNU General Public License v3 or Later
License URI: https://www.gnu.org/licenses/gpl.html

Please read: tidythemes.com/concept.

== Description ==

IMPORTANT - PLEASE READ: http://tidythemes.com/concept/

YOU MAY DELETE THIS FILE AND ANY OTHER FILE(S) BEFORE STARTING YOUR PROJECT

BlankSlate is the definitive WordPress HTML5 boilerplate starter theme. We've carefully constructed the most clean and minimalist theme possible for designers and developers to use as a base to build websites for clients or to build completely custom themes from scratch. Clean, simple, unstyled, semi-minified, unformatted, and valid code, SEO-friendly, jQuery-enabled, no programmer comments, standardized and as white label as possible, and most importantly, the CSS is reset for cross-browser-compatability and no intrusive visual CSS styles have been added whatsoever. A perfect skeleton theme. For support and suggestions, go to: https://github.com/tidythemes/blankslate/issues. Thank you.

If you're creating your own theme or client project, open up all files and do a "Find and Replace All in All Files" on the word "blankslate" with your own slug and prefix.

=== License ===

In its unchanged and original state:

BlankSlate WordPress Theme © 2011-2019 TidyThemes
BlankSlate is distributed under the terms of the GNU GPL

The BlankSlate theme package and all files contained within are distributed under the terms of the GNU GPL v3 or Later (https://www.gnu.org/licenses/gpl.html).

Once you've significantly changed the theme to build your own unique project, either for yourself or for a client under a different theme name (as is encouraged), you're entirely welcome to copyright and license that project as you see fit.