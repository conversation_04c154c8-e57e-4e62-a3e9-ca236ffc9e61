<?php
add_action('after_setup_theme', 'blankslate_setup');
function blankslate_setup() {

	load_theme_textdomain('blankslate', get_template_directory() . '/languages');
	add_theme_support('title-tag');
	add_theme_support('automatic-feed-links');
	add_theme_support('post-thumbnails');
	add_theme_support('html5', array('search-form'));
	global $content_width;
	if ( ! isset($content_width)) {
		$content_width = 1920;
	}
	register_nav_menus(array('main-menu' => esc_html__('Main Menu', 'blankslate')));
}

add_action('wp_enqueue_scripts', 'blankslate_load_scripts');
function blankslate_load_scripts() {

	wp_enqueue_style('blankslate-style', get_stylesheet_uri());
	wp_enqueue_script('jquery');
}

add_action('wp_footer', 'blankslate_footer_scripts');
function blankslate_footer_scripts() {

	?>
    <script>
        jQuery(document).ready(function ($) {
            var deviceAgent = navigator.userAgent.toLowerCase();
            if (deviceAgent.match(/(iphone|ipod|ipad)/)) {
                $("html").addClass("ios");
                $("html").addClass("mobile");
            }
            if (navigator.userAgent.search("MSIE") >= 0) {
                $("html").addClass("ie");
            } else if (navigator.userAgent.search("Chrome") >= 0) {
                $("html").addClass("chrome");
            } else if (navigator.userAgent.search("Firefox") >= 0) {
                $("html").addClass("firefox");
            } else if (navigator.userAgent.search("Safari") >= 0 && navigator.userAgent.search("Chrome") < 0) {
                $("html").addClass("safari");
            } else if (navigator.userAgent.search("Opera") >= 0) {
                $("html").addClass("opera");
            }
        });
    </script>
	<?php
}

add_filter('document_title_separator', 'blankslate_document_title_separator');
function blankslate_document_title_separator($sep) {

	$sep = '|';

	return $sep;
}

add_filter('the_title', 'blankslate_title');
function blankslate_title($title) {

	if ($title == '') {
		return '...';
	} else {
		return $title;
	}
}

add_filter('the_content_more_link', 'blankslate_read_more_link');
function blankslate_read_more_link() {

	if ( ! is_admin()) {
		return ' <a href="' . esc_url(get_permalink()) . '" class="more-link">...</a>';
	}
}

add_filter('excerpt_more', 'blankslate_excerpt_read_more_link');
function blankslate_excerpt_read_more_link($more) {

	if ( ! is_admin()) {
		global $post;

		return ' <a href="' . esc_url(get_permalink($post->ID)) . '" class="more-link">...</a>';
	}
}

add_filter('intermediate_image_sizes_advanced', 'blankslate_image_insert_override');
function blankslate_image_insert_override($sizes) {

	unset($sizes['medium_large']);

	return $sizes;
}

add_action('widgets_init', 'blankslate_widgets_init');
function blankslate_widgets_init() {

	register_sidebar(array(
		'name'          => esc_html__('Sidebar Widget Area', 'blankslate'),
		'id'            => 'primary-widget-area',
		'before_widget' => '<li id="%1$s" class="widget-container %2$s">',
		'after_widget'  => '</li>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	));
}

add_action('wp_head', 'blankslate_pingback_header');
function blankslate_pingback_header() {

	if (is_singular() && pings_open()) {
		printf('<link rel="pingback" href="%s" />' . "\n", esc_url(get_bloginfo('pingback_url')));
	}
}

add_action('comment_form_before', 'blankslate_enqueue_comment_reply_script');
function blankslate_enqueue_comment_reply_script() {

	if (get_option('thread_comments')) {
		wp_enqueue_script('comment-reply');
	}
}

function blankslate_custom_pings($comment) {

	?>
    <li <?php comment_class(); ?> id="li-comment-<?php comment_ID(); ?>"><?php echo comment_author_link(); ?></li>
	<?php
}

add_filter('get_comments_number', 'blankslate_comment_count', 0);
function blankslate_comment_count($count) {

	if ( ! is_admin()) {
		global $id;
		$get_comments     = get_comments('status=approve&post_id=' . $id);
		$comments_by_type = separate_comments($get_comments);

		return count($comments_by_type['comment']);
	} else {
		return $count;
	}
}

/** CUSTOM ADDED FUNCTIONS */

// fully Disable Gutenberg editor
add_filter('use_block_editor_for_post_type', '__return_false', 10);

// hide Wordpress update notifications
function remove_core_updates() {

	global $wp_version;

	return (object) array('last_checked' => time(), 'version_checked' => $wp_version,);
}

add_filter('pre_site_transient_update_core', 'remove_core_updates'); //hide updates for WordPress itself
add_filter('pre_site_transient_update_plugins', 'remove_core_updates'); //hide updates for all plugins
add_filter('pre_site_transient_update_themes', 'remove_core_updates'); //hide updates for all themes

// WP editor content filter
function the_content_filter($content) {

	$block = join("|", array("one_third", "team_member"));
	$rep   = preg_replace("/(<p>)?\[($block)(\s[^\]]+)?\](<\/p>|<br \/>)?/", "[$2$3]", $content);
	$rep   = preg_replace("/(<p>)?\[\/($block)](<\/p>|<br \/>)?/", "[/$2]", $rep);

	return $rep;
}

add_filter("the_content", "the_content_filter");

// each time we save a post, we need to notify Pravomat
add_action('save_post', 'sync');
function sync($id)
{
    try {

        // fetch post
        $post = get_post($id);

	    $index = $post->post_type;
        $status = $post->post_status;

        // if revision (preview), set parent post_type as index
        if ($post->post_type === 'revision') {
            $parent_id = wp_get_post_parent_id($post->ID);
            $parent_post = get_post($parent_id);

            $index = $parent_post->post_type;
            $status = $parent_post->post_status;
        }

        // if post is in draft OR post is revision
        if($status === 'draft' || $post->post_type === 'revision'){

            if($index === 'example') {
                delete_example_images($id);
            }

            cache_clear($index);

            return;
        }

        // for document/example, sync ES, delete screenshots
        if(in_array($index, ['document', 'example'])) {

            // if post is document, sync laravel template model tags with wp document tags
            if ($index === 'document') {
                sync_template_tags($id);
            }

            // if post is example, delete content images
            if ($index === 'example') {
                delete_example_images($id);
            }

            // sync es data for this post
            sync_es($index, $id);
        }

        // clear appropriate search+response cache in laravel
        cache_clear($index);

    } catch (Exception $e) {
        error_log('Failed API sync.');
    }
}

function sync_template_tags($id) {
	$endpoint = '/template/tags/sync?key=' . PRAVOMAT_API_KEY . '&id=' . $id;
	return pravomat_api($endpoint);
}

function sync_es($index, $id) {
	$endpoint = '/es/sync?key=' . PRAVOMAT_API_KEY . '&index=' . $index . '&id=' . $id;
	return pravomat_api($endpoint);
}

function delete_example_images($id) {
	$endpoint = '/example/images/delete?key=' . PRAVOMAT_API_KEY . '&id=' . $id;
	return pravomat_api($endpoint);
}

function cache_clear($index) {
	$endpoint = '/cache/clear?key=' . PRAVOMAT_API_KEY . '&index=' . $index;
	return pravomat_api($endpoint);
}

function pravomat_api($endpoint) {

    $url = get_option('home') . $endpoint;

	$ch = curl_init($url);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_FAILONERROR, true);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);

	// check if 'staging' is in the home URL
	$home_url = get_option('home');
	if (str_contains($home_url, 'staging')) {
		// staging server logic with HTTP Basic Auth
		if (defined('PRAVOMAT_HTPASSWD') && !empty(PRAVOMAT_HTPASSWD)) {
			curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
			curl_setopt($ch, CURLOPT_USERPWD, PRAVOMAT_HTPASSWD);
		}
	}

	$response = curl_exec($ch);

	if ($response === false) {
		$error = curl_error($ch);
		curl_close($ch);
		error_log("cURL request failed: " . $error);
		return false;
	}

	$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	curl_close($ch);

	if ($http_code >= 200 && $http_code < 300) {
		return true;
	} else {
		error_log("cURL request failed. HTTP Code: " . $http_code);
		return false;
	}
}

add_action('init', 'create_document_post_type');
function create_document_post_type() {
	$labels = array(
		'name'               => _x('Documents', 'post type general name'),
		'singular_name'      => _x('Document', 'post type singular name'),
		'menu_name'          => _x('Documents', 'admin menu'),
		'name_admin_bar'     => _x('Document', 'add new on admin bar'),
		'add_new'            => _x('Add New', 'document'),
		'add_new_item'       => __('Add New Document'),
		'new_item'           => __('New Document'),
		'edit_item'          => __('Edit Document'),
		'view_item'          => __('View Document'),
		'all_items'          => __('All Documents'),
		'search_items'       => __('Search Documents'),
		'parent_item_colon'  => __('Parent Documents:'),
		'not_found'          => __('No documents found.'),
		'not_found_in_trash' => __('No documents found in Trash.')
	);

	$args = array(
		'labels'             => $labels,
		'description'        => __('Description.'),
		'public'             => true,
		'publicly_queryable' => true,
		'show_ui'            => true,
		'show_in_menu'       => true,
		'query_var'          => true,
		'rewrite'            => array('slug' => '/', 'with_front' => false),
		'capability_type'    => 'post',
		'has_archive'        => true,
		'hierarchical'       => false,
		'menu_position'      => null,
		'menu_icon'          => 'dashicons-media-document',
	);

	register_post_type('document', $args );
}

add_action('init', 'create_example_post_type');
function create_example_post_type() {
	$labels = array(
		'name'               => _x('Examples', 'post type general name'),
		'singular_name'      => _x('Example', 'post type singular name'),
		'menu_name'          => _x('Examples', 'admin menu'),
		'name_admin_bar'     => _x('Example', 'add new on admin bar'),
		'add_new'            => _x('Add New', 'example'),
		'add_new_item'       => __('Add New Example'),
		'new_item'           => __('New Example'),
		'edit_item'          => __('Edit Example'),
		'view_item'          => __('View Example'),
		'all_items'          => __('All Examples'),
		'search_items'       => __('Search Examples'),
		'parent_item_colon'  => __('Parent Examples:'),
		'not_found'          => __('No examples found.'),
		'not_found_in_trash' => __('No examples found in Trash.')
	);

	$args = array(
		'labels'             => $labels,
		'description'        => __('Description.'),
		'public'             => true,
		'publicly_queryable' => true,
		'show_ui'            => true,
		'show_in_menu'       => true,
		'query_var'          => true,
		'rewrite'            => array('slug' => 'primjeri', 'with_front' => false),
		'capability_type'    => 'post',
		'has_archive'        => true,
		'hierarchical'       => false,
		'menu_position'      => null,
		'supports'           => array('title'),
		'menu_icon'          => 'dashicons-format-gallery'
	);

	register_post_type('example', $args );
}

add_action('admin_menu', 'remove_admin_menus' );
function remove_admin_menus() {
	remove_menu_page('edit-comments.php' );
}


function custom_mce_buttons_2($buttons) {
	$index = array_search('forecolor', $buttons);
	if ($index !== false) {
		array_splice($buttons, $index + 1, 0, 'backcolor');
	}

	$index = array_search('backcolor', $buttons);
	if ($index !== false) {
		array_splice($buttons, $index + 1, 0, 'remove_enumeration');
	}

	$index = array_search('remove_enumeration', $buttons);
	if ($index !== false) {
		array_splice($buttons, $index + 1, 0, 'tooltip');
	}

	$index = array_search('redo', $buttons);
	if ($index !== false) {
		array_splice($buttons, $index + 1, 0, 'table');
	}

	return $buttons;
}
add_filter('mce_buttons_2', 'custom_mce_buttons_2');

function add_the_table_plugin( $plugins ) {
	$plugins['table'] = includes_url() . 'js/tinymce/plugins/table/plugin.min.js';
	return $plugins;
}
add_filter( 'mce_external_plugins', 'add_the_table_plugin' );


// exclude the current post from the query
function my_acf_relationship_query( $args, $field, $post_id ) {

	$args['post__not_in'] = array( $post_id );

	return $args;

}
add_filter('acf/fields/relationship/query', 'my_acf_relationship_query', 10, 3);

function enqueue_admin_scripts() {
	// add custom made buttons to tinyMCE
	wp_enqueue_script('custom_admin_script', get_template_directory_uri() . '/js/tinymce-remove-paragraph-enumeration-button.js', ['wp-tinymce'], false, true);
	wp_enqueue_script('custom_admin_script', get_template_directory_uri() . '/js/tinymce-tooltips.js', ['wp-tinymce'], false, true);
}
add_action('admin_enqueue_scripts', 'enqueue_admin_scripts');

function my_custom_tinymce_styles($mce_css) {
	$mce_css .= ( !empty($mce_css) ? ',' : '' ) . get_template_directory_uri() . '/css/editor-style.css';
	return $mce_css;
}
add_filter('mce_css', 'my_custom_tinymce_styles');


function my_mce_external_plugins($plugins) {
	$plugins['remove_enumeration'] = get_template_directory_uri() . '/js/tinymce-remove-paragraph-enumeration-button.js';
	$plugins['tooltip'] = get_template_directory_uri() . '/js/tinymce-tooltips.js';
	return $plugins;
}
add_filter('mce_external_plugins', 'my_mce_external_plugins');

function changeMceDefaults($in) {
	$in[ 'wordpress_adv_hidden' ] = false; // Keep the "kitchen sink" open
	return $in;
}
add_filter( 'tiny_mce_before_init', 'changeMceDefaults' );