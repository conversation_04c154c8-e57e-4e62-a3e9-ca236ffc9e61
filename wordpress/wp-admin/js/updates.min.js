/*! This file is auto-generated */
!function(c,g,m){var f=c(document),h=g.i18n.__,d=g.i18n._x,i=g.i18n._n,o=g.i18n._nx,r=g.i18n.sprintf;(g=g||{}).updates={},g.updates.l10n={searchResults:"",searchResultsLabel:"",noPlugins:"",noItemsSelected:"",updating:"",pluginUpdated:"",themeUpdated:"",update:"",updateNow:"",pluginUpdateNowLabel:"",updateFailedShort:"",updateFailed:"",pluginUpdatingLabel:"",pluginUpdatedLabel:"",pluginUpdateFailedLabel:"",updatingMsg:"",updatedMsg:"",updateCancel:"",beforeunload:"",installNow:"",pluginInstallNowLabel:"",installing:"",pluginInstalled:"",themeInstalled:"",installFailedShort:"",installFailed:"",pluginInstallingLabel:"",themeInstallingLabel:"",pluginInstalledLabel:"",themeInstalledLabel:"",pluginInstallFailedLabel:"",themeInstallFailedLabel:"",installingMsg:"",installedMsg:"",importerInstalledMsg:"",aysDelete:"",aysDeleteUninstall:"",aysBulkDelete:"",aysBulkDeleteThemes:"",deleting:"",deleteFailed:"",pluginDeleted:"",themeDeleted:"",livePreview:"",activatePlugin:"",activateTheme:"",activatePluginLabel:"",activateThemeLabel:"",activateImporter:"",activateImporterLabel:"",unknownError:"",connectionError:"",nonceError:"",pluginsFound:"",noPluginsFound:"",autoUpdatesEnable:"",autoUpdatesEnabling:"",autoUpdatesEnabled:"",autoUpdatesDisable:"",autoUpdatesDisabling:"",autoUpdatesDisabled:"",autoUpdatesError:""},g.updates.l10n=window.wp.deprecateL10nObject("wp.updates.l10n",g.updates.l10n,"5.5.0"),g.updates.ajaxNonce=m.ajax_nonce,g.updates.searchTerm="",g.updates.shouldRequestFilesystemCredentials=!1,g.updates.filesystemCredentials={ftp:{host:"",username:"",password:"",connectionType:""},ssh:{publicKey:"",privateKey:""},fsNonce:"",available:!1},g.updates.ajaxLocked=!1,g.updates.adminNotice=g.template("wp-updates-admin-notice"),g.updates.queue=[],g.updates.$elToReturnFocusToFromCredentialsModal=void 0,g.updates.addAdminNotice=function(e){var t,a=c(e.selector),s=c(".wp-header-end");delete e.selector,t=g.updates.adminNotice(e),(a=a.length?a:c("#"+e.id)).length?a.replaceWith(t):s.length?s.after(t):"customize"===pagenow?c(".customize-themes-notifications").append(t):c(".wrap").find("> h1").after(t),f.trigger("wp-updates-notice-added")},g.updates.ajax=function(e,t){var a={};return g.updates.ajaxLocked?(g.updates.queue.push({action:e,data:t}),c.Deferred()):(g.updates.ajaxLocked=!0,t.success&&(a.success=t.success,delete t.success),t.error&&(a.error=t.error,delete t.error),a.data=_.extend(t,{action:e,_ajax_nonce:g.updates.ajaxNonce,_fs_nonce:g.updates.filesystemCredentials.fsNonce,username:g.updates.filesystemCredentials.ftp.username,password:g.updates.filesystemCredentials.ftp.password,hostname:g.updates.filesystemCredentials.ftp.hostname,connection_type:g.updates.filesystemCredentials.ftp.connectionType,public_key:g.updates.filesystemCredentials.ssh.publicKey,private_key:g.updates.filesystemCredentials.ssh.privateKey}),g.ajax.send(a).always(g.updates.ajaxAlways))},g.updates.ajaxAlways=function(e){e.errorCode&&"unable_to_connect_to_filesystem"===e.errorCode||(g.updates.ajaxLocked=!1,g.updates.queueChecker()),void 0!==e.debug&&window.console&&window.console.log&&_.map(e.debug,function(e){window.console.log(g.sanitize.stripTagsAndEncodeText(e))})},g.updates.refreshCount=function(){var e,t=c("#wp-admin-bar-updates"),a=c('a[href="update-core.php"] .update-plugins'),s=c('a[href="plugins.php"] .update-plugins'),n=c('a[href="themes.php"] .update-plugins');t.find(".ab-label").text(m.totals.counts.total),t.find(".updates-available-text").text(r(i("%s update available","%s updates available",m.totals.counts.total),m.totals.counts.total)),0===m.totals.counts.total&&t.find(".ab-label").parents("li").remove(),a.each(function(e,t){t.className=t.className.replace(/count-\d+/,"count-"+m.totals.counts.total)}),0<m.totals.counts.total?a.find(".update-count").text(m.totals.counts.total):a.remove(),s.each(function(e,t){t.className=t.className.replace(/count-\d+/,"count-"+m.totals.counts.plugins)}),0<m.totals.counts.total?s.find(".plugin-count").text(m.totals.counts.plugins):s.remove(),n.each(function(e,t){t.className=t.className.replace(/count-\d+/,"count-"+m.totals.counts.themes)}),0<m.totals.counts.total?n.find(".theme-count").text(m.totals.counts.themes):n.remove(),"plugins"===pagenow||"plugins-network"===pagenow?e=m.totals.counts.plugins:"themes"!==pagenow&&"themes-network"!==pagenow||(e=m.totals.counts.themes),0<e?c(".subsubsub .upgrade .count").text("("+e+")"):(c(".subsubsub .upgrade").remove(),c(".subsubsub li:last").html(function(){return c(this).children()}))},g.updates.setCardButtonStatus=function(e){var t=window.parent===window?null:window.parent;c.support.postMessage=!!window.postMessage,!1!==c.support.postMessage&&null!==t&&-1===window.parent.location.pathname.indexOf("index.php")&&t.postMessage(JSON.stringify(e),window.location.origin)},g.updates.decrementCount=function(e){m.totals.counts.total=Math.max(--m.totals.counts.total,0),"plugin"===e?m.totals.counts.plugins=Math.max(--m.totals.counts.plugins,0):"theme"===e&&(m.totals.counts.themes=Math.max(--m.totals.counts.themes,0)),g.updates.refreshCount(e)},g.updates.updatePlugin=function(e){var t,a,s,n=c("#wp-admin-bar-updates"),i=h("Updating..."),l="plugin-install"===pagenow||"plugin-install-network"===pagenow;return e=_.extend({success:g.updates.updatePluginSuccess,error:g.updates.updatePluginError},e),"plugins"===pagenow||"plugins-network"===pagenow?(a=(s=c('tr[data-plugin="'+e.plugin+'"]')).find(".update-message").removeClass("notice-error").addClass("updating-message notice-warning").find("p"),s=r(d("Updating %s...","plugin"),s.find(".plugin-title strong").text())):l&&(a=(t=c(".plugin-card-"+e.slug+", #plugin-information-footer")).find(".update-now").addClass("updating-message"),s=r(d("Updating %s...","plugin"),a.data("name")),t.removeClass("plugin-card-update-failed").find(".notice.notice-error").remove()),n.addClass("spin"),a.html()!==h("Updating...")&&a.data("originaltext",a.html()),a.attr("aria-label",s).text(i),f.trigger("wp-plugin-updating",e),l&&"plugin-information-footer"===t.attr("id")&&g.updates.setCardButtonStatus({status:"updating-plugin",slug:e.slug,addClasses:"updating-message",text:i,ariaLabel:s}),g.updates.ajax("update-plugin",e)},g.updates.updatePluginSuccess=function(e){var t,a,s,n=c("#wp-admin-bar-updates"),i=d("Updated!","plugin"),l=r(d("%s updated!","plugin"),e.pluginName);"plugins"===pagenow||"plugins-network"===pagenow?(a=(t=c('tr[data-plugin="'+e.plugin+'"]').removeClass("update is-enqueued").addClass("updated")).find(".update-message").removeClass("updating-message notice-warning").addClass("updated-message notice-success").find("p"),s=t.find(".plugin-version-author-uri").html().replace(e.oldVersion,e.newVersion),t.find(".plugin-version-author-uri").html(s),t.find(".auto-update-time").empty()):"plugin-install"!==pagenow&&"plugin-install-network"!==pagenow||(a=c(".plugin-card-"+e.slug+", #plugin-information-footer").find(".update-now").removeClass("updating-message").addClass("button-disabled updated-message")),n.removeClass("spin"),a.attr("aria-label",l).text(i),g.a11y.speak(h("Update completed successfully.")),"plugin_install_from_iframe"!==a.attr("id")?g.updates.decrementCount("plugin"):g.updates.setCardButtonStatus({status:"updated-plugin",slug:e.slug,removeClasses:"updating-message",addClasses:"button-disabled updated-message",text:i,ariaLabel:l}),f.trigger("wp-plugin-update-success",e)},g.updates.updatePluginError=function(e){var t,a,s,n,i,l=c("#wp-admin-bar-updates");g.updates.isValidResponse(e,"update")&&!g.updates.maybeHandleCredentialError(e,"update-plugin")&&(s=r(h("Update failed: %s"),e.errorMessage),"plugins"===pagenow||"plugins-network"===pagenow?(c('tr[data-plugin="'+e.plugin+'"]').removeClass("is-enqueued"),(a=(e.plugin?c('tr[data-plugin="'+e.plugin+'"]'):c('tr[data-slug="'+e.slug+'"]')).find(".update-message")).removeClass("updating-message notice-warning").addClass("notice-error").find("p").html(s),e.pluginName?a.find("p").attr("aria-label",r(d("%s update failed.","plugin"),e.pluginName)):a.find("p").removeAttr("aria-label")):"plugin-install"!==pagenow&&"plugin-install-network"!==pagenow||(n=h("Update failed."),(t=c(".plugin-card-"+e.slug+", #plugin-information-footer").append(g.updates.adminNotice({className:"update-message notice-error notice-alt is-dismissible",message:s}))).hasClass("plugin-card-"+e.slug)&&t.addClass("plugin-card-update-failed"),t.find(".update-now").text(n).removeClass("updating-message"),e.pluginName?(i=r(d("%s update failed.","plugin"),e.pluginName),t.find(".update-now").attr("aria-label",i)):(i="",t.find(".update-now").removeAttr("aria-label")),t.on("click",".notice.is-dismissible .notice-dismiss",function(){setTimeout(function(){t.removeClass("plugin-card-update-failed").find(".column-name a").trigger("focus"),t.find(".update-now").attr("aria-label",!1).text(h("Update Now"))},200)})),l.removeClass("spin"),g.a11y.speak(s,"assertive"),"plugin-information-footer"===t.attr("id")&&g.updates.setCardButtonStatus({status:"plugin-update-failed",slug:e.slug,removeClasses:"updating-message",text:n,ariaLabel:i}),f.trigger("wp-plugin-update-error",e))},g.updates.installPlugin=function(e){var t,a=c(".plugin-card-"+e.slug+", #plugin-information-footer"),s=a.find(".install-now"),n=h("Installing...");return e=_.extend({success:g.updates.installPluginSuccess,error:g.updates.installPluginError},e),(s="import"===pagenow?c('[data-slug="'+e.slug+'"]'):s).html()!==h("Installing...")&&s.data("originaltext",s.html()),t=r(d("Installing %s...","plugin"),s.data("name")),s.addClass("updating-message").attr("aria-label",t).text(n),g.a11y.speak(h("Installing... please wait.")),a.removeClass("plugin-card-install-failed").find(".notice.notice-error").remove(),f.trigger("wp-plugin-installing",e),"plugin-information-footer"===s.parent().attr("id")&&g.updates.setCardButtonStatus({status:"installing-plugin",slug:e.slug,addClasses:"updating-message",text:n,ariaLabel:t}),g.updates.ajax("install-plugin",e)},g.updates.installPluginSuccess=function(e){var t=c(".plugin-card-"+e.slug+", #plugin-information-footer").find(".install-now"),a=d("Installed!","plugin"),s=r(d("%s installed!","plugin"),e.pluginName);t.removeClass("updating-message").addClass("updated-message installed button-disabled").attr("aria-label",s).text(a),g.a11y.speak(h("Installation completed successfully.")),f.trigger("wp-plugin-install-success",e),e.activateUrl&&setTimeout(function(){g.updates.checkPluginDependencies({slug:e.slug})},1e3),"plugin-information-footer"===t.parent().attr("id")&&g.updates.setCardButtonStatus({status:"installed-plugin",slug:e.slug,removeClasses:"updating-message",addClasses:"updated-message installed button-disabled",text:a,ariaLabel:s})},g.updates.installPluginError=function(e){var t,a=c(".plugin-card-"+e.slug+", #plugin-information-footer"),s=a.find(".install-now"),n=h("Installation failed."),i=r(d("%s installation failed","plugin"),s.data("name"));g.updates.isValidResponse(e,"install")&&!g.updates.maybeHandleCredentialError(e,"install-plugin")&&(t=r(h("Installation failed: %s"),e.errorMessage),a.addClass("plugin-card-update-failed").append('<div class="notice notice-error notice-alt is-dismissible"><p>'+t+"</p></div>"),a.on("click",".notice.is-dismissible .notice-dismiss",function(){setTimeout(function(){a.removeClass("plugin-card-update-failed").find(".column-name a").trigger("focus")},200)}),s.removeClass("updating-message").addClass("button-disabled").attr("aria-label",i).text(n),g.a11y.speak(t,"assertive"),g.updates.setCardButtonStatus({status:"plugin-install-failed",slug:e.slug,removeClasses:"updating-message",addClasses:"button-disabled",text:n,ariaLabel:i}),f.trigger("wp-plugin-install-error",e))},g.updates.checkPluginDependencies=function(e){return e=_.extend({success:g.updates.checkPluginDependenciesSuccess,error:g.updates.checkPluginDependenciesError},e),g.a11y.speak(h("Checking plugin dependencies... please wait.")),f.trigger("wp-checking-plugin-dependencies",e),g.updates.ajax("check_plugin_dependencies",e)},g.updates.checkPluginDependenciesSuccess=function(e){var t,a,s=c(".plugin-card-"+e.slug+", #plugin-information-footer").find(".install-now");s.removeClass("install-now installed button-disabled updated-message").addClass("activate-now button-primary").attr("href",e.activateUrl),g.a11y.speak(h("Plugin dependencies check completed successfully.")),f.trigger("wp-check-plugin-dependencies-success",e),("plugins-network"===pagenow?(t=d("Network Activate","plugin"),a=r(d("Network Activate %s","plugin"),e.pluginName),s.attr("aria-label",a)):(t=d("Activate","plugin"),a=r(d("Activate %s","plugin"),e.pluginName),s.attr("aria-label",a).attr("data-name",e.pluginName).attr("data-slug",e.slug).attr("data-plugin",e.plugin))).text(t),"plugin-information-footer"===s.parent().attr("id")&&g.updates.setCardButtonStatus({status:"dependencies-check-success",slug:e.slug,removeClasses:"install-now installed button-disabled updated-message",addClasses:"activate-now button-primary",text:t,ariaLabel:a,pluginName:e.pluginName,plugin:e.plugin,href:e.activateUrl})},g.updates.checkPluginDependenciesError=function(e){var t,a=c(".plugin-card-"+e.slug+", #plugin-information-footer").find(".install-now"),s=d("Activate","plugin"),n=r(d("Cannot activate %1$s. %2$s","plugin"),e.pluginName,e.errorMessage);g.updates.isValidResponse(e,"check-dependencies")&&(t=r(h("Activation failed: %s"),e.errorMessage),g.a11y.speak(t,"assertive"),f.trigger("wp-check-plugin-dependencies-error",e),a.removeClass("install-now installed updated-message").addClass("activate-now button-primary").attr("aria-label",n).text(s),"plugin-information-footer"===a.parent().attr("id"))&&g.updates.setCardButtonStatus({status:"dependencies-check-failed",slug:e.slug,removeClasses:"install-now installed updated-message",addClasses:"activate-now button-primary",text:s,ariaLabel:n})},g.updates.activatePlugin=function(e){var t=c(".plugin-card-"+e.slug+", #plugin-information-footer").find(".activate-now, .activating-message");return e=_.extend({success:g.updates.activatePluginSuccess,error:g.updates.activatePluginError},e),g.a11y.speak(h("Activating... please wait.")),f.trigger("wp-activating-plugin",e),"plugin-information-footer"===t.parent().attr("id")&&g.updates.setCardButtonStatus({status:"activating-plugin",slug:e.slug,removeClasses:"installed updated-message button-primary",addClasses:"activating-message",text:h("Activating..."),ariaLabel:r(d("Activating %s","plugin"),e.name)}),g.updates.ajax("activate-plugin",e)},g.updates.activatePluginSuccess=function(e){var t=c(".plugin-card-"+e.slug+", #plugin-information-footer").find(".activating-message"),a=d("Activated!","plugin"),s=r("%s activated successfully.",e.pluginName);g.a11y.speak(h("Activation completed successfully.")),f.trigger("wp-plugin-activate-success",e),t.removeClass("activating-message").addClass("activated-message button-disabled").attr("aria-label",s).text(a),"plugin-information-footer"===t.parent().attr("id")&&g.updates.setCardButtonStatus({status:"activated-plugin",slug:e.slug,removeClasses:"activating-message",addClasses:"activated-message button-disabled",text:a,ariaLabel:s}),setTimeout(function(){t.removeClass("activated-message").text(d("Active","plugin")),"plugin-information-footer"===t.parent().attr("id")&&g.updates.setCardButtonStatus({status:"plugin-active",slug:e.slug,removeClasses:"activated-message",text:d("Active","plugin"),ariaLabel:r("%s is active.",e.pluginName)})},1e3)},g.updates.activatePluginError=function(e){var t,a=c(".plugin-card-"+e.slug+", #plugin-information-footer").find(".activating-message"),s=h("Activation failed."),n=r(d("%s activation failed","plugin"),e.pluginName);g.updates.isValidResponse(e,"activate")&&(t=r(h("Activation failed: %s"),e.errorMessage),g.a11y.speak(t,"assertive"),f.trigger("wp-plugin-activate-error",e),a.removeClass("install-now installed activating-message").addClass("button-disabled").attr("aria-label",n).text(s),"plugin-information-footer"===a.parent().attr("id"))&&g.updates.setCardButtonStatus({status:"plugin-activation-failed",slug:e.slug,removeClasses:"install-now installed activating-message",addClasses:"button-disabled",text:s,ariaLabel:n})},g.updates.installImporterSuccess=function(e){g.updates.addAdminNotice({id:"install-success",className:"notice-success is-dismissible",message:r(h('Importer installed successfully. <a href="%s">Run importer</a>'),e.activateUrl+"&from=import")}),c('[data-slug="'+e.slug+'"]').removeClass("install-now updating-message").addClass("activate-now").attr({href:e.activateUrl+"&from=import","aria-label":r(h("Run %s"),e.pluginName)}).text(h("Run Importer")),g.a11y.speak(h("Installation completed successfully.")),f.trigger("wp-importer-install-success",e)},g.updates.installImporterError=function(e){var t=r(h("Installation failed: %s"),e.errorMessage),a=c('[data-slug="'+e.slug+'"]'),s=a.data("name");g.updates.isValidResponse(e,"install")&&!g.updates.maybeHandleCredentialError(e,"install-plugin")&&(g.updates.addAdminNotice({id:e.errorCode,className:"notice-error is-dismissible",message:t}),a.removeClass("updating-message").attr("aria-label",r(d("Install %s now","plugin"),s)).text(d("Install Now","plugin")),g.a11y.speak(t,"assertive"),f.trigger("wp-importer-install-error",e))},g.updates.deletePlugin=function(e){var t=c('[data-plugin="'+e.plugin+'"]').find(".row-actions a.delete");return e=_.extend({success:g.updates.deletePluginSuccess,error:g.updates.deletePluginError},e),t.html()!==h("Deleting...")&&t.data("originaltext",t.html()).text(h("Deleting...")),g.a11y.speak(h("Deleting...")),f.trigger("wp-plugin-deleting",e),g.updates.ajax("delete-plugin",e)},g.updates.deletePluginSuccess=function(u){c('[data-plugin="'+u.plugin+'"]').css({backgroundColor:"#faafaa"}).fadeOut(350,function(){var e=c("#bulk-action-form"),t=c(".subsubsub"),a=c(this),s=t.find('[aria-current="page"]'),n=c(".displaying-num"),i=e.find("thead th:not(.hidden), thead td").length,l=g.template("item-deleted-row"),d=m.plugins;a.hasClass("plugin-update-tr")||a.after(l({slug:u.slug,plugin:u.plugin,colspan:i,name:u.pluginName})),a.remove(),-1!==_.indexOf(d.upgrade,u.plugin)&&(d.upgrade=_.without(d.upgrade,u.plugin),g.updates.decrementCount("plugin")),-1!==_.indexOf(d.inactive,u.plugin)&&(d.inactive=_.without(d.inactive,u.plugin),d.inactive.length?t.find(".inactive .count").text("("+d.inactive.length+")"):t.find(".inactive").remove()),-1!==_.indexOf(d.active,u.plugin)&&(d.active=_.without(d.active,u.plugin),d.active.length?t.find(".active .count").text("("+d.active.length+")"):t.find(".active").remove()),-1!==_.indexOf(d.recently_activated,u.plugin)&&(d.recently_activated=_.without(d.recently_activated,u.plugin),d.recently_activated.length?t.find(".recently_activated .count").text("("+d.recently_activated.length+")"):t.find(".recently_activated").remove()),-1!==_.indexOf(d["auto-update-enabled"],u.plugin)&&(d["auto-update-enabled"]=_.without(d["auto-update-enabled"],u.plugin),d["auto-update-enabled"].length?t.find(".auto-update-enabled .count").text("("+d["auto-update-enabled"].length+")"):t.find(".auto-update-enabled").remove()),-1!==_.indexOf(d["auto-update-disabled"],u.plugin)&&(d["auto-update-disabled"]=_.without(d["auto-update-disabled"],u.plugin),d["auto-update-disabled"].length?t.find(".auto-update-disabled .count").text("("+d["auto-update-disabled"].length+")"):t.find(".auto-update-disabled").remove()),d.all=_.without(d.all,u.plugin),d.all.length?t.find(".all .count").text("("+d.all.length+")"):(e.find(".tablenav").css({visibility:"hidden"}),t.find(".all").remove(),e.find("tr.no-items").length||e.find("#the-list").append('<tr class="no-items"><td class="colspanchange" colspan="'+i+'">'+h("No plugins are currently available.")+"</td></tr>")),n.length&&s.length&&(l=d[s.parent("li").attr("class")].length,n.text(r(o("%s item","%s items",l,"plugin/plugins"),l)))}),g.a11y.speak(d("Deleted!","plugin")),f.trigger("wp-plugin-delete-success",u)},g.updates.deletePluginError=function(e){var t,a=g.template("item-update-row"),s=g.updates.adminNotice({className:"update-message notice-error notice-alt",message:e.errorMessage}),n=e.plugin?(t=c('tr.inactive[data-plugin="'+e.plugin+'"]')).siblings('[data-plugin="'+e.plugin+'"]'):(t=c('tr.inactive[data-slug="'+e.slug+'"]')).siblings('[data-slug="'+e.slug+'"]');g.updates.isValidResponse(e,"delete")&&!g.updates.maybeHandleCredentialError(e,"delete-plugin")&&(n.length?(n.find(".notice-error").remove(),n.find(".plugin-update").append(s)):t.addClass("update").after(a({slug:e.slug,plugin:e.plugin||e.slug,colspan:c("#bulk-action-form").find("thead th:not(.hidden), thead td").length,content:s})),f.trigger("wp-plugin-delete-error",e))},g.updates.updateTheme=function(e){var t;return e=_.extend({success:g.updates.updateThemeSuccess,error:g.updates.updateThemeError},e),(t=("themes-network"===pagenow?c('[data-slug="'+e.slug+'"]').find(".update-message").removeClass("notice-error").addClass("updating-message notice-warning"):(t="customize"===pagenow?((t=c('[data-slug="'+e.slug+'"].notice').removeClass("notice-large")).find("h3").remove(),t.add(c("#customize-control-installed_theme_"+e.slug).find(".update-message"))):((t=c("#update-theme").closest(".notice").removeClass("notice-large")).find("h3").remove(),t.add(c('[data-slug="'+e.slug+'"]').find(".update-message")))).addClass("updating-message")).find("p")).html()!==h("Updating...")&&t.data("originaltext",t.html()),g.a11y.speak(h("Updating... please wait.")),t.text(h("Updating...")),f.trigger("wp-theme-updating",e),g.updates.ajax("update-theme",e)},g.updates.updateThemeSuccess=function(e){var t,a,s=c("body.modal-open").length,n=c('[data-slug="'+e.slug+'"]'),i={className:"updated-message notice-success notice-alt",message:d("Updated!","theme")};"customize"===pagenow?((n=c(".updating-message").siblings(".theme-name")).length&&(a=n.html().replace(e.oldVersion,e.newVersion),n.html(a)),t=c(".theme-info .notice").add(g.customize.control("installed_theme_"+e.slug).container.find(".theme").find(".update-message"))):"themes-network"===pagenow?(t=n.find(".update-message"),a=n.find(".theme-version-author-uri").html().replace(e.oldVersion,e.newVersion),n.find(".theme-version-author-uri").html(a),n.find(".auto-update-time").empty()):(t=c(".theme-info .notice").add(n.find(".update-message")),s?(c(".load-customize:visible").trigger("focus"),c(".theme-info .theme-autoupdate").find(".auto-update-time").empty()):n.find(".load-customize").trigger("focus")),g.updates.addAdminNotice(_.extend({selector:t},i)),g.a11y.speak(h("Update completed successfully.")),g.updates.decrementCount("theme"),f.trigger("wp-theme-update-success",e),s&&"customize"!==pagenow&&c(".theme-info .theme-author").after(g.updates.adminNotice(i))},g.updates.updateThemeError=function(e){var t,a=c('[data-slug="'+e.slug+'"]'),s=r(h("Update failed: %s"),e.errorMessage);g.updates.isValidResponse(e,"update")&&!g.updates.maybeHandleCredentialError(e,"update-theme")&&("customize"===pagenow&&(a=g.customize.control("installed_theme_"+e.slug).container.find(".theme")),"themes-network"===pagenow?t=a.find(".update-message "):(t=c(".theme-info .notice").add(a.find(".notice")),(c("body.modal-open").length?c(".load-customize:visible"):a.find(".load-customize")).trigger("focus")),g.updates.addAdminNotice({selector:t,className:"update-message notice-error notice-alt is-dismissible",message:s}),g.a11y.speak(s),f.trigger("wp-theme-update-error",e))},g.updates.installTheme=function(e){var t=c('.theme-install[data-slug="'+e.slug+'"]');return e=_.extend({success:g.updates.installThemeSuccess,error:g.updates.installThemeError},e),t.addClass("updating-message"),t.parents(".theme").addClass("focus"),t.html()!==h("Installing...")&&t.data("originaltext",t.html()),t.attr("aria-label",r(d("Installing %s...","theme"),t.data("name"))).text(h("Installing...")),g.a11y.speak(h("Installing... please wait.")),c('.install-theme-info, [data-slug="'+e.slug+'"]').removeClass("theme-install-failed").find(".notice.notice-error").remove(),f.trigger("wp-theme-installing",e),g.updates.ajax("install-theme",e)},g.updates.installThemeSuccess=function(e){var t,a=c(".wp-full-overlay-header, [data-slug="+e.slug+"]");f.trigger("wp-theme-install-success",e),t=a.find(".button-primary").removeClass("updating-message").addClass("updated-message disabled").attr("aria-label",r(d("%s installed!","theme"),e.themeName)).text(d("Installed!","theme")),g.a11y.speak(h("Installation completed successfully.")),setTimeout(function(){e.activateUrl&&(t.attr("href",e.activateUrl).removeClass("theme-install updated-message disabled").addClass("activate"),"themes-network"===pagenow?t.attr("aria-label",r(d("Network Activate %s","theme"),e.themeName)).text(h("Network Enable")):t.attr("aria-label",r(d("Activate %s","theme"),e.themeName)).text(d("Activate","theme"))),e.customizeUrl&&t.siblings(".preview").replaceWith(function(){return c("<a>").attr("href",e.customizeUrl).addClass("button load-customize").text(h("Live Preview"))})},1e3)},g.updates.installThemeError=function(e){var t,a=r(h("Installation failed: %s"),e.errorMessage),s=g.updates.adminNotice({className:"update-message notice-error notice-alt",message:a});g.updates.isValidResponse(e,"install")&&!g.updates.maybeHandleCredentialError(e,"install-theme")&&("customize"===pagenow?(f.find("body").hasClass("modal-open")?(t=c('.theme-install[data-slug="'+e.slug+'"]'),c(".theme-overlay .theme-info").prepend(s)):(t=c('.theme-install[data-slug="'+e.slug+'"]')).closest(".theme").addClass("theme-install-failed").append(s),g.customize.notifications.remove("theme_installing")):f.find("body").hasClass("full-overlay-active")?(t=c('.theme-install[data-slug="'+e.slug+'"]'),c(".install-theme-info").prepend(s)):t=c('[data-slug="'+e.slug+'"]').removeClass("focus").addClass("theme-install-failed").append(s).find(".theme-install"),t.removeClass("updating-message").attr("aria-label",r(d("%s installation failed","theme"),t.data("name"))).text(h("Installation failed.")),g.a11y.speak(a,"assertive"),f.trigger("wp-theme-install-error",e))},g.updates.deleteTheme=function(e){var t;return"themes"===pagenow?t=c(".theme-actions .delete-theme"):"themes-network"===pagenow&&(t=c('[data-slug="'+e.slug+'"]').find(".row-actions a.delete")),e=_.extend({success:g.updates.deleteThemeSuccess,error:g.updates.deleteThemeError},e),t&&t.html()!==h("Deleting...")&&t.data("originaltext",t.html()).text(h("Deleting...")),g.a11y.speak(h("Deleting...")),c(".theme-info .update-message").remove(),f.trigger("wp-theme-deleting",e),g.updates.ajax("delete-theme",e)},g.updates.deleteThemeSuccess=function(n){var e=c('[data-slug="'+n.slug+'"]');"themes-network"===pagenow&&e.css({backgroundColor:"#faafaa"}).fadeOut(350,function(){var e=c(".subsubsub"),t=c(this),a=m.themes,s=g.template("item-deleted-row");t.hasClass("plugin-update-tr")||t.after(s({slug:n.slug,colspan:c("#bulk-action-form").find("thead th:not(.hidden), thead td").length,name:t.find(".theme-title strong").text()})),t.remove(),-1!==_.indexOf(a.upgrade,n.slug)&&(a.upgrade=_.without(a.upgrade,n.slug),g.updates.decrementCount("theme")),-1!==_.indexOf(a.disabled,n.slug)&&(a.disabled=_.without(a.disabled,n.slug),a.disabled.length?e.find(".disabled .count").text("("+a.disabled.length+")"):e.find(".disabled").remove()),-1!==_.indexOf(a["auto-update-enabled"],n.slug)&&(a["auto-update-enabled"]=_.without(a["auto-update-enabled"],n.slug),a["auto-update-enabled"].length?e.find(".auto-update-enabled .count").text("("+a["auto-update-enabled"].length+")"):e.find(".auto-update-enabled").remove()),-1!==_.indexOf(a["auto-update-disabled"],n.slug)&&(a["auto-update-disabled"]=_.without(a["auto-update-disabled"],n.slug),a["auto-update-disabled"].length?e.find(".auto-update-disabled .count").text("("+a["auto-update-disabled"].length+")"):e.find(".auto-update-disabled").remove()),a.all=_.without(a.all,n.slug),e.find(".all .count").text("("+a.all.length+")")}),"themes"===pagenow&&_.find(_wpThemeSettings.themes,{id:n.slug}).hasUpdate&&g.updates.decrementCount("theme"),g.a11y.speak(d("Deleted!","theme")),f.trigger("wp-theme-delete-success",n)},g.updates.deleteThemeError=function(e){var t=c('tr.inactive[data-slug="'+e.slug+'"]'),a=c(".theme-actions .delete-theme"),s=g.template("item-update-row"),n=t.siblings("#"+e.slug+"-update"),i=r(h("Deletion failed: %s"),e.errorMessage),l=g.updates.adminNotice({className:"update-message notice-error notice-alt",message:i});g.updates.maybeHandleCredentialError(e,"delete-theme")||("themes-network"===pagenow?n.length?(n.find(".notice-error").remove(),n.find(".plugin-update").append(l)):t.addClass("update").after(s({slug:e.slug,colspan:c("#bulk-action-form").find("thead th:not(.hidden), thead td").length,content:l})):c(".theme-info .theme-description").before(l),a.html(a.data("originaltext")),g.a11y.speak(i,"assertive"),f.trigger("wp-theme-delete-error",e))},g.updates._addCallbacks=function(e,t){return"import"===pagenow&&"install-plugin"===t&&(e.success=g.updates.installImporterSuccess,e.error=g.updates.installImporterError),e},g.updates.queueChecker=function(){var e;if(!g.updates.ajaxLocked&&g.updates.queue.length)switch((e=g.updates.queue.shift()).action){case"install-plugin":g.updates.installPlugin(e.data);break;case"update-plugin":g.updates.updatePlugin(e.data);break;case"delete-plugin":g.updates.deletePlugin(e.data);break;case"install-theme":g.updates.installTheme(e.data);break;case"update-theme":g.updates.updateTheme(e.data);break;case"delete-theme":g.updates.deleteTheme(e.data)}},g.updates.requestFilesystemCredentials=function(e){!1===g.updates.filesystemCredentials.available&&(e&&!g.updates.$elToReturnFocusToFromCredentialsModal&&(g.updates.$elToReturnFocusToFromCredentialsModal=c(e.target)),g.updates.ajaxLocked=!0,g.updates.requestForCredentialsModalOpen())},g.updates.maybeRequestFilesystemCredentials=function(e){g.updates.shouldRequestFilesystemCredentials&&!g.updates.ajaxLocked&&g.updates.requestFilesystemCredentials(e)},g.updates.keydown=function(e){27===e.keyCode?g.updates.requestForCredentialsModalCancel():9===e.keyCode&&("upgrade"!==e.target.id||e.shiftKey?"hostname"===e.target.id&&e.shiftKey&&(c("#upgrade").trigger("focus"),e.preventDefault()):(c("#hostname").trigger("focus"),e.preventDefault()))},g.updates.requestForCredentialsModalOpen=function(){var e=c("#request-filesystem-credentials-dialog");c("body").addClass("modal-open"),e.show(),e.find("input:enabled:first").trigger("focus"),e.on("keydown",g.updates.keydown)},g.updates.requestForCredentialsModalClose=function(){c("#request-filesystem-credentials-dialog").hide(),c("body").removeClass("modal-open"),g.updates.$elToReturnFocusToFromCredentialsModal&&g.updates.$elToReturnFocusToFromCredentialsModal.trigger("focus")},g.updates.requestForCredentialsModalCancel=function(){(g.updates.ajaxLocked||g.updates.queue.length)&&(_.each(g.updates.queue,function(e){f.trigger("credential-modal-cancel",e)}),g.updates.ajaxLocked=!1,g.updates.queue=[],g.updates.requestForCredentialsModalClose())},g.updates.showErrorInCredentialsForm=function(e){var t=c("#request-filesystem-credentials-form");t.find(".notice").remove(),t.find("#request-filesystem-credentials-title").after('<div class="notice notice-alt notice-error"><p>'+e+"</p></div>")},g.updates.credentialError=function(e,t){e=g.updates._addCallbacks(e,t),g.updates.queue.unshift({action:t,data:e}),g.updates.filesystemCredentials.available=!1,g.updates.showErrorInCredentialsForm(e.errorMessage),g.updates.requestFilesystemCredentials()},g.updates.maybeHandleCredentialError=function(e,t){return!(!g.updates.shouldRequestFilesystemCredentials||!e.errorCode||"unable_to_connect_to_filesystem"!==e.errorCode||(g.updates.credentialError(e,t),0))},g.updates.isValidResponse=function(e,t){var a,s=h("Something went wrong.");if(_.isObject(e)&&!_.isFunction(e.always))return!0;switch(_.isString(e)&&"-1"===e?s=h("An error has occurred. Please reload the page and try again."):_.isString(e)?s=e:void 0!==e.readyState&&0===e.readyState?s=h("Connection lost or the server is busy. Please try again later."):_.isString(e.responseText)&&""!==e.responseText?s=e.responseText:_.isString(e.statusText)&&(s=e.statusText),t){case"update":a=h("Update failed: %s");break;case"install":a=h("Installation failed: %s");break;case"check-dependencies":a=h("Dependencies check failed: %s");break;case"activate":a=h("Activation failed: %s");break;case"delete":a=h("Deletion failed: %s")}return s=s.replace(/<[\/a-z][^<>]*>/gi,""),a=a.replace("%s",s),g.updates.addAdminNotice({id:"unknown_error",className:"notice-error is-dismissible",message:_.escape(a)}),g.updates.ajaxLocked=!1,g.updates.queue=[],c(".button.updating-message").removeClass("updating-message").removeAttr("aria-label").prop("disabled",!0).text(h("Update failed.")),c(".updating-message:not(.button):not(.thickbox)").removeClass("updating-message notice-warning").addClass("notice-error").find("p").removeAttr("aria-label").text(a),g.a11y.speak(a,"assertive"),!1},g.updates.beforeunload=function(){if(g.updates.ajaxLocked)return h("Updates may not complete if you navigate away from this page.")},c(function(){var i=c("#plugin-filter, #plugin-information-footer"),o=c("#bulk-action-form"),e=c("#request-filesystem-credentials-form"),t=c("#request-filesystem-credentials-dialog"),a=c(".plugins-php .wp-filter-search"),s=c(".plugin-install-php .wp-filter-search");(m=_.extend(m,window._wpUpdatesItemCounts||{})).totals&&g.updates.refreshCount(),g.updates.shouldRequestFilesystemCredentials=0<t.length,t.on("submit","form",function(e){e.preventDefault(),g.updates.filesystemCredentials.ftp.hostname=c("#hostname").val(),g.updates.filesystemCredentials.ftp.username=c("#username").val(),g.updates.filesystemCredentials.ftp.password=c("#password").val(),g.updates.filesystemCredentials.ftp.connectionType=c('input[name="connection_type"]:checked').val(),g.updates.filesystemCredentials.ssh.publicKey=c("#public_key").val(),g.updates.filesystemCredentials.ssh.privateKey=c("#private_key").val(),g.updates.filesystemCredentials.fsNonce=c("#_fs_nonce").val(),g.updates.filesystemCredentials.available=!0,g.updates.ajaxLocked=!1,g.updates.queueChecker(),g.updates.requestForCredentialsModalClose()}),t.on("click",'[data-js-action="close"], .notification-dialog-background',g.updates.requestForCredentialsModalCancel),e.on("change",'input[name="connection_type"]',function(){c("#ssh-keys").toggleClass("hidden","ssh"!==c(this).val())}).trigger("change"),f.on("credential-modal-cancel",function(e,t){var a,s=c(".updating-message");"import"===pagenow?s.removeClass("updating-message"):"plugins"===pagenow||"plugins-network"===pagenow?"update-plugin"===t.action?a=c('tr[data-plugin="'+t.data.plugin+'"]').find(".update-message"):"delete-plugin"===t.action&&(a=c('[data-plugin="'+t.data.plugin+'"]').find(".row-actions a.delete")):"themes"===pagenow||"themes-network"===pagenow?"update-theme"===t.action?a=c('[data-slug="'+t.data.slug+'"]').find(".update-message"):"delete-theme"===t.action&&"themes-network"===pagenow?a=c('[data-slug="'+t.data.slug+'"]').find(".row-actions a.delete"):"delete-theme"===t.action&&"themes"===pagenow&&(a=c(".theme-actions .delete-theme")):a=s,a&&a.hasClass("updating-message")&&(void 0===(s=a.data("originaltext"))&&(s=c("<p>").html(a.find("p").data("originaltext"))),a.removeClass("updating-message").html(s),"plugin-install"!==pagenow&&"plugin-install-network"!==pagenow||("update-plugin"===t.action?a.attr("aria-label",r(d("Update %s now","plugin"),a.data("name"))):"install-plugin"===t.action&&a.attr("aria-label",r(d("Install %s now","plugin"),a.data("name"))))),g.a11y.speak(h("Update canceled."))}),o.on("click","[data-plugin] .update-link",function(e){var t=c(e.target),a=t.parents("tr");e.preventDefault(),t.hasClass("updating-message")||t.hasClass("button-disabled")||(g.updates.maybeRequestFilesystemCredentials(e),g.updates.$elToReturnFocusToFromCredentialsModal=a.find(".check-column input"),g.updates.updatePlugin({plugin:a.data("plugin"),slug:a.data("slug")}))}),i.on("click",".update-now",function(e){var t=c(e.target);e.preventDefault(),t.hasClass("updating-message")||t.hasClass("button-disabled")||(g.updates.maybeRequestFilesystemCredentials(e),g.updates.updatePlugin({plugin:t.data("plugin"),slug:t.data("slug")}))}),i.on("click",".install-now",function(e){var t=c(e.target);e.preventDefault(),t.hasClass("updating-message")||t.hasClass("button-disabled")||(g.updates.shouldRequestFilesystemCredentials&&!g.updates.ajaxLocked&&(g.updates.requestFilesystemCredentials(e),f.on("credential-modal-cancel",function(){c(".install-now.updating-message").removeClass("updating-message").text(d("Install Now","plugin")),g.a11y.speak(h("Update canceled."))})),g.updates.installPlugin({slug:t.data("slug")}))}),f.on("click","#plugin-information-footer .activate-now",function(e){e.preventDefault(),window.parent.location.href=c(e.target).attr("href")}),f.on("click",".importer-item .install-now",function(e){var t=c(e.target),a=c(this).data("name");e.preventDefault(),t.hasClass("updating-message")||(g.updates.shouldRequestFilesystemCredentials&&!g.updates.ajaxLocked&&(g.updates.requestFilesystemCredentials(e),f.on("credential-modal-cancel",function(){t.removeClass("updating-message").attr("aria-label",r(d("Install %s now","plugin"),a)).text(d("Install Now","plugin")),g.a11y.speak(h("Update canceled."))})),g.updates.installPlugin({slug:t.data("slug"),pagenow:pagenow,success:g.updates.installImporterSuccess,error:g.updates.installImporterError}))}),o.on("click","[data-plugin] a.delete",function(e){var t=c(e.target).parents("tr"),a=t.hasClass("is-uninstallable")?r(h("Are you sure you want to delete %s and its data?"),t.find(".plugin-title strong").text()):r(h("Are you sure you want to delete %s?"),t.find(".plugin-title strong").text());e.preventDefault(),window.confirm(a)&&(g.updates.maybeRequestFilesystemCredentials(e),g.updates.deletePlugin({plugin:t.data("plugin"),slug:t.data("slug")}))}),f.on("click",".themes-php.network-admin .update-link",function(e){var t=c(e.target),a=t.parents("tr");e.preventDefault(),t.hasClass("updating-message")||t.hasClass("button-disabled")||(g.updates.maybeRequestFilesystemCredentials(e),g.updates.$elToReturnFocusToFromCredentialsModal=a.find(".check-column input"),g.updates.updateTheme({slug:a.data("slug")}))}),f.on("click",".themes-php.network-admin a.delete",function(e){var t=c(e.target).parents("tr"),a=r(h("Are you sure you want to delete %s?"),t.find(".theme-title strong").text());e.preventDefault(),window.confirm(a)&&(g.updates.maybeRequestFilesystemCredentials(e),g.updates.deleteTheme({slug:t.data("slug")}))}),o.on("click",'[type="submit"]:not([name="clear-recent-list"])',function(e){var t,s,n=c(e.target).siblings("select").val(),a=o.find('input[name="checked[]"]:checked'),i=0,l=0,d=[];switch(pagenow){case"plugins":case"plugins-network":t="plugin";break;case"themes-network":t="theme";break;default:return}if(!a.length)return e.preventDefault(),c("html, body").animate({scrollTop:0}),g.updates.addAdminNotice({id:"no-items-selected",className:"notice-error is-dismissible",message:h("Please select at least one item to perform this action on.")});switch(n){case"update-selected":s=n.replace("selected",t);break;case"delete-selected":var u=h("plugin"===t?"Are you sure you want to delete the selected plugins and their data?":"Caution: These themes may be active on other sites in the network. Are you sure you want to proceed?");if(!window.confirm(u))return void e.preventDefault();s=n.replace("selected",t);break;default:return}g.updates.maybeRequestFilesystemCredentials(e),e.preventDefault(),o.find('.manage-column [type="checkbox"]').prop("checked",!1),f.trigger("wp-"+t+"-bulk-"+n,a),a.each(function(e,t){var t=c(t),a=t.parents("tr");"update-selected"!==n||a.hasClass("update")&&!a.find("notice-error").length?"update-selected"===n&&a.hasClass("is-enqueued")||(a.addClass("is-enqueued"),g.updates.queue.push({action:s,data:{plugin:a.data("plugin"),slug:a.data("slug")}})):t.prop("checked",!1)}),f.on("wp-plugin-update-success wp-plugin-update-error wp-theme-update-success wp-theme-update-error",function(e,t){var a,s=c('[data-slug="'+t.slug+'"]');"wp-"+t.update+"-update-success"===e.type?i++:(e=t.pluginName||s.find(".column-primary strong").text(),l++,d.push(e+": "+t.errorMessage)),s.find('input[name="checked[]"]:checked').prop("checked",!1),g.updates.adminNotice=g.template("wp-bulk-updates-admin-notice"),g.updates.addAdminNotice({id:"bulk-action-notice",className:"bulk-action-notice",successes:i,errors:l,errorMessages:d,type:t.update}),a=c("#bulk-action-notice").on("click","button",function(){c(this).toggleClass("bulk-action-errors-collapsed").attr("aria-expanded",!c(this).hasClass("bulk-action-errors-collapsed")),a.find(".bulk-action-errors").toggleClass("hidden")}),0<l&&!g.updates.queue.length&&c("html, body").animate({scrollTop:0})}),f.on("wp-updates-notice-added",function(){g.updates.adminNotice=g.template("wp-updates-admin-notice")}),g.updates.queueChecker()}),s.length&&s.attr("aria-describedby","live-search-desc"),s.on("keyup input",_.debounce(function(e,t){var a=c(".plugin-install-search"),s={_ajax_nonce:g.updates.ajaxNonce,s:encodeURIComponent(e.target.value),tab:"search",type:c("#typeselector").val(),pagenow:pagenow},n=location.href.split("?")[0]+"?"+c.param(_.omit(s,["_ajax_nonce","pagenow"]));"keyup"===e.type&&27===e.which&&(e.target.value=""),g.updates.searchTerm===s.s&&"typechange"!==t||(i.empty(),g.updates.searchTerm=s.s,window.history&&window.history.replaceState&&window.history.replaceState(null,"",n),a.length||(a=c('<li class="plugin-install-search" />').append(c("<a />",{class:"current",href:n,text:h("Search Results")})),c(".wp-filter .filter-links .current").removeClass("current").parents(".filter-links").prepend(a),i.prev("p").remove(),c(".plugins-popular-tags-wrapper").remove()),void 0!==g.updates.searchRequest&&g.updates.searchRequest.abort(),c("body").addClass("loading-content"),g.updates.searchRequest=g.ajax.post("search-install-plugins",s).done(function(e){c("body").removeClass("loading-content"),i.append(e.items),delete g.updates.searchRequest,0===e.count?g.a11y.speak(h("You do not appear to have any plugins available at this time.")):g.a11y.speak(r(h("Number of plugins found: %d"),e.count))}))},1e3)),a.length&&a.attr("aria-describedby","live-search-desc"),a.on("keyup input",_.debounce(function(e){var s={_ajax_nonce:g.updates.ajaxNonce,s:encodeURIComponent(e.target.value),pagenow:pagenow,plugin_status:"all"};"keyup"===e.type&&27===e.which&&(e.target.value=""),g.updates.searchTerm!==s.s&&(g.updates.searchTerm=s.s,e=_.object(_.compact(_.map(location.search.slice(1).split("&"),function(e){if(e)return e.split("=")}))),s.plugin_status=e.plugin_status||"all",window.history&&window.history.replaceState&&window.history.replaceState(null,"",location.href.split("?")[0]+"?s="+s.s+"&plugin_status="+s.plugin_status),void 0!==g.updates.searchRequest&&g.updates.searchRequest.abort(),o.empty(),c("body").addClass("loading-content"),c(".subsubsub .current").removeClass("current"),g.updates.searchRequest=g.ajax.post("search-plugins",s).done(function(e){var t=c("<span />").addClass("subtitle").html(r(h("Search results for: %s"),"<strong>"+_.escape(decodeURIComponent(s.s))+"</strong>")),a=c(".wrap .subtitle");s.s.length?a.length?a.replaceWith(t):c(".wp-header-end").before(t):(a.remove(),c(".subsubsub ."+s.plugin_status+" a").addClass("current")),c("body").removeClass("loading-content"),o.append(e.items),delete g.updates.searchRequest,0===e.count?g.a11y.speak(h("No plugins found. Try a different search.")):g.a11y.speak(r(h("Number of plugins found: %d"),e.count))}))},500)),f.on("submit",".search-plugins",function(e){e.preventDefault(),c("input.wp-filter-search").trigger("input")}),f.on("click",".try-again",function(e){e.preventDefault(),s.trigger("input")}),c("#typeselector").on("change",function(){var e=c('input[name="s"]');e.val().length&&e.trigger("input","typechange")}),c("#plugin_update_from_iframe").on("click",function(e){var t=window.parent===window?null:window.parent;c.support.postMessage=!!window.postMessage,!1!==c.support.postMessage&&null!==t&&-1===window.parent.location.pathname.indexOf("update-core.php")&&(e.preventDefault(),e={action:"update-plugin",data:{plugin:c(this).data("plugin"),slug:c(this).data("slug")}},t.postMessage(JSON.stringify(e),window.location.origin))}),c(window).on("message",function(e){var t,e=e.originalEvent,a=document.location.protocol+"//"+document.location.host;if(e.origin===a){try{t=JSON.parse(e.data)}catch(e){return}if(t)if(void 0!==t.status&&void 0!==t.slug&&void 0!==t.text&&void 0!==t.ariaLabel&&(a=c(".plugin-card-"+t.slug).find('[data-slug="'+t.slug+'"]'),void 0!==t.removeClasses&&a.removeClass(t.removeClasses),void 0!==t.addClasses&&a.addClass(t.addClasses),""===t.ariaLabel?a.removeAttr("aria-label"):a.attr("aria-label",t.ariaLabel),"dependencies-check-success"===t.status&&a.attr("data-name",t.pluginName).attr("data-slug",t.slug).attr("data-plugin",t.plugin).attr("href",t.href),a.text(t.text)),void 0!==t.action)switch(t.action){case"decrementUpdateCount":g.updates.decrementCount(t.upgradeType);break;case"install-plugin":case"update-plugin":void 0!==t.data&&void 0!==t.data.slug&&(t.data=g.updates._addCallbacks(t.data,t.action),g.updates.queue.push(t),g.updates.queueChecker())}}}),c(window).on("beforeunload",g.updates.beforeunload),f.on("keydown",".column-auto-updates .toggle-auto-update, .theme-overlay .toggle-auto-update",function(e){32===e.which&&e.preventDefault()}),f.on("click keyup",".column-auto-updates .toggle-auto-update, .theme-overlay .toggle-auto-update",function(e){var l,d,u,o=c(this),r=o.attr("data-wp-action"),p=o.find(".label");if(("keyup"!==e.type||32===e.which)&&(u="themes"!==pagenow?o.closest(".column-auto-updates"):o.closest(".theme-autoupdate"),e.preventDefault(),"yes"!==o.attr("data-doing-ajax"))){switch(o.attr("data-doing-ajax","yes"),pagenow){case"plugins":case"plugins-network":d="plugin",l=o.closest("tr").attr("data-plugin");break;case"themes-network":d="theme",l=o.closest("tr").attr("data-slug");break;case"themes":d="theme",l=o.attr("data-slug")}u.find(".notice.notice-error").addClass("hidden"),"enable"===r?p.text(h("Enabling...")):p.text(h("Disabling...")),o.find(".dashicons-update").removeClass("hidden"),e={action:"toggle-auto-updates",_ajax_nonce:m.ajax_nonce,state:r,type:d,asset:l},c.post(window.ajaxurl,e).done(function(e){var t,a,s,n,i=o.attr("href");if(e.success){if("themes"!==pagenow){switch(n=c(".auto-update-enabled span"),t=c(".auto-update-disabled span"),a=parseInt(n.text().replace(/[^\d]+/g,""),10)||0,s=parseInt(t.text().replace(/[^\d]+/g,""),10)||0,r){case"enable":++a,--s;break;case"disable":--a,++s}a=Math.max(0,a),s=Math.max(0,s),n.text("("+a+")"),t.text("("+s+")")}"enable"===r?(o[0].hasAttribute("href")&&(i=i.replace("action=enable-auto-update","action=disable-auto-update"),o.attr("href",i)),o.attr("data-wp-action","disable"),p.text(h("Disable auto-updates")),u.find(".auto-update-time").removeClass("hidden"),g.a11y.speak(h("Auto-updates enabled"))):(o[0].hasAttribute("href")&&(i=i.replace("action=disable-auto-update","action=enable-auto-update"),o.attr("href",i)),o.attr("data-wp-action","enable"),p.text(h("Enable auto-updates")),u.find(".auto-update-time").addClass("hidden"),g.a11y.speak(h("Auto-updates disabled"))),f.trigger("wp-auto-update-setting-changed",{state:r,type:d,asset:l})}else n=e.data&&e.data.error?e.data.error:h("The request could not be completed."),u.find(".notice.notice-error").removeClass("hidden").find("p").text(n),g.a11y.speak(n,"assertive")}).fail(function(){u.find(".notice.notice-error").removeClass("hidden").find("p").text(h("The request could not be completed.")),g.a11y.speak(h("The request could not be completed."),"assertive")}).always(function(){o.removeAttr("data-doing-ajax").find(".dashicons-update").addClass("hidden")})}})})}(jQuery,window.wp,window._wpUpdatesSettings);