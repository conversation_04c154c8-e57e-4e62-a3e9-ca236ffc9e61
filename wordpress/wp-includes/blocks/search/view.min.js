import*as e from"@wordpress/interactivity";var t={d:(e,n)=>{for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const n=(e=>{var n={};return t.d(n,e),n})({getContext:()=>e.getContext,getElement:()=>e.getElement,store:()=>e.store}),{actions:r}=(0,n.store)("core/search",{state:{get ariaLabel(){const{isSearchInputVisible:e,ariaLabelCollapsed:t,ariaLabelExpanded:r}=(0,n.getContext)();return e?r:t},get ariaControls(){const{isSearchInputVisible:e,inputId:t}=(0,n.getContext)();return e?null:t},get type(){const{isSearchInputVisible:e}=(0,n.getContext)();return e?"submit":"button"},get tabindex(){const{isSearchInputVisible:e}=(0,n.getContext)();return e?"0":"-1"}},actions:{openSearchInput(e){const t=(0,n.getContext)(),{ref:r}=(0,n.getElement)();t.isSearchInputVisible||(e.preventDefault(),t.isSearchInputVisible=!0,r.parentElement.querySelector("input").focus())},closeSearchInput(){(0,n.getContext)().isSearchInputVisible=!1},handleSearchKeydown(e){const{ref:t}=(0,n.getElement)();"Escape"===e?.key&&(r.closeSearchInput(),t.querySelector("button").focus())},handleSearchFocusout(e){const{ref:t}=(0,n.getElement)();t.contains(e.relatedTarget)||e.target===window.document.activeElement||r.closeSearchInput()}}},{lock:!0});