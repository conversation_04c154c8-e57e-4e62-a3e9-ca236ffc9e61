{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/pullquote", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "text", "description": "Give special visual emphasis to a quote from your text.", "textdomain": "default", "attributes": {"value": {"type": "rich-text", "source": "rich-text", "selector": "p", "__experimentalRole": "content"}, "citation": {"type": "rich-text", "source": "rich-text", "selector": "cite", "__experimentalRole": "content"}, "textAlign": {"type": "string"}}, "supports": {"anchor": true, "align": ["left", "right", "wide", "full"], "color": {"gradients": true, "background": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "spacing": {"margin": true, "padding": true}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "__experimentalBorder": {"color": true, "radius": true, "style": true, "width": true, "__experimentalDefaultControls": {"color": true, "radius": true, "style": true, "width": true}}, "__experimentalStyle": {"typography": {"fontSize": "1.5em", "lineHeight": "1.6"}}, "interactivity": {"clientNavigation": true}}, "editorStyle": "wp-block-pullquote-editor", "style": "wp-block-pullquote"}