:root{
  --wp-admin-theme-color:#007cba;
  --wp-admin-theme-color--rgb:0, 124, 186;
  --wp-admin-theme-color-darker-10:#006ba1;
  --wp-admin-theme-color-darker-10--rgb:0, 107, 161;
  --wp-admin-theme-color-darker-20:#005a87;
  --wp-admin-theme-color-darker-20--rgb:0, 90, 135;
  --wp-admin-border-width-focus:2px;
  --wp-block-synced-color:#7a00df;
  --wp-block-synced-color--rgb:122, 0, 223;
  --wp-bound-block-color:#9747ff;
}
@media (min-resolution:192dpi){
  :root{
    --wp-admin-border-width-focus:1.5px;
  }
}

.block-editor-block-icon{
  align-items:center;
  display:flex;
  height:24px;
  justify-content:center;
  width:24px;
}
.block-editor-block-icon.has-colors svg{
  fill:currentColor;
}
@media (forced-colors:active){
  .block-editor-block-icon.has-colors svg{
    fill:CanvasText;
  }
}
.block-editor-block-icon svg{
  max-height:24px;
  max-width:24px;
  min-height:20px;
  min-width:20px;
}

.block-editor-block-styles .block-editor-block-list__block{
  margin:0;
}
@keyframes selection-overlay__fade-in-animation{
  0%{
    opacity:0;
  }
  to{
    opacity:.4;
  }
}
:root .block-editor-block-list__layout::selection,:root .has-multi-selection .block-editor-block-list__layout::selection,_::-webkit-full-page-media,_:future{
  background-color:initial;
}
.block-editor-block-list__layout{
  position:relative;
}
.block-editor-block-list__layout:where(.block-editor-block-list__block.is-multi-selected:not(.is-partially-selected)){
  border-radius:2px;
}
.block-editor-block-list__layout .block-editor-block-list__block.is-multi-selected:not(.is-partially-selected) ::selection,.block-editor-block-list__layout .block-editor-block-list__block.is-multi-selected:not(.is-partially-selected)::selection{
  background:#0000;
}
.block-editor-block-list__layout .block-editor-block-list__block.is-multi-selected:not(.is-partially-selected):after{
  animation:selection-overlay__fade-in-animation .1s ease-out;
  animation-fill-mode:forwards;
  background:var(--wp-admin-theme-color);
  border-radius:2px;
  bottom:0;
  content:"";
  left:0;
  opacity:.4;
  outline:2px solid #0000;
  pointer-events:none;
  position:absolute;
  right:0;
  top:0;
  z-index:1;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-block-list__layout .block-editor-block-list__block.is-multi-selected:not(.is-partially-selected):after{
    animation-delay:0s;
    animation-duration:1ms;
  }
}
.block-editor-block-list__layout .block-editor-block-list__block.is-multi-selected:not(.is-partially-selected).is-highlighted:after{
  box-shadow:none;
}
.block-editor-block-list__layout .block-editor-block-list__block.is-highlighted,.block-editor-block-list__layout .block-editor-block-list__block.is-highlighted~.is-multi-selected,.block-editor-block-list__layout .block-editor-block-list__block:not([contenteditable=true]):focus,.block-editor-block-list__layout.is-navigate-mode .block-editor-block-list__block.is-selected{
  outline:none;
}
.block-editor-block-list__layout .block-editor-block-list__block.is-highlighted:after,.block-editor-block-list__layout .block-editor-block-list__block.is-highlighted~.is-multi-selected:after,.block-editor-block-list__layout .block-editor-block-list__block:not([contenteditable=true]):focus:after,.block-editor-block-list__layout.is-navigate-mode .block-editor-block-list__block.is-selected:after{
  border-radius:1px;
  bottom:1px;
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  content:"";
  left:1px;
  outline:2px solid #0000;
  pointer-events:none;
  position:absolute;
  right:1px;
  top:1px;
  z-index:1;
}
.is-dark-theme .block-editor-block-list__layout .block-editor-block-list__block.is-highlighted:after,.is-dark-theme .block-editor-block-list__layout .block-editor-block-list__block.is-highlighted~.is-multi-selected:after,.is-dark-theme .block-editor-block-list__layout .block-editor-block-list__block:not([contenteditable=true]):focus:after,.is-dark-theme .block-editor-block-list__layout.is-navigate-mode .block-editor-block-list__block.is-selected:after{
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) #fff;
}
.block-editor-block-list__layout .is-block-moving-mode.block-editor-block-list__block.is-selected:after{
  border-radius:2px;
  border-top:4px solid #ccc;
  bottom:auto;
  box-shadow:none;
  content:"";
  left:0;
  pointer-events:none;
  position:absolute;
  right:0;
  top:-14px;
  transition:border-color .1s linear,border-style .1s linear,box-shadow .1s linear;
  z-index:0;
}
.block-editor-block-list__layout .is-block-moving-mode.can-insert-moving-block.block-editor-block-list__block.is-selected:after{
  border-color:var(--wp-admin-theme-color);
}
.has-multi-selection .block-editor-block-list__layout{
  -webkit-user-select:none;
          user-select:none;
}
.block-editor-block-list__layout [class^=components-]{
  -webkit-user-select:text;
          user-select:text;
}

.is-block-moving-mode.block-editor-block-list__block-selection-button{
  font-size:1px;
  height:1px;
  opacity:0;
  padding:0;
}

.block-editor-block-list__layout .block-editor-block-list__block{
  overflow-wrap:break-word;
  pointer-events:auto;
  position:relative;
  -webkit-user-select:text;
          user-select:text;
}
.block-editor-block-list__layout .block-editor-block-list__block.is-editing-disabled{
  pointer-events:none;
  -webkit-user-select:none;
          user-select:none;
}
.block-editor-block-list__layout .block-editor-block-list__block .reusable-block-edit-panel *{
  z-index:1;
}
.block-editor-block-list__layout .block-editor-block-list__block .components-placeholder .components-with-notices-ui{
  margin:-10px 0 12px;
}
.block-editor-block-list__layout .block-editor-block-list__block .components-with-notices-ui{
  margin:0 0 12px;
  width:100%;
}
.block-editor-block-list__layout .block-editor-block-list__block .components-with-notices-ui .components-notice .components-notice__content{
  font-size:13px;
}
.block-editor-block-list__layout .block-editor-block-list__block.has-warning{
  min-height:48px;
}
.block-editor-block-list__layout .block-editor-block-list__block.has-warning>*{
  pointer-events:none;
  -webkit-user-select:none;
          user-select:none;
}
.block-editor-block-list__layout .block-editor-block-list__block.has-warning .block-editor-warning{
  pointer-events:all;
}
.block-editor-block-list__layout .block-editor-block-list__block.has-warning:after{
  background-color:#fff6;
  border-radius:2px;
  bottom:0;
  content:"";
  left:0;
  position:absolute;
  right:0;
  top:0;
}
.block-editor-block-list__layout .block-editor-block-list__block.has-warning.is-multi-selected:after{
  background-color:initial;
}
.block-editor-block-list__layout .block-editor-block-list__block.is-reusable>.block-editor-inner-blocks>.block-editor-block-list__layout.has-overlay:after{
  display:none;
}
.block-editor-block-list__layout .block-editor-block-list__block.is-reusable>.block-editor-inner-blocks>.block-editor-block-list__layout.has-overlay .block-editor-block-list__layout.has-overlay:after{
  display:block;
}
.block-editor-block-list__layout .block-editor-block-list__block.is-reusable.has-child-selected:after{
  box-shadow:0 0 0 1px var(--wp-admin-theme-color);
}
.block-editor-block-list__layout .block-editor-block-list__block[data-clear=true]{
  float:none;
}

.is-outline-mode .block-editor-block-list__block:not(.remove-outline).is-hovered{
  cursor:default;
}
.is-outline-mode .block-editor-block-list__block:not(.remove-outline).is-hovered:after{
  border-radius:1px;
  bottom:1px;
  box-shadow:0 0 0 1px var(--wp-admin-theme-color);
  content:"";
  left:1px;
  pointer-events:none;
  position:absolute;
  right:1px;
  top:1px;
}
.is-outline-mode .block-editor-block-list__block:not(.remove-outline).is-selected{
  cursor:default;
}
.is-outline-mode .block-editor-block-list__block:not(.remove-outline).is-selected.rich-text{
  cursor:unset;
}
.is-outline-mode .block-editor-block-list__block:not(.remove-outline).is-selected:after{
  border-radius:2px;
  bottom:1px;
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  content:"";
  left:1px;
  pointer-events:none;
  position:absolute;
  right:1px;
  top:1px;
}

@keyframes block-editor-has-editable-outline__fade-out-animation{
  0%{
    border-color:rgba(var(--wp-admin-theme-color--rgb), 1);
  }
  to{
    border-color:rgba(var(--wp-admin-theme-color--rgb), 0);
  }
}
.is-root-container:not([inert]) .block-editor-block-list__block.has-editable-outline:after{
  animation:block-editor-has-editable-outline__fade-out-animation .3s ease-out;
  animation-delay:1s;
  animation-fill-mode:forwards;
  border:1px dotted rgba(var(--wp-admin-theme-color--rgb), 1);
  border-radius:2px;
  bottom:0;
  content:"";
  left:0;
  pointer-events:none;
  position:absolute;
  right:0;
  top:0;
}
@media (prefers-reduced-motion:reduce){
  .is-root-container:not([inert]) .block-editor-block-list__block.has-editable-outline:after{
    animation-delay:0s;
    animation-duration:1ms;
  }
}

.is-focus-mode .block-editor-block-list__block:not(.has-child-selected){
  opacity:.2;
  transition:opacity .1s linear;
}
@media (prefers-reduced-motion:reduce){
  .is-focus-mode .block-editor-block-list__block:not(.has-child-selected){
    transition-delay:0s;
    transition-duration:0s;
  }
}

.is-focus-mode .block-editor-block-list__block.is-content-locked-temporarily-editing-as-blocks.has-child-selected,.is-focus-mode .block-editor-block-list__block.is-content-locked-temporarily-editing-as-blocks.has-child-selected .block-editor-block-list__block,.is-focus-mode .block-editor-block-list__block.is-content-locked.has-child-selected,.is-focus-mode .block-editor-block-list__block.is-content-locked.has-child-selected .block-editor-block-list__block,.is-focus-mode .block-editor-block-list__block:not(.has-child-selected) .block-editor-block-list__block,.is-focus-mode .block-editor-block-list__block:not(.has-child-selected).is-multi-selected,.is-focus-mode .block-editor-block-list__block:not(.has-child-selected).is-selected{
  opacity:1;
}

.wp-block.alignleft,.wp-block.alignright,.wp-block[data-align=left]>*,.wp-block[data-align=right]>*{
  z-index:21;
}

.wp-site-blocks>[data-align=left]{
  float:left;
  margin-right:2em;
}

.wp-site-blocks>[data-align=right]{
  float:right;
  margin-left:2em;
}

.wp-site-blocks>[data-align=center]{
  justify-content:center;
  margin-left:auto;
  margin-right:auto;
}
.block-editor-block-list .block-editor-inserter{
  cursor:move;
  cursor:grab;
  margin:8px;
}

@keyframes block-editor-inserter__toggle__fade-in-animation{
  0%{
    opacity:0;
  }
  to{
    opacity:1;
  }
}
.wp-block .block-list-appender .block-editor-inserter__toggle{
  animation:block-editor-inserter__toggle__fade-in-animation .1s ease;
  animation-fill-mode:forwards;
}
@media (prefers-reduced-motion:reduce){
  .wp-block .block-list-appender .block-editor-inserter__toggle{
    animation-delay:0s;
    animation-duration:1ms;
  }
}

.block-editor-block-list__block:not(.is-selected):not(.has-child-selected) .block-editor-default-block-appender{
  display:none;
}
.block-editor-block-list__block:not(.is-selected):not(.has-child-selected) .block-editor-default-block-appender .block-editor-inserter__toggle{
  opacity:0;
  transform:scale(0);
}

.block-editor-block-list__block .block-editor-block-list__block-html-textarea{
  border:none;
  border-radius:2px;
  box-shadow:inset 0 0 0 1px #1e1e1e;
  display:block;
  font-family:Menlo,Consolas,monaco,monospace;
  font-size:15px;
  line-height:1.5;
  margin:0;
  outline:none;
  overflow:hidden;
  padding:12px;
  resize:none;
  transition:padding .2s linear;
  width:100%;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-block-list__block .block-editor-block-list__block-html-textarea{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.block-editor-block-list__block .block-editor-block-list__block-html-textarea:focus{
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
}
.block-editor-block-list__block .block-editor-warning{
  position:relative;
  z-index:5;
}
.block-editor-block-list__block .block-editor-warning.block-editor-block-list__block-crash-warning{
  margin-bottom:auto;
}

.block-editor-iframe__body{
  transform-origin:top center;
  transition:all .3s;
}

.is-vertical .block-list-appender{
  margin-left:12px;
  margin-right:auto;
  margin-top:12px;
  width:24px;
}

.block-list-appender>.block-editor-inserter{
  display:block;
}

.block-editor-block-list__block:not(.is-selected):not(.has-child-selected):not(.block-editor-block-list__layout) .block-editor-block-list__layout>.block-list-appender .block-list-appender__toggle{
  opacity:0;
  transform:scale(0);
}

.block-editor-block-list__block.has-block-overlay{
  cursor:default;
}
.block-editor-block-list__block.has-block-overlay:before{
  background:#0000;
  border:none;
  border-radius:2px;
  content:"";
  height:100%;
  left:0;
  position:absolute;
  top:0;
  width:100%;
  z-index:10;
}
.block-editor-block-list__block.has-block-overlay:not(.is-multi-selected):after{
  content:none !important;
}
.block-editor-block-list__block.has-block-overlay:hover:not(.is-dragging-blocks):not(.is-multi-selected):before{
  background:rgba(var(--wp-admin-theme-color--rgb), .04);
  box-shadow:0 0 0 1px var(--wp-admin-theme-color) inset;
}
.block-editor-block-list__block.has-block-overlay.is-reusable:hover:not(.is-dragging-blocks):not(.is-multi-selected):before,.block-editor-block-list__block.has-block-overlay.wp-block-template-part:hover:not(.is-dragging-blocks):not(.is-multi-selected):before{
  background:rgba(var(--wp-block-synced-color--rgb), .04);
  box-shadow:0 0 0 1px var(--wp-block-synced-color) inset;
}
.block-editor-block-list__block.has-block-overlay.is-selected:not(.is-dragging-blocks):before{
  box-shadow:0 0 0 1px var(--wp-admin-theme-color) inset;
}
.block-editor-block-list__block.has-block-overlay .block-editor-block-list__block{
  pointer-events:none;
}
.block-editor-iframe__body.is-zoomed-out .block-editor-block-list__block.has-block-overlay:before{
  left:calc(50% - 50vw);
  width:100vw;
}

.block-editor-block-list__layout .is-dragging{
  background-color:currentColor !important;
  border-radius:2px !important;
  opacity:.05 !important;
  pointer-events:none !important;
}
.block-editor-block-list__layout .is-dragging::selection{
  background:#0000 !important;
}
.block-editor-block-list__layout .is-dragging:after{
  content:none !important;
}

.block-editor-block-preview__content-iframe .block-list-appender{
  display:none;
}

.block-editor-block-preview__live-content *{
  pointer-events:none;
}
.block-editor-block-preview__live-content .block-list-appender{
  display:none;
}
.block-editor-block-preview__live-content .components-button:disabled{
  opacity:1;
}
.block-editor-block-preview__live-content .block-editor-block-list__block[data-empty=true],.block-editor-block-preview__live-content .components-placeholder{
  display:none;
}

.block-editor-block-variation-picker .components-placeholder__instructions{
  margin-bottom:0;
}
.block-editor-block-variation-picker .components-placeholder__fieldset{
  flex-direction:column;
}
.block-editor-block-variation-picker.has-many-variations .components-placeholder__fieldset{
  max-width:90%;
}

.block-editor-block-variation-picker__variations.block-editor-block-variation-picker__variations{
  display:flex;
  flex-direction:row;
  flex-wrap:wrap;
  justify-content:flex-start;
  list-style:none;
  margin:16px 0;
  padding:0;
  width:100%;
}
.block-editor-block-variation-picker__variations.block-editor-block-variation-picker__variations>li{
  flex-shrink:1;
  list-style:none;
  margin:8px 20px 0 0;
  text-align:center;
  width:75px;
}
.block-editor-block-variation-picker__variations.block-editor-block-variation-picker__variations>li button{
  display:inline-flex;
  margin-right:0;
}
.block-editor-block-variation-picker__variations.block-editor-block-variation-picker__variations .block-editor-block-variation-picker__variation{
  padding:8px;
}
.block-editor-block-variation-picker__variations.block-editor-block-variation-picker__variations .block-editor-block-variation-picker__variation-label{
  display:block;
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:12px;
  line-height:1.4;
}

.block-editor-block-variation-picker__variation{
  width:100%;
}
.block-editor-block-variation-picker__variation.components-button.has-icon{
  justify-content:center;
  width:auto;
}
.block-editor-block-variation-picker__variation.components-button.has-icon.is-secondary{
  background-color:#fff;
}
.block-editor-block-variation-picker__variation.components-button{
  height:auto;
  padding:0;
}
.block-editor-block-variation-picker__variation:before{
  content:"";
  padding-bottom:100%;
}
.block-editor-block-variation-picker__variation:first-child{
  margin-left:0;
}
.block-editor-block-variation-picker__variation:last-child{
  margin-right:0;
}

.block-editor-button-block-appender{
  align-items:center;
  box-shadow:inset 0 0 0 1px #1e1e1e;
  color:#1e1e1e;
  display:flex;
  flex-direction:column;
  height:auto;
  justify-content:center;
  width:100%;
}
.block-editor-button-block-appender.components-button.components-button{
  padding:12px;
}
.is-dark-theme .block-editor-button-block-appender{
  box-shadow:inset 0 0 0 1px #ffffffa6;
  color:#ffffffa6;
}
.block-editor-button-block-appender:hover{
  box-shadow:inset 0 0 0 1px var(--wp-admin-theme-color);
  color:var(--wp-admin-theme-color);
}
.block-editor-button-block-appender:focus{
  box-shadow:inset 0 0 0 2px var(--wp-admin-theme-color);
}
.block-editor-button-block-appender:active{
  color:#000;
}

.block-editor-block-list__block:not(.is-selected)>.is-layout-constrained.wp-block-group__inner-container>.block-list-appender:only-child,.block-editor-block-list__block:not(.is-selected)>.is-layout-flow.wp-block-group__inner-container>.block-list-appender:only-child,.is-layout-constrained.block-editor-block-list__block:not(.is-selected)>.block-list-appender:only-child,.is-layout-flow.block-editor-block-list__block:not(.is-selected)>.block-list-appender:only-child{
  pointer-events:none;
}
.block-editor-block-list__block:not(.is-selected)>.is-layout-constrained.wp-block-group__inner-container>.block-list-appender:only-child:after,.block-editor-block-list__block:not(.is-selected)>.is-layout-flow.wp-block-group__inner-container>.block-list-appender:only-child:after,.is-layout-constrained.block-editor-block-list__block:not(.is-selected)>.block-list-appender:only-child:after,.is-layout-flow.block-editor-block-list__block:not(.is-selected)>.block-list-appender:only-child:after{
  border:1px dashed;
  border-radius:2px;
  bottom:0;
  content:"";
  left:0;
  pointer-events:none;
  position:absolute;
  right:0;
  top:0;
}
.block-editor-block-list__block:not(.is-selected)>.is-layout-constrained.wp-block-group__inner-container>.block-list-appender:only-child:after:before,.block-editor-block-list__block:not(.is-selected)>.is-layout-flow.wp-block-group__inner-container>.block-list-appender:only-child:after:before,.is-layout-constrained.block-editor-block-list__block:not(.is-selected)>.block-list-appender:only-child:after:before,.is-layout-flow.block-editor-block-list__block:not(.is-selected)>.block-list-appender:only-child:after:before{
  background:currentColor;
  bottom:0;
  content:"";
  left:0;
  opacity:.1;
  pointer-events:none;
  position:absolute;
  right:0;
  top:0;
}
.block-editor-block-list__block:not(.is-selected)>.is-layout-constrained.wp-block-group__inner-container>.block-list-appender:only-child .block-editor-inserter,.block-editor-block-list__block:not(.is-selected)>.is-layout-flow.wp-block-group__inner-container>.block-list-appender:only-child .block-editor-inserter,.is-layout-constrained.block-editor-block-list__block:not(.is-selected)>.block-list-appender:only-child .block-editor-inserter,.is-layout-flow.block-editor-block-list__block:not(.is-selected)>.block-list-appender:only-child .block-editor-inserter{
  visibility:hidden;
}
.block-editor-block-list__block:not(.is-selected)>.is-layout-constrained.wp-block-group__inner-container>.block-list-appender:only-child.is-drag-over:after,.block-editor-block-list__block:not(.is-selected)>.is-layout-flow.wp-block-group__inner-container>.block-list-appender:only-child.is-drag-over:after,.is-layout-constrained.block-editor-block-list__block:not(.is-selected)>.block-list-appender:only-child.is-drag-over:after,.is-layout-flow.block-editor-block-list__block:not(.is-selected)>.block-list-appender:only-child.is-drag-over:after{
  border:none;
}
.block-editor-block-list__block:not(.is-selected)>.is-layout-constrained.wp-block-group__inner-container>.block-list-appender:only-child.is-drag-over .block-editor-inserter,.block-editor-block-list__block:not(.is-selected)>.is-layout-flow.wp-block-group__inner-container>.block-list-appender:only-child.is-drag-over .block-editor-inserter,.is-layout-constrained.block-editor-block-list__block:not(.is-selected)>.block-list-appender:only-child.is-drag-over .block-editor-inserter,.is-layout-flow.block-editor-block-list__block:not(.is-selected)>.block-list-appender:only-child.is-drag-over .block-editor-inserter{
  visibility:visible;
}
.block-editor-block-list__block:not(.is-selected)>.block-editor-block-list__block>.block-list-appender:only-child:after{
  border:none;
}
.block-list-appender:only-child.is-drag-over .block-editor-button-block-appender{
  background-color:var(--wp-admin-theme-color);
  box-shadow:inset 0 0 0 1px #ffffffa6;
  color:#ffffffa6;
  transition:background-color .2s ease-in-out;
}
@media (prefers-reduced-motion:reduce){
  .block-list-appender:only-child.is-drag-over .block-editor-button-block-appender{
    transition:none;
  }
}
.block-editor-default-block-appender{
  clear:both;
  margin-left:auto;
  margin-right:auto;
  position:relative;
}
.block-editor-default-block-appender[data-root-client-id=""] .block-editor-default-block-appender__content:hover{
  outline:1px solid #0000;
}
.block-editor-default-block-appender .block-editor-default-block-appender__content{
  opacity:.62;
}
:where(body .is-layout-constrained) .block-editor-default-block-appender>:first-child:first-child{
  margin-block-start:0;
}
.block-editor-default-block-appender .components-drop-zone__content-icon{
  display:none;
}
.block-editor-default-block-appender .block-editor-inserter__toggle.components-button.has-icon{
  background:#1e1e1e;
  border-radius:2px;
  color:#fff;
  height:24px;
  min-width:24px;
  padding:0;
}
.block-editor-default-block-appender .block-editor-inserter__toggle.components-button.has-icon:hover{
  background:var(--wp-admin-theme-color);
  color:#fff;
}

.block-editor-block-list__empty-block-inserter.block-editor-block-list__empty-block-inserter,.block-editor-default-block-appender .block-editor-inserter{
  line-height:0;
  position:absolute;
  right:0;
  top:0;
}
.block-editor-block-list__empty-block-inserter.block-editor-block-list__empty-block-inserter:disabled,.block-editor-default-block-appender .block-editor-inserter:disabled{
  display:none;
}
.block-editor-block-list__block .block-list-appender{
  bottom:0;
  list-style:none;
  padding:0;
  position:absolute;
  right:0;
  z-index:2;
}
.block-editor-block-list__block .block-list-appender.block-list-appender{
  line-height:0;
  margin:0;
}
.block-editor-block-list__block .block-list-appender .block-editor-default-block-appender{
  height:24px;
}
.block-editor-block-list__block .block-list-appender .block-editor-inserter__toggle.components-button.has-icon,.block-editor-block-list__block .block-list-appender .block-list-appender__toggle{
  background:#1e1e1e;
  box-shadow:none;
  color:#fff;
  display:none;
  flex-direction:row;
  height:24px;
  min-width:24px;
  padding:0 !important;
  width:24px;
}
.block-editor-block-list__block .block-list-appender .block-editor-inserter__toggle.components-button.has-icon:hover,.block-editor-block-list__block .block-list-appender .block-list-appender__toggle:hover{
  background:var(--wp-admin-theme-color);
  color:#fff;
}
.block-editor-block-list__block .block-list-appender .block-editor-default-block-appender__content{
  display:none;
}
.block-editor-block-list__block .block-list-appender:only-child{
  align-self:center;
  line-height:inherit;
  list-style:none;
  position:relative;
  right:auto;
}
.block-editor-block-list__block .block-list-appender:only-child .block-editor-default-block-appender__content{
  display:block;
}

.block-editor-block-list__block.is-selected .block-editor-block-list__layout>.block-list-appender .block-editor-inserter__toggle.components-button.has-icon,.block-editor-block-list__block.is-selected .block-editor-block-list__layout>.block-list-appender .block-list-appender__toggle,.block-editor-block-list__block.is-selected>.block-list-appender .block-editor-inserter__toggle.components-button.has-icon,.block-editor-block-list__block.is-selected>.block-list-appender .block-list-appender__toggle{
  display:flex;
}

.block-editor-default-block-appender__content{
  cursor:text;
}

.block-editor-block-list__layout.has-overlay:after{
  bottom:0;
  content:"";
  left:0;
  position:absolute;
  right:0;
  top:0;
  z-index:60;
}

.block-editor-media-placeholder__url-input-container .block-editor-media-placeholder__button{
  margin-bottom:0;
}

.block-editor-media-placeholder__url-input-form{
  display:flex;
}
.block-editor-media-placeholder__url-input-form input[type=url].block-editor-media-placeholder__url-input-field{
  border:none;
  border-radius:0;
  flex-grow:1;
  margin:2px;
  min-width:200px;
  width:100%;
}
@media (min-width:600px){
  .block-editor-media-placeholder__url-input-form input[type=url].block-editor-media-placeholder__url-input-field{
    width:300px;
  }
}

.block-editor-media-placeholder__url-input-submit-button{
  flex-shrink:1;
}

.block-editor-media-placeholder__button{
  margin-bottom:.5rem;
}

.block-editor-media-placeholder__cancel-button.is-link{
  display:block;
  margin:1em;
}

.block-editor-media-placeholder.is-appender{
  min-height:0;
}
.block-editor-media-placeholder.is-appender:hover{
  box-shadow:0 0 0 1px var(--wp-admin-theme-color);
  cursor:pointer;
}

.block-editor-plain-text{
  border:none;
  box-shadow:none;
  color:inherit;
  font-family:inherit;
  font-size:inherit;
  line-height:inherit;
  margin:0;
  padding:0;
  width:100%;
}

.rich-text [data-rich-text-placeholder]{
  pointer-events:none;
}
.rich-text [data-rich-text-placeholder]:after{
  content:attr(data-rich-text-placeholder);
  opacity:.62;
}
.rich-text:focus{
  outline:none;
}
.rich-text:focus [data-rich-text-format-boundary]{
  border-radius:2px;
}

.block-editor-rich-text__editable>p:first-child{
  margin-top:0;
}

figcaption.block-editor-rich-text__editable [data-rich-text-placeholder]:before{
  opacity:.8;
}

[data-rich-text-script]{
  display:inline;
}
[data-rich-text-script]:before{
  background:#ff0;
  content:"</>";
}

.block-editor-warning{
  align-items:center;
  background-color:#fff;
  border:1px solid #1e1e1e;
  border-radius:2px;
  display:flex;
  flex-wrap:wrap;
  padding:1em;
}
.block-editor-warning,.block-editor-warning .block-editor-warning__message{
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
}
.block-editor-warning .block-editor-warning__message{
  color:#1e1e1e;
  font-size:13px;
  line-height:1.4;
  margin:0;
}
.block-editor-warning p.block-editor-warning__message.block-editor-warning__message{
  min-height:auto;
}
.block-editor-warning .block-editor-warning__contents{
  align-items:baseline;
  display:flex;
  flex-direction:row;
  flex-wrap:wrap;
  justify-content:space-between;
  width:100%;
}
.block-editor-warning .block-editor-warning__actions{
  align-items:center;
  display:flex;
  margin-top:1em;
}
.block-editor-warning .block-editor-warning__action{
  margin:0 8px 0 0;
}

.block-editor-warning__secondary{
  margin:auto 0 auto 8px;
}

.components-popover.block-editor-warning__dropdown{
  z-index:99998;
}

body.admin-color-light{
  --wp-admin-theme-color:#0085ba;
  --wp-admin-theme-color--rgb:0, 133, 186;
  --wp-admin-theme-color-darker-10:#0073a1;
  --wp-admin-theme-color-darker-10--rgb:0, 115, 161;
  --wp-admin-theme-color-darker-20:#006187;
  --wp-admin-theme-color-darker-20--rgb:0, 97, 135;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-light{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-modern{
  --wp-admin-theme-color:#3858e9;
  --wp-admin-theme-color--rgb:56, 88, 233;
  --wp-admin-theme-color-darker-10:#2145e6;
  --wp-admin-theme-color-darker-10--rgb:33, 69, 230;
  --wp-admin-theme-color-darker-20:#183ad6;
  --wp-admin-theme-color-darker-20--rgb:24, 58, 214;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-modern{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-blue{
  --wp-admin-theme-color:#096484;
  --wp-admin-theme-color--rgb:9, 100, 132;
  --wp-admin-theme-color-darker-10:#07526c;
  --wp-admin-theme-color-darker-10--rgb:7, 82, 108;
  --wp-admin-theme-color-darker-20:#064054;
  --wp-admin-theme-color-darker-20--rgb:6, 64, 84;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-blue{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-coffee{
  --wp-admin-theme-color:#46403c;
  --wp-admin-theme-color--rgb:70, 64, 60;
  --wp-admin-theme-color-darker-10:#383330;
  --wp-admin-theme-color-darker-10--rgb:56, 51, 48;
  --wp-admin-theme-color-darker-20:#2b2724;
  --wp-admin-theme-color-darker-20--rgb:43, 39, 36;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-coffee{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-ectoplasm{
  --wp-admin-theme-color:#523f6d;
  --wp-admin-theme-color--rgb:82, 63, 109;
  --wp-admin-theme-color-darker-10:#46365d;
  --wp-admin-theme-color-darker-10--rgb:70, 54, 93;
  --wp-admin-theme-color-darker-20:#3a2c4d;
  --wp-admin-theme-color-darker-20--rgb:58, 44, 77;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-ectoplasm{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-midnight{
  --wp-admin-theme-color:#e14d43;
  --wp-admin-theme-color--rgb:225, 77, 67;
  --wp-admin-theme-color-darker-10:#dd382d;
  --wp-admin-theme-color-darker-10--rgb:221, 56, 45;
  --wp-admin-theme-color-darker-20:#d02c21;
  --wp-admin-theme-color-darker-20--rgb:208, 44, 33;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-midnight{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-ocean{
  --wp-admin-theme-color:#627c83;
  --wp-admin-theme-color--rgb:98, 124, 131;
  --wp-admin-theme-color-darker-10:#576e74;
  --wp-admin-theme-color-darker-10--rgb:87, 110, 116;
  --wp-admin-theme-color-darker-20:#4c6066;
  --wp-admin-theme-color-darker-20--rgb:76, 96, 102;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-ocean{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-sunrise{
  --wp-admin-theme-color:#dd823b;
  --wp-admin-theme-color--rgb:221, 130, 59;
  --wp-admin-theme-color-darker-10:#d97426;
  --wp-admin-theme-color-darker-10--rgb:217, 116, 38;
  --wp-admin-theme-color-darker-20:#c36922;
  --wp-admin-theme-color-darker-20--rgb:195, 105, 34;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-sunrise{
    --wp-admin-border-width-focus:1.5px;
  }
}