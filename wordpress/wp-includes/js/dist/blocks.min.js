/*! This file is auto-generated */
(()=>{var e={7734:e=>{"use strict";e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;var n,o,a;if(Array.isArray(t)){if((n=t.length)!=r.length)return!1;for(o=n;0!=o--;)if(!e(t[o],r[o]))return!1;return!0}if(t instanceof Map&&r instanceof Map){if(t.size!==r.size)return!1;for(o of t.entries())if(!r.has(o[0]))return!1;for(o of t.entries())if(!e(o[1],r.get(o[0])))return!1;return!0}if(t instanceof Set&&r instanceof Set){if(t.size!==r.size)return!1;for(o of t.entries())if(!r.has(o[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(r)){if((n=t.length)!=r.length)return!1;for(o=n;0!=o--;)if(t[o]!==r[o])return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((n=(a=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(o=n;0!=o--;)if(!Object.prototype.hasOwnProperty.call(r,a[o]))return!1;for(o=n;0!=o--;){var i=a[o];if(!e(t[i],r[i]))return!1}return!0}return t!=t&&r!=r}},5373:(e,t)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.server_context"),d=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),f=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),m=Symbol.for("react.offscreen");
/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function b(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case a:case s:case i:case p:case h:return e;default:switch(e=e&&e.$$typeof){case u:case l:case d:case g:case f:case c:return e;default:return t}}case o:return t}}}r=Symbol.for("react.module.reference"),t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===a||e===s||e===i||e===p||e===h||e===m||"object"==typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===f||e.$$typeof===c||e.$$typeof===l||e.$$typeof===d||e.$$typeof===r||void 0!==e.getModuleId)}},8529:(e,t,r)=>{"use strict";e.exports=r(5373)},9681:e=>{var t={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",Ấ:"A",Ắ:"A",Ẳ:"A",Ẵ:"A",Ặ:"A",Æ:"AE",Ầ:"A",Ằ:"A",Ȃ:"A",Ả:"A",Ạ:"A",Ẩ:"A",Ẫ:"A",Ậ:"A",Ç:"C",Ḉ:"C",È:"E",É:"E",Ê:"E",Ë:"E",Ế:"E",Ḗ:"E",Ề:"E",Ḕ:"E",Ḝ:"E",Ȇ:"E",Ẻ:"E",Ẽ:"E",Ẹ:"E",Ể:"E",Ễ:"E",Ệ:"E",Ì:"I",Í:"I",Î:"I",Ï:"I",Ḯ:"I",Ȋ:"I",Ỉ:"I",Ị:"I",Ð:"D",Ñ:"N",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",Ố:"O",Ṍ:"O",Ṓ:"O",Ȏ:"O",Ỏ:"O",Ọ:"O",Ổ:"O",Ỗ:"O",Ộ:"O",Ờ:"O",Ở:"O",Ỡ:"O",Ớ:"O",Ợ:"O",Ù:"U",Ú:"U",Û:"U",Ü:"U",Ủ:"U",Ụ:"U",Ử:"U",Ữ:"U",Ự:"U",Ý:"Y",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",ấ:"a",ắ:"a",ẳ:"a",ẵ:"a",ặ:"a",æ:"ae",ầ:"a",ằ:"a",ȃ:"a",ả:"a",ạ:"a",ẩ:"a",ẫ:"a",ậ:"a",ç:"c",ḉ:"c",è:"e",é:"e",ê:"e",ë:"e",ế:"e",ḗ:"e",ề:"e",ḕ:"e",ḝ:"e",ȇ:"e",ẻ:"e",ẽ:"e",ẹ:"e",ể:"e",ễ:"e",ệ:"e",ì:"i",í:"i",î:"i",ï:"i",ḯ:"i",ȋ:"i",ỉ:"i",ị:"i",ð:"d",ñ:"n",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",ố:"o",ṍ:"o",ṓ:"o",ȏ:"o",ỏ:"o",ọ:"o",ổ:"o",ỗ:"o",ộ:"o",ờ:"o",ở:"o",ỡ:"o",ớ:"o",ợ:"o",ù:"u",ú:"u",û:"u",ü:"u",ủ:"u",ụ:"u",ử:"u",ữ:"u",ự:"u",ý:"y",ÿ:"y",Ā:"A",ā:"a",Ă:"A",ă:"a",Ą:"A",ą:"a",Ć:"C",ć:"c",Ĉ:"C",ĉ:"c",Ċ:"C",ċ:"c",Č:"C",č:"c",C̆:"C",c̆:"c",Ď:"D",ď:"d",Đ:"D",đ:"d",Ē:"E",ē:"e",Ĕ:"E",ĕ:"e",Ė:"E",ė:"e",Ę:"E",ę:"e",Ě:"E",ě:"e",Ĝ:"G",Ǵ:"G",ĝ:"g",ǵ:"g",Ğ:"G",ğ:"g",Ġ:"G",ġ:"g",Ģ:"G",ģ:"g",Ĥ:"H",ĥ:"h",Ħ:"H",ħ:"h",Ḫ:"H",ḫ:"h",Ĩ:"I",ĩ:"i",Ī:"I",ī:"i",Ĭ:"I",ĭ:"i",Į:"I",į:"i",İ:"I",ı:"i",Ĳ:"IJ",ĳ:"ij",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",Ḱ:"K",ḱ:"k",K̆:"K",k̆:"k",Ĺ:"L",ĺ:"l",Ļ:"L",ļ:"l",Ľ:"L",ľ:"l",Ŀ:"L",ŀ:"l",Ł:"l",ł:"l",Ḿ:"M",ḿ:"m",M̆:"M",m̆:"m",Ń:"N",ń:"n",Ņ:"N",ņ:"n",Ň:"N",ň:"n",ŉ:"n",N̆:"N",n̆:"n",Ō:"O",ō:"o",Ŏ:"O",ŏ:"o",Ő:"O",ő:"o",Œ:"OE",œ:"oe",P̆:"P",p̆:"p",Ŕ:"R",ŕ:"r",Ŗ:"R",ŗ:"r",Ř:"R",ř:"r",R̆:"R",r̆:"r",Ȓ:"R",ȓ:"r",Ś:"S",ś:"s",Ŝ:"S",ŝ:"s",Ş:"S",Ș:"S",ș:"s",ş:"s",Š:"S",š:"s",Ţ:"T",ţ:"t",ț:"t",Ț:"T",Ť:"T",ť:"t",Ŧ:"T",ŧ:"t",T̆:"T",t̆:"t",Ũ:"U",ũ:"u",Ū:"U",ū:"u",Ŭ:"U",ŭ:"u",Ů:"U",ů:"u",Ű:"U",ű:"u",Ų:"U",ų:"u",Ȗ:"U",ȗ:"u",V̆:"V",v̆:"v",Ŵ:"W",ŵ:"w",Ẃ:"W",ẃ:"w",X̆:"X",x̆:"x",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Y̆:"Y",y̆:"y",Ź:"Z",ź:"z",Ż:"Z",ż:"z",Ž:"Z",ž:"z",ſ:"s",ƒ:"f",Ơ:"O",ơ:"o",Ư:"U",ư:"u",Ǎ:"A",ǎ:"a",Ǐ:"I",ǐ:"i",Ǒ:"O",ǒ:"o",Ǔ:"U",ǔ:"u",Ǖ:"U",ǖ:"u",Ǘ:"U",ǘ:"u",Ǚ:"U",ǚ:"u",Ǜ:"U",ǜ:"u",Ứ:"U",ứ:"u",Ṹ:"U",ṹ:"u",Ǻ:"A",ǻ:"a",Ǽ:"AE",ǽ:"ae",Ǿ:"O",ǿ:"o",Þ:"TH",þ:"th",Ṕ:"P",ṕ:"p",Ṥ:"S",ṥ:"s",X́:"X",x́:"x",Ѓ:"Г",ѓ:"г",Ќ:"К",ќ:"к",A̋:"A",a̋:"a",E̋:"E",e̋:"e",I̋:"I",i̋:"i",Ǹ:"N",ǹ:"n",Ồ:"O",ồ:"o",Ṑ:"O",ṑ:"o",Ừ:"U",ừ:"u",Ẁ:"W",ẁ:"w",Ỳ:"Y",ỳ:"y",Ȁ:"A",ȁ:"a",Ȅ:"E",ȅ:"e",Ȉ:"I",ȉ:"i",Ȍ:"O",ȍ:"o",Ȑ:"R",ȑ:"r",Ȕ:"U",ȕ:"u",B̌:"B",b̌:"b",Č̣:"C",č̣:"c",Ê̌:"E",ê̌:"e",F̌:"F",f̌:"f",Ǧ:"G",ǧ:"g",Ȟ:"H",ȟ:"h",J̌:"J",ǰ:"j",Ǩ:"K",ǩ:"k",M̌:"M",m̌:"m",P̌:"P",p̌:"p",Q̌:"Q",q̌:"q",Ř̩:"R",ř̩:"r",Ṧ:"S",ṧ:"s",V̌:"V",v̌:"v",W̌:"W",w̌:"w",X̌:"X",x̌:"x",Y̌:"Y",y̌:"y",A̧:"A",a̧:"a",B̧:"B",b̧:"b",Ḑ:"D",ḑ:"d",Ȩ:"E",ȩ:"e",Ɛ̧:"E",ɛ̧:"e",Ḩ:"H",ḩ:"h",I̧:"I",i̧:"i",Ɨ̧:"I",ɨ̧:"i",M̧:"M",m̧:"m",O̧:"O",o̧:"o",Q̧:"Q",q̧:"q",U̧:"U",u̧:"u",X̧:"X",x̧:"x",Z̧:"Z",z̧:"z",й:"и",Й:"И",ё:"е",Ё:"Е"},r=Object.keys(t).join("|"),n=new RegExp(r,"g"),o=new RegExp(r,"");function a(e){return t[e]}var i=function(e){return e.replace(n,a)};e.exports=i,e.exports.has=function(e){return!!e.match(o)},e.exports.remove=i},1030:function(e,t,r){var n;/*! showdown v 1.9.1 - 02-11-2019 */
(function(){function o(e){"use strict";var t={omitExtraWLInCodeBlocks:{defaultValue:!1,describe:"Omit the default extra whiteline added to code blocks",type:"boolean"},noHeaderId:{defaultValue:!1,describe:"Turn on/off generated header id",type:"boolean"},prefixHeaderId:{defaultValue:!1,describe:"Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic 'section-' prefix",type:"string"},rawPrefixHeaderId:{defaultValue:!1,describe:'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the " char is used in the prefix)',type:"boolean"},ghCompatibleHeaderId:{defaultValue:!1,describe:"Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)",type:"boolean"},rawHeaderId:{defaultValue:!1,describe:"Remove only spaces, ' and \" from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids",type:"boolean"},headerLevelStart:{defaultValue:!1,describe:"The header blocks level start",type:"integer"},parseImgDimensions:{defaultValue:!1,describe:"Turn on/off image dimension parsing",type:"boolean"},simplifiedAutoLink:{defaultValue:!1,describe:"Turn on/off GFM autolink style",type:"boolean"},excludeTrailingPunctuationFromURLs:{defaultValue:!1,describe:"Excludes trailing punctuation from links generated with autoLinking",type:"boolean"},literalMidWordUnderscores:{defaultValue:!1,describe:"Parse midword underscores as literal underscores",type:"boolean"},literalMidWordAsterisks:{defaultValue:!1,describe:"Parse midword asterisks as literal asterisks",type:"boolean"},strikethrough:{defaultValue:!1,describe:"Turn on/off strikethrough support",type:"boolean"},tables:{defaultValue:!1,describe:"Turn on/off tables support",type:"boolean"},tablesHeaderId:{defaultValue:!1,describe:"Add an id to table headers",type:"boolean"},ghCodeBlocks:{defaultValue:!0,describe:"Turn on/off GFM fenced code blocks support",type:"boolean"},tasklists:{defaultValue:!1,describe:"Turn on/off GFM tasklist support",type:"boolean"},smoothLivePreview:{defaultValue:!1,describe:"Prevents weird effects in live previews due to incomplete input",type:"boolean"},smartIndentationFix:{defaultValue:!1,description:"Tries to smartly fix indentation in es6 strings",type:"boolean"},disableForced4SpacesIndentedSublists:{defaultValue:!1,description:"Disables the requirement of indenting nested sublists by 4 spaces",type:"boolean"},simpleLineBreaks:{defaultValue:!1,description:"Parses simple line breaks as <br> (GFM Style)",type:"boolean"},requireSpaceBeforeHeadingText:{defaultValue:!1,description:"Makes adding a space between `#` and the header text mandatory (GFM Style)",type:"boolean"},ghMentions:{defaultValue:!1,description:"Enables github @mentions",type:"boolean"},ghMentionsLink:{defaultValue:"https://github.com/{u}",description:"Changes the link generated by @mentions. Only applies if ghMentions option is enabled.",type:"string"},encodeEmails:{defaultValue:!0,description:"Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities",type:"boolean"},openLinksInNewWindow:{defaultValue:!1,description:"Open all links in new windows",type:"boolean"},backslashEscapesHTMLTags:{defaultValue:!1,description:"Support for HTML Tag escaping. ex: <div>foo</div>",type:"boolean"},emoji:{defaultValue:!1,description:"Enable emoji support. Ex: `this is a :smile: emoji`",type:"boolean"},underline:{defaultValue:!1,description:"Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`",type:"boolean"},completeHTMLDocument:{defaultValue:!1,description:"Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags",type:"boolean"},metadata:{defaultValue:!1,description:"Enable support for document metadata (defined at the top of the document between `«««` and `»»»` or between `---` and `---`).",type:"boolean"},splitAdjacentBlockquotes:{defaultValue:!1,description:"Split adjacent blockquote blocks",type:"boolean"}};if(!1===e)return JSON.parse(JSON.stringify(t));var r={};for(var n in t)t.hasOwnProperty(n)&&(r[n]=t[n].defaultValue);return r}var a={},i={},s={},c=o(!0),l="vanilla",u={github:{omitExtraWLInCodeBlocks:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,disableForced4SpacesIndentedSublists:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghCompatibleHeaderId:!0,ghMentions:!0,backslashEscapesHTMLTags:!0,emoji:!0,splitAdjacentBlockquotes:!0},original:{noHeaderId:!0,ghCodeBlocks:!1},ghost:{omitExtraWLInCodeBlocks:!0,parseImgDimensions:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,smoothLivePreview:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghMentions:!1,encodeEmails:!0},vanilla:o(!0),allOn:function(){"use strict";var e=o(!0),t={};for(var r in e)e.hasOwnProperty(r)&&(t[r]=!0);return t}()};function d(e,t){"use strict";var r=t?"Error in "+t+" extension->":"Error in unnamed extension",n={valid:!0,error:""};a.helper.isArray(e)||(e=[e]);for(var o=0;o<e.length;++o){var i=r+" sub-extension "+o+": ",s=e[o];if("object"!=typeof s)return n.valid=!1,n.error=i+"must be an object, but "+typeof s+" given",n;if(!a.helper.isString(s.type))return n.valid=!1,n.error=i+'property "type" must be a string, but '+typeof s.type+" given",n;var c=s.type=s.type.toLowerCase();if("language"===c&&(c=s.type="lang"),"html"===c&&(c=s.type="output"),"lang"!==c&&"output"!==c&&"listener"!==c)return n.valid=!1,n.error=i+"type "+c+' is not recognized. Valid values: "lang/language", "output/html" or "listener"',n;if("listener"===c){if(a.helper.isUndefined(s.listeners))return n.valid=!1,n.error=i+'. Extensions of type "listener" must have a property called "listeners"',n}else if(a.helper.isUndefined(s.filter)&&a.helper.isUndefined(s.regex))return n.valid=!1,n.error=i+c+' extensions must define either a "regex" property or a "filter" method',n;if(s.listeners){if("object"!=typeof s.listeners)return n.valid=!1,n.error=i+'"listeners" property must be an object but '+typeof s.listeners+" given",n;for(var l in s.listeners)if(s.listeners.hasOwnProperty(l)&&"function"!=typeof s.listeners[l])return n.valid=!1,n.error=i+'"listeners" property must be an hash of [event name]: [callback]. listeners.'+l+" must be a function but "+typeof s.listeners[l]+" given",n}if(s.filter){if("function"!=typeof s.filter)return n.valid=!1,n.error=i+'"filter" must be a function, but '+typeof s.filter+" given",n}else if(s.regex){if(a.helper.isString(s.regex)&&(s.regex=new RegExp(s.regex,"g")),!(s.regex instanceof RegExp))return n.valid=!1,n.error=i+'"regex" property must either be a string or a RegExp object, but '+typeof s.regex+" given",n;if(a.helper.isUndefined(s.replace))return n.valid=!1,n.error=i+'"regex" extensions must implement a replace string or function',n}}return n}function p(e,t){"use strict";return"¨E"+t.charCodeAt(0)+"E"}a.helper={},a.extensions={},a.setOption=function(e,t){"use strict";return c[e]=t,this},a.getOption=function(e){"use strict";return c[e]},a.getOptions=function(){"use strict";return c},a.resetOptions=function(){"use strict";c=o(!0)},a.setFlavor=function(e){"use strict";if(!u.hasOwnProperty(e))throw Error(e+" flavor was not found");a.resetOptions();var t=u[e];for(var r in l=e,t)t.hasOwnProperty(r)&&(c[r]=t[r])},a.getFlavor=function(){"use strict";return l},a.getFlavorOptions=function(e){"use strict";if(u.hasOwnProperty(e))return u[e]},a.getDefaultOptions=function(e){"use strict";return o(e)},a.subParser=function(e,t){"use strict";if(a.helper.isString(e)){if(void 0===t){if(i.hasOwnProperty(e))return i[e];throw Error("SubParser named "+e+" not registered!")}i[e]=t}},a.extension=function(e,t){"use strict";if(!a.helper.isString(e))throw Error("Extension 'name' must be a string");if(e=a.helper.stdExtName(e),a.helper.isUndefined(t)){if(!s.hasOwnProperty(e))throw Error("Extension named "+e+" is not registered!");return s[e]}"function"==typeof t&&(t=t()),a.helper.isArray(t)||(t=[t]);var r=d(t,e);if(!r.valid)throw Error(r.error);s[e]=t},a.getAllExtensions=function(){"use strict";return s},a.removeExtension=function(e){"use strict";delete s[e]},a.resetExtensions=function(){"use strict";s={}},a.validateExtension=function(e){"use strict";var t=d(e,null);return!!t.valid||(console.warn(t.error),!1)},a.hasOwnProperty("helper")||(a.helper={}),a.helper.isString=function(e){"use strict";return"string"==typeof e||e instanceof String},a.helper.isFunction=function(e){"use strict";return e&&"[object Function]"==={}.toString.call(e)},a.helper.isArray=function(e){"use strict";return Array.isArray(e)},a.helper.isUndefined=function(e){"use strict";return void 0===e},a.helper.forEach=function(e,t){"use strict";if(a.helper.isUndefined(e))throw new Error("obj param is required");if(a.helper.isUndefined(t))throw new Error("callback param is required");if(!a.helper.isFunction(t))throw new Error("callback param must be a function/closure");if("function"==typeof e.forEach)e.forEach(t);else if(a.helper.isArray(e))for(var r=0;r<e.length;r++)t(e[r],r,e);else{if("object"!=typeof e)throw new Error("obj does not seem to be an array or an iterable object");for(var n in e)e.hasOwnProperty(n)&&t(e[n],n,e)}},a.helper.stdExtName=function(e){"use strict";return e.replace(/[_?*+\/\\.^-]/g,"").replace(/\s/g,"").toLowerCase()},a.helper.escapeCharactersCallback=p,a.helper.escapeCharacters=function(e,t,r){"use strict";var n="(["+t.replace(/([\[\]\\])/g,"\\$1")+"])";r&&(n="\\\\"+n);var o=new RegExp(n,"g");return e=e.replace(o,p)},a.helper.unescapeHTMLEntities=function(e){"use strict";return e.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")};var h=function(e,t,r,n){"use strict";var o,a,i,s,c,l=n||"",u=l.indexOf("g")>-1,d=new RegExp(t+"|"+r,"g"+l.replace(/g/g,"")),p=new RegExp(t,l.replace(/g/g,"")),h=[];do{for(o=0;i=d.exec(e);)if(p.test(i[0]))o++||(s=(a=d.lastIndex)-i[0].length);else if(o&&! --o){c=i.index+i[0].length;var f={left:{start:s,end:a},match:{start:a,end:i.index},right:{start:i.index,end:c},wholeMatch:{start:s,end:c}};if(h.push(f),!u)return h}}while(o&&(d.lastIndex=a));return h};a.helper.matchRecursiveRegExp=function(e,t,r,n){"use strict";for(var o=h(e,t,r,n),a=[],i=0;i<o.length;++i)a.push([e.slice(o[i].wholeMatch.start,o[i].wholeMatch.end),e.slice(o[i].match.start,o[i].match.end),e.slice(o[i].left.start,o[i].left.end),e.slice(o[i].right.start,o[i].right.end)]);return a},a.helper.replaceRecursiveRegExp=function(e,t,r,n,o){"use strict";if(!a.helper.isFunction(t)){var i=t;t=function(){return i}}var s=h(e,r,n,o),c=e,l=s.length;if(l>0){var u=[];0!==s[0].wholeMatch.start&&u.push(e.slice(0,s[0].wholeMatch.start));for(var d=0;d<l;++d)u.push(t(e.slice(s[d].wholeMatch.start,s[d].wholeMatch.end),e.slice(s[d].match.start,s[d].match.end),e.slice(s[d].left.start,s[d].left.end),e.slice(s[d].right.start,s[d].right.end))),d<l-1&&u.push(e.slice(s[d].wholeMatch.end,s[d+1].wholeMatch.start));s[l-1].wholeMatch.end<e.length&&u.push(e.slice(s[l-1].wholeMatch.end)),c=u.join("")}return c},a.helper.regexIndexOf=function(e,t,r){"use strict";if(!a.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";if(t instanceof RegExp==!1)throw"InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp";var n=e.substring(r||0).search(t);return n>=0?n+(r||0):n},a.helper.splitAtIndex=function(e,t){"use strict";if(!a.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";return[e.substring(0,t),e.substring(t)]},a.helper.encodeEmailAddress=function(e){"use strict";var t=[function(e){return"&#"+e.charCodeAt(0)+";"},function(e){return"&#x"+e.charCodeAt(0).toString(16)+";"},function(e){return e}];return e=e.replace(/./g,(function(e){if("@"===e)e=t[Math.floor(2*Math.random())](e);else{var r=Math.random();e=r>.9?t[2](e):r>.45?t[1](e):t[0](e)}return e}))},a.helper.padEnd=function(e,t,r){"use strict";return t>>=0,r=String(r||" "),e.length>t?String(e):((t-=e.length)>r.length&&(r+=r.repeat(t/r.length)),String(e)+r.slice(0,t))},"undefined"==typeof console&&(console={warn:function(e){"use strict";alert(e)},log:function(e){"use strict";alert(e)},error:function(e){"use strict";throw e}}),a.helper.regexes={asteriskDashAndColon:/([*_:~])/g},a.helper.emojis={"+1":"👍","-1":"👎",100:"💯",1234:"🔢","1st_place_medal":"🥇","2nd_place_medal":"🥈","3rd_place_medal":"🥉","8ball":"🎱",a:"🅰️",ab:"🆎",abc:"🔤",abcd:"🔡",accept:"🉑",aerial_tramway:"🚡",airplane:"✈️",alarm_clock:"⏰",alembic:"⚗️",alien:"👽",ambulance:"🚑",amphora:"🏺",anchor:"⚓️",angel:"👼",anger:"💢",angry:"😠",anguished:"😧",ant:"🐜",apple:"🍎",aquarius:"♒️",aries:"♈️",arrow_backward:"◀️",arrow_double_down:"⏬",arrow_double_up:"⏫",arrow_down:"⬇️",arrow_down_small:"🔽",arrow_forward:"▶️",arrow_heading_down:"⤵️",arrow_heading_up:"⤴️",arrow_left:"⬅️",arrow_lower_left:"↙️",arrow_lower_right:"↘️",arrow_right:"➡️",arrow_right_hook:"↪️",arrow_up:"⬆️",arrow_up_down:"↕️",arrow_up_small:"🔼",arrow_upper_left:"↖️",arrow_upper_right:"↗️",arrows_clockwise:"🔃",arrows_counterclockwise:"🔄",art:"🎨",articulated_lorry:"🚛",artificial_satellite:"🛰",astonished:"😲",athletic_shoe:"👟",atm:"🏧",atom_symbol:"⚛️",avocado:"🥑",b:"🅱️",baby:"👶",baby_bottle:"🍼",baby_chick:"🐤",baby_symbol:"🚼",back:"🔙",bacon:"🥓",badminton:"🏸",baggage_claim:"🛄",baguette_bread:"🥖",balance_scale:"⚖️",balloon:"🎈",ballot_box:"🗳",ballot_box_with_check:"☑️",bamboo:"🎍",banana:"🍌",bangbang:"‼️",bank:"🏦",bar_chart:"📊",barber:"💈",baseball:"⚾️",basketball:"🏀",basketball_man:"⛹️",basketball_woman:"⛹️&zwj;♀️",bat:"🦇",bath:"🛀",bathtub:"🛁",battery:"🔋",beach_umbrella:"🏖",bear:"🐻",bed:"🛏",bee:"🐝",beer:"🍺",beers:"🍻",beetle:"🐞",beginner:"🔰",bell:"🔔",bellhop_bell:"🛎",bento:"🍱",biking_man:"🚴",bike:"🚲",biking_woman:"🚴&zwj;♀️",bikini:"👙",biohazard:"☣️",bird:"🐦",birthday:"🎂",black_circle:"⚫️",black_flag:"🏴",black_heart:"🖤",black_joker:"🃏",black_large_square:"⬛️",black_medium_small_square:"◾️",black_medium_square:"◼️",black_nib:"✒️",black_small_square:"▪️",black_square_button:"🔲",blonde_man:"👱",blonde_woman:"👱&zwj;♀️",blossom:"🌼",blowfish:"🐡",blue_book:"📘",blue_car:"🚙",blue_heart:"💙",blush:"😊",boar:"🐗",boat:"⛵️",bomb:"💣",book:"📖",bookmark:"🔖",bookmark_tabs:"📑",books:"📚",boom:"💥",boot:"👢",bouquet:"💐",bowing_man:"🙇",bow_and_arrow:"🏹",bowing_woman:"🙇&zwj;♀️",bowling:"🎳",boxing_glove:"🥊",boy:"👦",bread:"🍞",bride_with_veil:"👰",bridge_at_night:"🌉",briefcase:"💼",broken_heart:"💔",bug:"🐛",building_construction:"🏗",bulb:"💡",bullettrain_front:"🚅",bullettrain_side:"🚄",burrito:"🌯",bus:"🚌",business_suit_levitating:"🕴",busstop:"🚏",bust_in_silhouette:"👤",busts_in_silhouette:"👥",butterfly:"🦋",cactus:"🌵",cake:"🍰",calendar:"📆",call_me_hand:"🤙",calling:"📲",camel:"🐫",camera:"📷",camera_flash:"📸",camping:"🏕",cancer:"♋️",candle:"🕯",candy:"🍬",canoe:"🛶",capital_abcd:"🔠",capricorn:"♑️",car:"🚗",card_file_box:"🗃",card_index:"📇",card_index_dividers:"🗂",carousel_horse:"🎠",carrot:"🥕",cat:"🐱",cat2:"🐈",cd:"💿",chains:"⛓",champagne:"🍾",chart:"💹",chart_with_downwards_trend:"📉",chart_with_upwards_trend:"📈",checkered_flag:"🏁",cheese:"🧀",cherries:"🍒",cherry_blossom:"🌸",chestnut:"🌰",chicken:"🐔",children_crossing:"🚸",chipmunk:"🐿",chocolate_bar:"🍫",christmas_tree:"🎄",church:"⛪️",cinema:"🎦",circus_tent:"🎪",city_sunrise:"🌇",city_sunset:"🌆",cityscape:"🏙",cl:"🆑",clamp:"🗜",clap:"👏",clapper:"🎬",classical_building:"🏛",clinking_glasses:"🥂",clipboard:"📋",clock1:"🕐",clock10:"🕙",clock1030:"🕥",clock11:"🕚",clock1130:"🕦",clock12:"🕛",clock1230:"🕧",clock130:"🕜",clock2:"🕑",clock230:"🕝",clock3:"🕒",clock330:"🕞",clock4:"🕓",clock430:"🕟",clock5:"🕔",clock530:"🕠",clock6:"🕕",clock630:"🕡",clock7:"🕖",clock730:"🕢",clock8:"🕗",clock830:"🕣",clock9:"🕘",clock930:"🕤",closed_book:"📕",closed_lock_with_key:"🔐",closed_umbrella:"🌂",cloud:"☁️",cloud_with_lightning:"🌩",cloud_with_lightning_and_rain:"⛈",cloud_with_rain:"🌧",cloud_with_snow:"🌨",clown_face:"🤡",clubs:"♣️",cocktail:"🍸",coffee:"☕️",coffin:"⚰️",cold_sweat:"😰",comet:"☄️",computer:"💻",computer_mouse:"🖱",confetti_ball:"🎊",confounded:"😖",confused:"😕",congratulations:"㊗️",construction:"🚧",construction_worker_man:"👷",construction_worker_woman:"👷&zwj;♀️",control_knobs:"🎛",convenience_store:"🏪",cookie:"🍪",cool:"🆒",policeman:"👮",copyright:"©️",corn:"🌽",couch_and_lamp:"🛋",couple:"👫",couple_with_heart_woman_man:"💑",couple_with_heart_man_man:"👨&zwj;❤️&zwj;👨",couple_with_heart_woman_woman:"👩&zwj;❤️&zwj;👩",couplekiss_man_man:"👨&zwj;❤️&zwj;💋&zwj;👨",couplekiss_man_woman:"💏",couplekiss_woman_woman:"👩&zwj;❤️&zwj;💋&zwj;👩",cow:"🐮",cow2:"🐄",cowboy_hat_face:"🤠",crab:"🦀",crayon:"🖍",credit_card:"💳",crescent_moon:"🌙",cricket:"🏏",crocodile:"🐊",croissant:"🥐",crossed_fingers:"🤞",crossed_flags:"🎌",crossed_swords:"⚔️",crown:"👑",cry:"😢",crying_cat_face:"😿",crystal_ball:"🔮",cucumber:"🥒",cupid:"💘",curly_loop:"➰",currency_exchange:"💱",curry:"🍛",custard:"🍮",customs:"🛃",cyclone:"🌀",dagger:"🗡",dancer:"💃",dancing_women:"👯",dancing_men:"👯&zwj;♂️",dango:"🍡",dark_sunglasses:"🕶",dart:"🎯",dash:"💨",date:"📅",deciduous_tree:"🌳",deer:"🦌",department_store:"🏬",derelict_house:"🏚",desert:"🏜",desert_island:"🏝",desktop_computer:"🖥",male_detective:"🕵️",diamond_shape_with_a_dot_inside:"💠",diamonds:"♦️",disappointed:"😞",disappointed_relieved:"😥",dizzy:"💫",dizzy_face:"😵",do_not_litter:"🚯",dog:"🐶",dog2:"🐕",dollar:"💵",dolls:"🎎",dolphin:"🐬",door:"🚪",doughnut:"🍩",dove:"🕊",dragon:"🐉",dragon_face:"🐲",dress:"👗",dromedary_camel:"🐪",drooling_face:"🤤",droplet:"💧",drum:"🥁",duck:"🦆",dvd:"📀","e-mail":"📧",eagle:"🦅",ear:"👂",ear_of_rice:"🌾",earth_africa:"🌍",earth_americas:"🌎",earth_asia:"🌏",egg:"🥚",eggplant:"🍆",eight_pointed_black_star:"✴️",eight_spoked_asterisk:"✳️",electric_plug:"🔌",elephant:"🐘",email:"✉️",end:"🔚",envelope_with_arrow:"📩",euro:"💶",european_castle:"🏰",european_post_office:"🏤",evergreen_tree:"🌲",exclamation:"❗️",expressionless:"😑",eye:"👁",eye_speech_bubble:"👁&zwj;🗨",eyeglasses:"👓",eyes:"👀",face_with_head_bandage:"🤕",face_with_thermometer:"🤒",fist_oncoming:"👊",factory:"🏭",fallen_leaf:"🍂",family_man_woman_boy:"👪",family_man_boy:"👨&zwj;👦",family_man_boy_boy:"👨&zwj;👦&zwj;👦",family_man_girl:"👨&zwj;👧",family_man_girl_boy:"👨&zwj;👧&zwj;👦",family_man_girl_girl:"👨&zwj;👧&zwj;👧",family_man_man_boy:"👨&zwj;👨&zwj;👦",family_man_man_boy_boy:"👨&zwj;👨&zwj;👦&zwj;👦",family_man_man_girl:"👨&zwj;👨&zwj;👧",family_man_man_girl_boy:"👨&zwj;👨&zwj;👧&zwj;👦",family_man_man_girl_girl:"👨&zwj;👨&zwj;👧&zwj;👧",family_man_woman_boy_boy:"👨&zwj;👩&zwj;👦&zwj;👦",family_man_woman_girl:"👨&zwj;👩&zwj;👧",family_man_woman_girl_boy:"👨&zwj;👩&zwj;👧&zwj;👦",family_man_woman_girl_girl:"👨&zwj;👩&zwj;👧&zwj;👧",family_woman_boy:"👩&zwj;👦",family_woman_boy_boy:"👩&zwj;👦&zwj;👦",family_woman_girl:"👩&zwj;👧",family_woman_girl_boy:"👩&zwj;👧&zwj;👦",family_woman_girl_girl:"👩&zwj;👧&zwj;👧",family_woman_woman_boy:"👩&zwj;👩&zwj;👦",family_woman_woman_boy_boy:"👩&zwj;👩&zwj;👦&zwj;👦",family_woman_woman_girl:"👩&zwj;👩&zwj;👧",family_woman_woman_girl_boy:"👩&zwj;👩&zwj;👧&zwj;👦",family_woman_woman_girl_girl:"👩&zwj;👩&zwj;👧&zwj;👧",fast_forward:"⏩",fax:"📠",fearful:"😨",feet:"🐾",female_detective:"🕵️&zwj;♀️",ferris_wheel:"🎡",ferry:"⛴",field_hockey:"🏑",file_cabinet:"🗄",file_folder:"📁",film_projector:"📽",film_strip:"🎞",fire:"🔥",fire_engine:"🚒",fireworks:"🎆",first_quarter_moon:"🌓",first_quarter_moon_with_face:"🌛",fish:"🐟",fish_cake:"🍥",fishing_pole_and_fish:"🎣",fist_raised:"✊",fist_left:"🤛",fist_right:"🤜",flags:"🎏",flashlight:"🔦",fleur_de_lis:"⚜️",flight_arrival:"🛬",flight_departure:"🛫",floppy_disk:"💾",flower_playing_cards:"🎴",flushed:"😳",fog:"🌫",foggy:"🌁",football:"🏈",footprints:"👣",fork_and_knife:"🍴",fountain:"⛲️",fountain_pen:"🖋",four_leaf_clover:"🍀",fox_face:"🦊",framed_picture:"🖼",free:"🆓",fried_egg:"🍳",fried_shrimp:"🍤",fries:"🍟",frog:"🐸",frowning:"😦",frowning_face:"☹️",frowning_man:"🙍&zwj;♂️",frowning_woman:"🙍",middle_finger:"🖕",fuelpump:"⛽️",full_moon:"🌕",full_moon_with_face:"🌝",funeral_urn:"⚱️",game_die:"🎲",gear:"⚙️",gem:"💎",gemini:"♊️",ghost:"👻",gift:"🎁",gift_heart:"💝",girl:"👧",globe_with_meridians:"🌐",goal_net:"🥅",goat:"🐐",golf:"⛳️",golfing_man:"🏌️",golfing_woman:"🏌️&zwj;♀️",gorilla:"🦍",grapes:"🍇",green_apple:"🍏",green_book:"📗",green_heart:"💚",green_salad:"🥗",grey_exclamation:"❕",grey_question:"❔",grimacing:"😬",grin:"😁",grinning:"😀",guardsman:"💂",guardswoman:"💂&zwj;♀️",guitar:"🎸",gun:"🔫",haircut_woman:"💇",haircut_man:"💇&zwj;♂️",hamburger:"🍔",hammer:"🔨",hammer_and_pick:"⚒",hammer_and_wrench:"🛠",hamster:"🐹",hand:"✋",handbag:"👜",handshake:"🤝",hankey:"💩",hatched_chick:"🐥",hatching_chick:"🐣",headphones:"🎧",hear_no_evil:"🙉",heart:"❤️",heart_decoration:"💟",heart_eyes:"😍",heart_eyes_cat:"😻",heartbeat:"💓",heartpulse:"💗",hearts:"♥️",heavy_check_mark:"✔️",heavy_division_sign:"➗",heavy_dollar_sign:"💲",heavy_heart_exclamation:"❣️",heavy_minus_sign:"➖",heavy_multiplication_x:"✖️",heavy_plus_sign:"➕",helicopter:"🚁",herb:"🌿",hibiscus:"🌺",high_brightness:"🔆",high_heel:"👠",hocho:"🔪",hole:"🕳",honey_pot:"🍯",horse:"🐴",horse_racing:"🏇",hospital:"🏥",hot_pepper:"🌶",hotdog:"🌭",hotel:"🏨",hotsprings:"♨️",hourglass:"⌛️",hourglass_flowing_sand:"⏳",house:"🏠",house_with_garden:"🏡",houses:"🏘",hugs:"🤗",hushed:"😯",ice_cream:"🍨",ice_hockey:"🏒",ice_skate:"⛸",icecream:"🍦",id:"🆔",ideograph_advantage:"🉐",imp:"👿",inbox_tray:"📥",incoming_envelope:"📨",tipping_hand_woman:"💁",information_source:"ℹ️",innocent:"😇",interrobang:"⁉️",iphone:"📱",izakaya_lantern:"🏮",jack_o_lantern:"🎃",japan:"🗾",japanese_castle:"🏯",japanese_goblin:"👺",japanese_ogre:"👹",jeans:"👖",joy:"😂",joy_cat:"😹",joystick:"🕹",kaaba:"🕋",key:"🔑",keyboard:"⌨️",keycap_ten:"🔟",kick_scooter:"🛴",kimono:"👘",kiss:"💋",kissing:"😗",kissing_cat:"😽",kissing_closed_eyes:"😚",kissing_heart:"😘",kissing_smiling_eyes:"😙",kiwi_fruit:"🥝",koala:"🐨",koko:"🈁",label:"🏷",large_blue_circle:"🔵",large_blue_diamond:"🔷",large_orange_diamond:"🔶",last_quarter_moon:"🌗",last_quarter_moon_with_face:"🌜",latin_cross:"✝️",laughing:"😆",leaves:"🍃",ledger:"📒",left_luggage:"🛅",left_right_arrow:"↔️",leftwards_arrow_with_hook:"↩️",lemon:"🍋",leo:"♌️",leopard:"🐆",level_slider:"🎚",libra:"♎️",light_rail:"🚈",link:"🔗",lion:"🦁",lips:"👄",lipstick:"💄",lizard:"🦎",lock:"🔒",lock_with_ink_pen:"🔏",lollipop:"🍭",loop:"➿",loud_sound:"🔊",loudspeaker:"📢",love_hotel:"🏩",love_letter:"💌",low_brightness:"🔅",lying_face:"🤥",m:"Ⓜ️",mag:"🔍",mag_right:"🔎",mahjong:"🀄️",mailbox:"📫",mailbox_closed:"📪",mailbox_with_mail:"📬",mailbox_with_no_mail:"📭",man:"👨",man_artist:"👨&zwj;🎨",man_astronaut:"👨&zwj;🚀",man_cartwheeling:"🤸&zwj;♂️",man_cook:"👨&zwj;🍳",man_dancing:"🕺",man_facepalming:"🤦&zwj;♂️",man_factory_worker:"👨&zwj;🏭",man_farmer:"👨&zwj;🌾",man_firefighter:"👨&zwj;🚒",man_health_worker:"👨&zwj;⚕️",man_in_tuxedo:"🤵",man_judge:"👨&zwj;⚖️",man_juggling:"🤹&zwj;♂️",man_mechanic:"👨&zwj;🔧",man_office_worker:"👨&zwj;💼",man_pilot:"👨&zwj;✈️",man_playing_handball:"🤾&zwj;♂️",man_playing_water_polo:"🤽&zwj;♂️",man_scientist:"👨&zwj;🔬",man_shrugging:"🤷&zwj;♂️",man_singer:"👨&zwj;🎤",man_student:"👨&zwj;🎓",man_teacher:"👨&zwj;🏫",man_technologist:"👨&zwj;💻",man_with_gua_pi_mao:"👲",man_with_turban:"👳",tangerine:"🍊",mans_shoe:"👞",mantelpiece_clock:"🕰",maple_leaf:"🍁",martial_arts_uniform:"🥋",mask:"😷",massage_woman:"💆",massage_man:"💆&zwj;♂️",meat_on_bone:"🍖",medal_military:"🎖",medal_sports:"🏅",mega:"📣",melon:"🍈",memo:"📝",men_wrestling:"🤼&zwj;♂️",menorah:"🕎",mens:"🚹",metal:"🤘",metro:"🚇",microphone:"🎤",microscope:"🔬",milk_glass:"🥛",milky_way:"🌌",minibus:"🚐",minidisc:"💽",mobile_phone_off:"📴",money_mouth_face:"🤑",money_with_wings:"💸",moneybag:"💰",monkey:"🐒",monkey_face:"🐵",monorail:"🚝",moon:"🌔",mortar_board:"🎓",mosque:"🕌",motor_boat:"🛥",motor_scooter:"🛵",motorcycle:"🏍",motorway:"🛣",mount_fuji:"🗻",mountain:"⛰",mountain_biking_man:"🚵",mountain_biking_woman:"🚵&zwj;♀️",mountain_cableway:"🚠",mountain_railway:"🚞",mountain_snow:"🏔",mouse:"🐭",mouse2:"🐁",movie_camera:"🎥",moyai:"🗿",mrs_claus:"🤶",muscle:"💪",mushroom:"🍄",musical_keyboard:"🎹",musical_note:"🎵",musical_score:"🎼",mute:"🔇",nail_care:"💅",name_badge:"📛",national_park:"🏞",nauseated_face:"🤢",necktie:"👔",negative_squared_cross_mark:"❎",nerd_face:"🤓",neutral_face:"😐",new:"🆕",new_moon:"🌑",new_moon_with_face:"🌚",newspaper:"📰",newspaper_roll:"🗞",next_track_button:"⏭",ng:"🆖",no_good_man:"🙅&zwj;♂️",no_good_woman:"🙅",night_with_stars:"🌃",no_bell:"🔕",no_bicycles:"🚳",no_entry:"⛔️",no_entry_sign:"🚫",no_mobile_phones:"📵",no_mouth:"😶",no_pedestrians:"🚷",no_smoking:"🚭","non-potable_water":"🚱",nose:"👃",notebook:"📓",notebook_with_decorative_cover:"📔",notes:"🎶",nut_and_bolt:"🔩",o:"⭕️",o2:"🅾️",ocean:"🌊",octopus:"🐙",oden:"🍢",office:"🏢",oil_drum:"🛢",ok:"🆗",ok_hand:"👌",ok_man:"🙆&zwj;♂️",ok_woman:"🙆",old_key:"🗝",older_man:"👴",older_woman:"👵",om:"🕉",on:"🔛",oncoming_automobile:"🚘",oncoming_bus:"🚍",oncoming_police_car:"🚔",oncoming_taxi:"🚖",open_file_folder:"📂",open_hands:"👐",open_mouth:"😮",open_umbrella:"☂️",ophiuchus:"⛎",orange_book:"📙",orthodox_cross:"☦️",outbox_tray:"📤",owl:"🦉",ox:"🐂",package:"📦",page_facing_up:"📄",page_with_curl:"📃",pager:"📟",paintbrush:"🖌",palm_tree:"🌴",pancakes:"🥞",panda_face:"🐼",paperclip:"📎",paperclips:"🖇",parasol_on_ground:"⛱",parking:"🅿️",part_alternation_mark:"〽️",partly_sunny:"⛅️",passenger_ship:"🛳",passport_control:"🛂",pause_button:"⏸",peace_symbol:"☮️",peach:"🍑",peanuts:"🥜",pear:"🍐",pen:"🖊",pencil2:"✏️",penguin:"🐧",pensive:"😔",performing_arts:"🎭",persevere:"😣",person_fencing:"🤺",pouting_woman:"🙎",phone:"☎️",pick:"⛏",pig:"🐷",pig2:"🐖",pig_nose:"🐽",pill:"💊",pineapple:"🍍",ping_pong:"🏓",pisces:"♓️",pizza:"🍕",place_of_worship:"🛐",plate_with_cutlery:"🍽",play_or_pause_button:"⏯",point_down:"👇",point_left:"👈",point_right:"👉",point_up:"☝️",point_up_2:"👆",police_car:"🚓",policewoman:"👮&zwj;♀️",poodle:"🐩",popcorn:"🍿",post_office:"🏣",postal_horn:"📯",postbox:"📮",potable_water:"🚰",potato:"🥔",pouch:"👝",poultry_leg:"🍗",pound:"💷",rage:"😡",pouting_cat:"😾",pouting_man:"🙎&zwj;♂️",pray:"🙏",prayer_beads:"📿",pregnant_woman:"🤰",previous_track_button:"⏮",prince:"🤴",princess:"👸",printer:"🖨",purple_heart:"💜",purse:"👛",pushpin:"📌",put_litter_in_its_place:"🚮",question:"❓",rabbit:"🐰",rabbit2:"🐇",racehorse:"🐎",racing_car:"🏎",radio:"📻",radio_button:"🔘",radioactive:"☢️",railway_car:"🚃",railway_track:"🛤",rainbow:"🌈",rainbow_flag:"🏳️&zwj;🌈",raised_back_of_hand:"🤚",raised_hand_with_fingers_splayed:"🖐",raised_hands:"🙌",raising_hand_woman:"🙋",raising_hand_man:"🙋&zwj;♂️",ram:"🐏",ramen:"🍜",rat:"🐀",record_button:"⏺",recycle:"♻️",red_circle:"🔴",registered:"®️",relaxed:"☺️",relieved:"😌",reminder_ribbon:"🎗",repeat:"🔁",repeat_one:"🔂",rescue_worker_helmet:"⛑",restroom:"🚻",revolving_hearts:"💞",rewind:"⏪",rhinoceros:"🦏",ribbon:"🎀",rice:"🍚",rice_ball:"🍙",rice_cracker:"🍘",rice_scene:"🎑",right_anger_bubble:"🗯",ring:"💍",robot:"🤖",rocket:"🚀",rofl:"🤣",roll_eyes:"🙄",roller_coaster:"🎢",rooster:"🐓",rose:"🌹",rosette:"🏵",rotating_light:"🚨",round_pushpin:"📍",rowing_man:"🚣",rowing_woman:"🚣&zwj;♀️",rugby_football:"🏉",running_man:"🏃",running_shirt_with_sash:"🎽",running_woman:"🏃&zwj;♀️",sa:"🈂️",sagittarius:"♐️",sake:"🍶",sandal:"👡",santa:"🎅",satellite:"📡",saxophone:"🎷",school:"🏫",school_satchel:"🎒",scissors:"✂️",scorpion:"🦂",scorpius:"♏️",scream:"😱",scream_cat:"🙀",scroll:"📜",seat:"💺",secret:"㊙️",see_no_evil:"🙈",seedling:"🌱",selfie:"🤳",shallow_pan_of_food:"🥘",shamrock:"☘️",shark:"🦈",shaved_ice:"🍧",sheep:"🐑",shell:"🐚",shield:"🛡",shinto_shrine:"⛩",ship:"🚢",shirt:"👕",shopping:"🛍",shopping_cart:"🛒",shower:"🚿",shrimp:"🦐",signal_strength:"📶",six_pointed_star:"🔯",ski:"🎿",skier:"⛷",skull:"💀",skull_and_crossbones:"☠️",sleeping:"😴",sleeping_bed:"🛌",sleepy:"😪",slightly_frowning_face:"🙁",slightly_smiling_face:"🙂",slot_machine:"🎰",small_airplane:"🛩",small_blue_diamond:"🔹",small_orange_diamond:"🔸",small_red_triangle:"🔺",small_red_triangle_down:"🔻",smile:"😄",smile_cat:"😸",smiley:"😃",smiley_cat:"😺",smiling_imp:"😈",smirk:"😏",smirk_cat:"😼",smoking:"🚬",snail:"🐌",snake:"🐍",sneezing_face:"🤧",snowboarder:"🏂",snowflake:"❄️",snowman:"⛄️",snowman_with_snow:"☃️",sob:"😭",soccer:"⚽️",soon:"🔜",sos:"🆘",sound:"🔉",space_invader:"👾",spades:"♠️",spaghetti:"🍝",sparkle:"❇️",sparkler:"🎇",sparkles:"✨",sparkling_heart:"💖",speak_no_evil:"🙊",speaker:"🔈",speaking_head:"🗣",speech_balloon:"💬",speedboat:"🚤",spider:"🕷",spider_web:"🕸",spiral_calendar:"🗓",spiral_notepad:"🗒",spoon:"🥄",squid:"🦑",stadium:"🏟",star:"⭐️",star2:"🌟",star_and_crescent:"☪️",star_of_david:"✡️",stars:"🌠",station:"🚉",statue_of_liberty:"🗽",steam_locomotive:"🚂",stew:"🍲",stop_button:"⏹",stop_sign:"🛑",stopwatch:"⏱",straight_ruler:"📏",strawberry:"🍓",stuck_out_tongue:"😛",stuck_out_tongue_closed_eyes:"😝",stuck_out_tongue_winking_eye:"😜",studio_microphone:"🎙",stuffed_flatbread:"🥙",sun_behind_large_cloud:"🌥",sun_behind_rain_cloud:"🌦",sun_behind_small_cloud:"🌤",sun_with_face:"🌞",sunflower:"🌻",sunglasses:"😎",sunny:"☀️",sunrise:"🌅",sunrise_over_mountains:"🌄",surfing_man:"🏄",surfing_woman:"🏄&zwj;♀️",sushi:"🍣",suspension_railway:"🚟",sweat:"😓",sweat_drops:"💦",sweat_smile:"😅",sweet_potato:"🍠",swimming_man:"🏊",swimming_woman:"🏊&zwj;♀️",symbols:"🔣",synagogue:"🕍",syringe:"💉",taco:"🌮",tada:"🎉",tanabata_tree:"🎋",taurus:"♉️",taxi:"🚕",tea:"🍵",telephone_receiver:"📞",telescope:"🔭",tennis:"🎾",tent:"⛺️",thermometer:"🌡",thinking:"🤔",thought_balloon:"💭",ticket:"🎫",tickets:"🎟",tiger:"🐯",tiger2:"🐅",timer_clock:"⏲",tipping_hand_man:"💁&zwj;♂️",tired_face:"😫",tm:"™️",toilet:"🚽",tokyo_tower:"🗼",tomato:"🍅",tongue:"👅",top:"🔝",tophat:"🎩",tornado:"🌪",trackball:"🖲",tractor:"🚜",traffic_light:"🚥",train:"🚋",train2:"🚆",tram:"🚊",triangular_flag_on_post:"🚩",triangular_ruler:"📐",trident:"🔱",triumph:"😤",trolleybus:"🚎",trophy:"🏆",tropical_drink:"🍹",tropical_fish:"🐠",truck:"🚚",trumpet:"🎺",tulip:"🌷",tumbler_glass:"🥃",turkey:"🦃",turtle:"🐢",tv:"📺",twisted_rightwards_arrows:"🔀",two_hearts:"💕",two_men_holding_hands:"👬",two_women_holding_hands:"👭",u5272:"🈹",u5408:"🈴",u55b6:"🈺",u6307:"🈯️",u6708:"🈷️",u6709:"🈶",u6e80:"🈵",u7121:"🈚️",u7533:"🈸",u7981:"🈲",u7a7a:"🈳",umbrella:"☔️",unamused:"😒",underage:"🔞",unicorn:"🦄",unlock:"🔓",up:"🆙",upside_down_face:"🙃",v:"✌️",vertical_traffic_light:"🚦",vhs:"📼",vibration_mode:"📳",video_camera:"📹",video_game:"🎮",violin:"🎻",virgo:"♍️",volcano:"🌋",volleyball:"🏐",vs:"🆚",vulcan_salute:"🖖",walking_man:"🚶",walking_woman:"🚶&zwj;♀️",waning_crescent_moon:"🌘",waning_gibbous_moon:"🌖",warning:"⚠️",wastebasket:"🗑",watch:"⌚️",water_buffalo:"🐃",watermelon:"🍉",wave:"👋",wavy_dash:"〰️",waxing_crescent_moon:"🌒",wc:"🚾",weary:"😩",wedding:"💒",weight_lifting_man:"🏋️",weight_lifting_woman:"🏋️&zwj;♀️",whale:"🐳",whale2:"🐋",wheel_of_dharma:"☸️",wheelchair:"♿️",white_check_mark:"✅",white_circle:"⚪️",white_flag:"🏳️",white_flower:"💮",white_large_square:"⬜️",white_medium_small_square:"◽️",white_medium_square:"◻️",white_small_square:"▫️",white_square_button:"🔳",wilted_flower:"🥀",wind_chime:"🎐",wind_face:"🌬",wine_glass:"🍷",wink:"😉",wolf:"🐺",woman:"👩",woman_artist:"👩&zwj;🎨",woman_astronaut:"👩&zwj;🚀",woman_cartwheeling:"🤸&zwj;♀️",woman_cook:"👩&zwj;🍳",woman_facepalming:"🤦&zwj;♀️",woman_factory_worker:"👩&zwj;🏭",woman_farmer:"👩&zwj;🌾",woman_firefighter:"👩&zwj;🚒",woman_health_worker:"👩&zwj;⚕️",woman_judge:"👩&zwj;⚖️",woman_juggling:"🤹&zwj;♀️",woman_mechanic:"👩&zwj;🔧",woman_office_worker:"👩&zwj;💼",woman_pilot:"👩&zwj;✈️",woman_playing_handball:"🤾&zwj;♀️",woman_playing_water_polo:"🤽&zwj;♀️",woman_scientist:"👩&zwj;🔬",woman_shrugging:"🤷&zwj;♀️",woman_singer:"👩&zwj;🎤",woman_student:"👩&zwj;🎓",woman_teacher:"👩&zwj;🏫",woman_technologist:"👩&zwj;💻",woman_with_turban:"👳&zwj;♀️",womans_clothes:"👚",womans_hat:"👒",women_wrestling:"🤼&zwj;♀️",womens:"🚺",world_map:"🗺",worried:"😟",wrench:"🔧",writing_hand:"✍️",x:"❌",yellow_heart:"💛",yen:"💴",yin_yang:"☯️",yum:"😋",zap:"⚡️",zipper_mouth_face:"🤐",zzz:"💤",octocat:'<img alt=":octocat:" height="20" width="20" align="absmiddle" src="https://assets-cdn.github.com/images/icons/emoji/octocat.png">',showdown:"<span style=\"font-family: 'Anonymous Pro', monospace; text-decoration: underline; text-decoration-style: dashed; text-decoration-color: #3e8b8a;text-underline-position: under;\">S</span>"},a.Converter=function(e){"use strict";var t={},r=[],n=[],o={},i=l,p={parsed:{},raw:"",format:""};function h(e,t){if(t=t||null,a.helper.isString(e)){if(t=e=a.helper.stdExtName(e),a.extensions[e])return console.warn("DEPRECATION WARNING: "+e+" is an old extension that uses a deprecated loading method.Please inform the developer that the extension should be updated!"),void function(e,t){"function"==typeof e&&(e=e(new a.Converter));a.helper.isArray(e)||(e=[e]);var o=d(e,t);if(!o.valid)throw Error(o.error);for(var i=0;i<e.length;++i)switch(e[i].type){case"lang":r.push(e[i]);break;case"output":n.push(e[i]);break;default:throw Error("Extension loader error: Type unrecognized!!!")}}(a.extensions[e],e);if(a.helper.isUndefined(s[e]))throw Error('Extension "'+e+'" could not be loaded. It was either not found or is not a valid extension.');e=s[e]}"function"==typeof e&&(e=e()),a.helper.isArray(e)||(e=[e]);var o=d(e,t);if(!o.valid)throw Error(o.error);for(var i=0;i<e.length;++i){switch(e[i].type){case"lang":r.push(e[i]);break;case"output":n.push(e[i])}if(e[i].hasOwnProperty("listeners"))for(var c in e[i].listeners)e[i].listeners.hasOwnProperty(c)&&f(c,e[i].listeners[c])}}function f(e,t){if(!a.helper.isString(e))throw Error("Invalid argument in converter.listen() method: name must be a string, but "+typeof e+" given");if("function"!=typeof t)throw Error("Invalid argument in converter.listen() method: callback must be a function, but "+typeof t+" given");o.hasOwnProperty(e)||(o[e]=[]),o[e].push(t)}!function(){for(var r in e=e||{},c)c.hasOwnProperty(r)&&(t[r]=c[r]);if("object"!=typeof e)throw Error("Converter expects the passed parameter to be an object, but "+typeof e+" was passed instead.");for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);t.extensions&&a.helper.forEach(t.extensions,h)}(),this._dispatch=function(e,t,r,n){if(o.hasOwnProperty(e))for(var a=0;a<o[e].length;++a){var i=o[e][a](e,t,this,r,n);i&&void 0!==i&&(t=i)}return t},this.listen=function(e,t){return f(e,t),this},this.makeHtml=function(e){if(!e)return e;var o={gHtmlBlocks:[],gHtmlMdBlocks:[],gHtmlSpans:[],gUrls:{},gTitles:{},gDimensions:{},gListLevel:0,hashLinkCounts:{},langExtensions:r,outputModifiers:n,converter:this,ghCodeBlocks:[],metadata:{parsed:{},raw:"",format:""}};return e=(e=(e=(e=(e=e.replace(/¨/g,"¨T")).replace(/\$/g,"¨D")).replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/\u00A0/g,"&nbsp;"),t.smartIndentationFix&&(e=function(e){var t=e.match(/^\s*/)[0].length,r=new RegExp("^\\s{0,"+t+"}","gm");return e.replace(r,"")}(e)),e="\n\n"+e+"\n\n",e=(e=a.subParser("detab")(e,t,o)).replace(/^[ \t]+$/gm,""),a.helper.forEach(r,(function(r){e=a.subParser("runExtension")(r,e,t,o)})),e=a.subParser("metadata")(e,t,o),e=a.subParser("hashPreCodeTags")(e,t,o),e=a.subParser("githubCodeBlocks")(e,t,o),e=a.subParser("hashHTMLBlocks")(e,t,o),e=a.subParser("hashCodeTags")(e,t,o),e=a.subParser("stripLinkDefinitions")(e,t,o),e=a.subParser("blockGamut")(e,t,o),e=a.subParser("unhashHTMLSpans")(e,t,o),e=(e=(e=a.subParser("unescapeSpecialChars")(e,t,o)).replace(/¨D/g,"$$")).replace(/¨T/g,"¨"),e=a.subParser("completeHTMLDocument")(e,t,o),a.helper.forEach(n,(function(r){e=a.subParser("runExtension")(r,e,t,o)})),p=o.metadata,e},this.makeMarkdown=this.makeMd=function(e,t){if(e=(e=(e=e.replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/>[ \t]+</,">¨NBSP;<"),!t){if(!window||!window.document)throw new Error("HTMLParser is undefined. If in a webworker or nodejs environment, you need to provide a WHATWG DOM and HTML such as JSDOM");t=window.document}var r=t.createElement("div");r.innerHTML=e;var n={preList:function(e){for(var t=e.querySelectorAll("pre"),r=[],n=0;n<t.length;++n)if(1===t[n].childElementCount&&"code"===t[n].firstChild.tagName.toLowerCase()){var o=t[n].firstChild.innerHTML.trim(),i=t[n].firstChild.getAttribute("data-language")||"";if(""===i)for(var s=t[n].firstChild.className.split(" "),c=0;c<s.length;++c){var l=s[c].match(/^language-(.+)$/);if(null!==l){i=l[1];break}}o=a.helper.unescapeHTMLEntities(o),r.push(o),t[n].outerHTML='<precode language="'+i+'" precodenum="'+n.toString()+'"></precode>'}else r.push(t[n].innerHTML),t[n].innerHTML="",t[n].setAttribute("prenum",n.toString());return r}(r)};!function e(t){for(var r=0;r<t.childNodes.length;++r){var n=t.childNodes[r];3===n.nodeType?/\S/.test(n.nodeValue)?(n.nodeValue=n.nodeValue.split("\n").join(" "),n.nodeValue=n.nodeValue.replace(/(\s)+/g,"$1")):(t.removeChild(n),--r):1===n.nodeType&&e(n)}}(r);for(var o=r.childNodes,i="",s=0;s<o.length;s++)i+=a.subParser("makeMarkdown.node")(o[s],n);return i},this.setOption=function(e,r){t[e]=r},this.getOption=function(e){return t[e]},this.getOptions=function(){return t},this.addExtension=function(e,t){h(e,t=t||null)},this.useExtension=function(e){h(e)},this.setFlavor=function(e){if(!u.hasOwnProperty(e))throw Error(e+" flavor was not found");var r=u[e];for(var n in i=e,r)r.hasOwnProperty(n)&&(t[n]=r[n])},this.getFlavor=function(){return i},this.removeExtension=function(e){a.helper.isArray(e)||(e=[e]);for(var t=0;t<e.length;++t){for(var o=e[t],i=0;i<r.length;++i)r[i]===o&&r[i].splice(i,1);for(;0<n.length;++i)n[0]===o&&n[0].splice(i,1)}},this.getAllExtensions=function(){return{language:r,output:n}},this.getMetadata=function(e){return e?p.raw:p.parsed},this.getMetadataFormat=function(){return p.format},this._setMetadataPair=function(e,t){p.parsed[e]=t},this._setMetadataFormat=function(e){p.format=e},this._setMetadataRaw=function(e){p.raw=e}},a.subParser("anchors",(function(e,t,r){"use strict";var n=function(e,n,o,i,s,c,l){if(a.helper.isUndefined(l)&&(l=""),o=o.toLowerCase(),e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)i="";else if(!i){if(o||(o=n.toLowerCase().replace(/ ?\n/g," ")),i="#"+o,a.helper.isUndefined(r.gUrls[o]))return e;i=r.gUrls[o],a.helper.isUndefined(r.gTitles[o])||(l=r.gTitles[o])}var u='<a href="'+(i=i.replace(a.helper.regexes.asteriskDashAndColon,a.helper.escapeCharactersCallback))+'"';return""!==l&&null!==l&&(u+=' title="'+(l=(l=l.replace(/"/g,"&quot;")).replace(a.helper.regexes.asteriskDashAndColon,a.helper.escapeCharactersCallback))+'"'),t.openLinksInNewWindow&&!/^#/.test(i)&&(u+=' rel="noopener noreferrer" target="¨E95Eblank"'),u+=">"+n+"</a>"};return e=(e=(e=(e=(e=r.converter._dispatch("anchors.before",e,t,r)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)] ?(?:\n *)?\[(.*?)]()()()()/g,n)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<([^>]*)>(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,n)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,n)).replace(/\[([^\[\]]+)]()()()()()/g,n),t.ghMentions&&(e=e.replace(/(^|\s)(\\)?(@([a-z\d]+(?:[a-z\d.-]+?[a-z\d]+)*))/gim,(function(e,r,n,o,i){if("\\"===n)return r+o;if(!a.helper.isString(t.ghMentionsLink))throw new Error("ghMentionsLink option must be a string");var s=t.ghMentionsLink.replace(/\{u}/g,i),c="";return t.openLinksInNewWindow&&(c=' rel="noopener noreferrer" target="¨E95Eblank"'),r+'<a href="'+s+'"'+c+">"+o+"</a>"}))),e=r.converter._dispatch("anchors.after",e,t,r)}));var f=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+?\.[^'">\s]+?)()(\1)?(?=\s|$)(?!["<>])/gi,g=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+\.[^'">\s]+?)([.!?,()\[\]])?(\1)?(?=\s|$)(?!["<>])/gi,m=/()<(((https?|ftp|dict):\/\/|www\.)[^'">\s]+)()>()/gi,b=/(^|\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-/=?^_`{|}~.]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(?=$|\s)/gim,_=/<()(?:mailto:)?([-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)>/gi,k=function(e){"use strict";return function(t,r,n,o,i,s,c){var l=n=n.replace(a.helper.regexes.asteriskDashAndColon,a.helper.escapeCharactersCallback),u="",d="",p=r||"",h=c||"";return/^www\./i.test(n)&&(n=n.replace(/^www\./i,"http://www.")),e.excludeTrailingPunctuationFromURLs&&s&&(u=s),e.openLinksInNewWindow&&(d=' rel="noopener noreferrer" target="¨E95Eblank"'),p+'<a href="'+n+'"'+d+">"+l+"</a>"+u+h}},y=function(e,t){"use strict";return function(r,n,o){var i="mailto:";return n=n||"",o=a.subParser("unescapeSpecialChars")(o,e,t),e.encodeEmails?(i=a.helper.encodeEmailAddress(i+o),o=a.helper.encodeEmailAddress(o)):i+=o,n+'<a href="'+i+'">'+o+"</a>"}};a.subParser("autoLinks",(function(e,t,r){"use strict";return e=(e=(e=r.converter._dispatch("autoLinks.before",e,t,r)).replace(m,k(t))).replace(_,y(t,r)),e=r.converter._dispatch("autoLinks.after",e,t,r)})),a.subParser("simplifiedAutoLinks",(function(e,t,r){"use strict";return t.simplifiedAutoLink?(e=r.converter._dispatch("simplifiedAutoLinks.before",e,t,r),e=(e=t.excludeTrailingPunctuationFromURLs?e.replace(g,k(t)):e.replace(f,k(t))).replace(b,y(t,r)),e=r.converter._dispatch("simplifiedAutoLinks.after",e,t,r)):e})),a.subParser("blockGamut",(function(e,t,r){"use strict";return e=r.converter._dispatch("blockGamut.before",e,t,r),e=a.subParser("blockQuotes")(e,t,r),e=a.subParser("headers")(e,t,r),e=a.subParser("horizontalRule")(e,t,r),e=a.subParser("lists")(e,t,r),e=a.subParser("codeBlocks")(e,t,r),e=a.subParser("tables")(e,t,r),e=a.subParser("hashHTMLBlocks")(e,t,r),e=a.subParser("paragraphs")(e,t,r),e=r.converter._dispatch("blockGamut.after",e,t,r)})),a.subParser("blockQuotes",(function(e,t,r){"use strict";e=r.converter._dispatch("blockQuotes.before",e,t,r),e+="\n\n";var n=/(^ {0,3}>[ \t]?.+\n(.+\n)*\n*)+/gm;return t.splitAdjacentBlockquotes&&(n=/^ {0,3}>[\s\S]*?(?:\n\n)/gm),e=e.replace(n,(function(e){return e=(e=(e=e.replace(/^[ \t]*>[ \t]?/gm,"")).replace(/¨0/g,"")).replace(/^[ \t]+$/gm,""),e=a.subParser("githubCodeBlocks")(e,t,r),e=(e=(e=a.subParser("blockGamut")(e,t,r)).replace(/(^|\n)/g,"$1  ")).replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,(function(e,t){var r=t;return r=(r=r.replace(/^  /gm,"¨0")).replace(/¨0/g,"")})),a.subParser("hashBlock")("<blockquote>\n"+e+"\n</blockquote>",t,r)})),e=r.converter._dispatch("blockQuotes.after",e,t,r)})),a.subParser("codeBlocks",(function(e,t,r){"use strict";e=r.converter._dispatch("codeBlocks.before",e,t,r);return e=(e=(e+="¨0").replace(/(?:\n\n|^)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=¨0))/g,(function(e,n,o){var i=n,s=o,c="\n";return i=a.subParser("outdent")(i,t,r),i=a.subParser("encodeCode")(i,t,r),i=(i=(i=a.subParser("detab")(i,t,r)).replace(/^\n+/g,"")).replace(/\n+$/g,""),t.omitExtraWLInCodeBlocks&&(c=""),i="<pre><code>"+i+c+"</code></pre>",a.subParser("hashBlock")(i,t,r)+s}))).replace(/¨0/,""),e=r.converter._dispatch("codeBlocks.after",e,t,r)})),a.subParser("codeSpans",(function(e,t,r){"use strict";return void 0===(e=r.converter._dispatch("codeSpans.before",e,t,r))&&(e=""),e=e.replace(/(^|[^\\])(`+)([^\r]*?[^`])\2(?!`)/gm,(function(e,n,o,i){var s=i;return s=(s=s.replace(/^([ \t]*)/g,"")).replace(/[ \t]*$/g,""),s=n+"<code>"+(s=a.subParser("encodeCode")(s,t,r))+"</code>",s=a.subParser("hashHTMLSpans")(s,t,r)})),e=r.converter._dispatch("codeSpans.after",e,t,r)})),a.subParser("completeHTMLDocument",(function(e,t,r){"use strict";if(!t.completeHTMLDocument)return e;e=r.converter._dispatch("completeHTMLDocument.before",e,t,r);var n="html",o="<!DOCTYPE HTML>\n",a="",i='<meta charset="utf-8">\n',s="",c="";for(var l in void 0!==r.metadata.parsed.doctype&&(o="<!DOCTYPE "+r.metadata.parsed.doctype+">\n","html"!==(n=r.metadata.parsed.doctype.toString().toLowerCase())&&"html5"!==n||(i='<meta charset="utf-8">')),r.metadata.parsed)if(r.metadata.parsed.hasOwnProperty(l))switch(l.toLowerCase()){case"doctype":break;case"title":a="<title>"+r.metadata.parsed.title+"</title>\n";break;case"charset":i="html"===n||"html5"===n?'<meta charset="'+r.metadata.parsed.charset+'">\n':'<meta name="charset" content="'+r.metadata.parsed.charset+'">\n';break;case"language":case"lang":s=' lang="'+r.metadata.parsed[l]+'"',c+='<meta name="'+l+'" content="'+r.metadata.parsed[l]+'">\n';break;default:c+='<meta name="'+l+'" content="'+r.metadata.parsed[l]+'">\n'}return e=o+"<html"+s+">\n<head>\n"+a+i+c+"</head>\n<body>\n"+e.trim()+"\n</body>\n</html>",e=r.converter._dispatch("completeHTMLDocument.after",e,t,r)})),a.subParser("detab",(function(e,t,r){"use strict";return e=(e=(e=(e=(e=(e=r.converter._dispatch("detab.before",e,t,r)).replace(/\t(?=\t)/g,"    ")).replace(/\t/g,"¨A¨B")).replace(/¨B(.+?)¨A/g,(function(e,t){for(var r=t,n=4-r.length%4,o=0;o<n;o++)r+=" ";return r}))).replace(/¨A/g,"    ")).replace(/¨B/g,""),e=r.converter._dispatch("detab.after",e,t,r)})),a.subParser("ellipsis",(function(e,t,r){"use strict";return e=(e=r.converter._dispatch("ellipsis.before",e,t,r)).replace(/\.\.\./g,"…"),e=r.converter._dispatch("ellipsis.after",e,t,r)})),a.subParser("emoji",(function(e,t,r){"use strict";if(!t.emoji)return e;return e=(e=r.converter._dispatch("emoji.before",e,t,r)).replace(/:([\S]+?):/g,(function(e,t){return a.helper.emojis.hasOwnProperty(t)?a.helper.emojis[t]:e})),e=r.converter._dispatch("emoji.after",e,t,r)})),a.subParser("encodeAmpsAndAngles",(function(e,t,r){"use strict";return e=(e=(e=(e=(e=r.converter._dispatch("encodeAmpsAndAngles.before",e,t,r)).replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;")).replace(/<(?![a-z\/?$!])/gi,"&lt;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;"),e=r.converter._dispatch("encodeAmpsAndAngles.after",e,t,r)})),a.subParser("encodeBackslashEscapes",(function(e,t,r){"use strict";return e=(e=(e=r.converter._dispatch("encodeBackslashEscapes.before",e,t,r)).replace(/\\(\\)/g,a.helper.escapeCharactersCallback)).replace(/\\([`*_{}\[\]()>#+.!~=|-])/g,a.helper.escapeCharactersCallback),e=r.converter._dispatch("encodeBackslashEscapes.after",e,t,r)})),a.subParser("encodeCode",(function(e,t,r){"use strict";return e=(e=r.converter._dispatch("encodeCode.before",e,t,r)).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/([*_{}\[\]\\=~-])/g,a.helper.escapeCharactersCallback),e=r.converter._dispatch("encodeCode.after",e,t,r)})),a.subParser("escapeSpecialCharsWithinTagAttributes",(function(e,t,r){"use strict";return e=(e=(e=r.converter._dispatch("escapeSpecialCharsWithinTagAttributes.before",e,t,r)).replace(/<\/?[a-z\d_:-]+(?:[\s]+[\s\S]+?)?>/gi,(function(e){return e.replace(/(.)<\/?code>(?=.)/g,"$1`").replace(/([\\`*_~=|])/g,a.helper.escapeCharactersCallback)}))).replace(/<!(--(?:(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>/gi,(function(e){return e.replace(/([\\`*_~=|])/g,a.helper.escapeCharactersCallback)})),e=r.converter._dispatch("escapeSpecialCharsWithinTagAttributes.after",e,t,r)})),a.subParser("githubCodeBlocks",(function(e,t,r){"use strict";return t.ghCodeBlocks?(e=r.converter._dispatch("githubCodeBlocks.before",e,t,r),e=(e=(e+="¨0").replace(/(?:^|\n)(?: {0,3})(```+|~~~+)(?: *)([^\s`~]*)\n([\s\S]*?)\n(?: {0,3})\1/g,(function(e,n,o,i){var s=t.omitExtraWLInCodeBlocks?"":"\n";return i=a.subParser("encodeCode")(i,t,r),i="<pre><code"+(o?' class="'+o+" language-"+o+'"':"")+">"+(i=(i=(i=a.subParser("detab")(i,t,r)).replace(/^\n+/g,"")).replace(/\n+$/g,""))+s+"</code></pre>",i=a.subParser("hashBlock")(i,t,r),"\n\n¨G"+(r.ghCodeBlocks.push({text:e,codeblock:i})-1)+"G\n\n"}))).replace(/¨0/,""),r.converter._dispatch("githubCodeBlocks.after",e,t,r)):e})),a.subParser("hashBlock",(function(e,t,r){"use strict";return e=(e=r.converter._dispatch("hashBlock.before",e,t,r)).replace(/(^\n+|\n+$)/g,""),e="\n\n¨K"+(r.gHtmlBlocks.push(e)-1)+"K\n\n",e=r.converter._dispatch("hashBlock.after",e,t,r)})),a.subParser("hashCodeTags",(function(e,t,r){"use strict";e=r.converter._dispatch("hashCodeTags.before",e,t,r);return e=a.helper.replaceRecursiveRegExp(e,(function(e,n,o,i){var s=o+a.subParser("encodeCode")(n,t,r)+i;return"¨C"+(r.gHtmlSpans.push(s)-1)+"C"}),"<code\\b[^>]*>","</code>","gim"),e=r.converter._dispatch("hashCodeTags.after",e,t,r)})),a.subParser("hashElement",(function(e,t,r){"use strict";return function(e,t){var n=t;return n=(n=(n=n.replace(/\n\n/g,"\n")).replace(/^\n/,"")).replace(/\n+$/g,""),n="\n\n¨K"+(r.gHtmlBlocks.push(n)-1)+"K\n\n"}})),a.subParser("hashHTMLBlocks",(function(e,t,r){"use strict";e=r.converter._dispatch("hashHTMLBlocks.before",e,t,r);var n=["pre","div","h1","h2","h3","h4","h5","h6","blockquote","table","dl","ol","ul","script","noscript","form","fieldset","iframe","math","style","section","header","footer","nav","article","aside","address","audio","canvas","figure","hgroup","output","video","p"],o=function(e,t,n,o){var a=e;return-1!==n.search(/\bmarkdown\b/)&&(a=n+r.converter.makeHtml(t)+o),"\n\n¨K"+(r.gHtmlBlocks.push(a)-1)+"K\n\n"};t.backslashEscapesHTMLTags&&(e=e.replace(/\\<(\/?[^>]+?)>/g,(function(e,t){return"&lt;"+t+"&gt;"})));for(var i=0;i<n.length;++i)for(var s,c=new RegExp("^ {0,3}(<"+n[i]+"\\b[^>]*>)","im"),l="<"+n[i]+"\\b[^>]*>",u="</"+n[i]+">";-1!==(s=a.helper.regexIndexOf(e,c));){var d=a.helper.splitAtIndex(e,s),p=a.helper.replaceRecursiveRegExp(d[1],o,l,u,"im");if(p===d[1])break;e=d[0].concat(p)}return e=e.replace(/(\n {0,3}(<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,a.subParser("hashElement")(e,t,r)),e=(e=a.helper.replaceRecursiveRegExp(e,(function(e){return"\n\n¨K"+(r.gHtmlBlocks.push(e)-1)+"K\n\n"}),"^ {0,3}\x3c!--","--\x3e","gm")).replace(/(?:\n\n)( {0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,a.subParser("hashElement")(e,t,r)),e=r.converter._dispatch("hashHTMLBlocks.after",e,t,r)})),a.subParser("hashHTMLSpans",(function(e,t,r){"use strict";function n(e){return"¨C"+(r.gHtmlSpans.push(e)-1)+"C"}return e=(e=(e=(e=(e=r.converter._dispatch("hashHTMLSpans.before",e,t,r)).replace(/<[^>]+?\/>/gi,(function(e){return n(e)}))).replace(/<([^>]+?)>[\s\S]*?<\/\1>/g,(function(e){return n(e)}))).replace(/<([^>]+?)\s[^>]+?>[\s\S]*?<\/\1>/g,(function(e){return n(e)}))).replace(/<[^>]+?>/gi,(function(e){return n(e)})),e=r.converter._dispatch("hashHTMLSpans.after",e,t,r)})),a.subParser("unhashHTMLSpans",(function(e,t,r){"use strict";e=r.converter._dispatch("unhashHTMLSpans.before",e,t,r);for(var n=0;n<r.gHtmlSpans.length;++n){for(var o=r.gHtmlSpans[n],a=0;/¨C(\d+)C/.test(o);){var i=RegExp.$1;if(o=o.replace("¨C"+i+"C",r.gHtmlSpans[i]),10===a){console.error("maximum nesting of 10 spans reached!!!");break}++a}e=e.replace("¨C"+n+"C",o)}return e=r.converter._dispatch("unhashHTMLSpans.after",e,t,r)})),a.subParser("hashPreCodeTags",(function(e,t,r){"use strict";e=r.converter._dispatch("hashPreCodeTags.before",e,t,r);return e=a.helper.replaceRecursiveRegExp(e,(function(e,n,o,i){var s=o+a.subParser("encodeCode")(n,t,r)+i;return"\n\n¨G"+(r.ghCodeBlocks.push({text:e,codeblock:s})-1)+"G\n\n"}),"^ {0,3}<pre\\b[^>]*>\\s*<code\\b[^>]*>","^ {0,3}</code>\\s*</pre>","gim"),e=r.converter._dispatch("hashPreCodeTags.after",e,t,r)})),a.subParser("headers",(function(e,t,r){"use strict";e=r.converter._dispatch("headers.before",e,t,r);var n=isNaN(parseInt(t.headerLevelStart))?1:parseInt(t.headerLevelStart),o=t.smoothLivePreview?/^(.+)[ \t]*\n={2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n=+[ \t]*\n+/gm,i=t.smoothLivePreview?/^(.+)[ \t]*\n-{2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n-+[ \t]*\n+/gm;e=(e=e.replace(o,(function(e,o){var i=a.subParser("spanGamut")(o,t,r),s=t.noHeaderId?"":' id="'+c(o)+'"',l="<h"+n+s+">"+i+"</h"+n+">";return a.subParser("hashBlock")(l,t,r)}))).replace(i,(function(e,o){var i=a.subParser("spanGamut")(o,t,r),s=t.noHeaderId?"":' id="'+c(o)+'"',l=n+1,u="<h"+l+s+">"+i+"</h"+l+">";return a.subParser("hashBlock")(u,t,r)}));var s=t.requireSpaceBeforeHeadingText?/^(#{1,6})[ \t]+(.+?)[ \t]*#*\n+/gm:/^(#{1,6})[ \t]*(.+?)[ \t]*#*\n+/gm;function c(e){var n,o;if(t.customizedHeaderId){var i=e.match(/\{([^{]+?)}\s*$/);i&&i[1]&&(e=i[1])}return n=e,o=a.helper.isString(t.prefixHeaderId)?t.prefixHeaderId:!0===t.prefixHeaderId?"section-":"",t.rawPrefixHeaderId||(n=o+n),n=t.ghCompatibleHeaderId?n.replace(/ /g,"-").replace(/&amp;/g,"").replace(/¨T/g,"").replace(/¨D/g,"").replace(/[&+$,\/:;=?@"#{}|^¨~\[\]`\\*)(%.!'<>]/g,"").toLowerCase():t.rawHeaderId?n.replace(/ /g,"-").replace(/&amp;/g,"&").replace(/¨T/g,"¨").replace(/¨D/g,"$").replace(/["']/g,"-").toLowerCase():n.replace(/[^\w]/g,"").toLowerCase(),t.rawPrefixHeaderId&&(n=o+n),r.hashLinkCounts[n]?n=n+"-"+r.hashLinkCounts[n]++:r.hashLinkCounts[n]=1,n}return e=e.replace(s,(function(e,o,i){var s=i;t.customizedHeaderId&&(s=i.replace(/\s?\{([^{]+?)}\s*$/,""));var l=a.subParser("spanGamut")(s,t,r),u=t.noHeaderId?"":' id="'+c(i)+'"',d=n-1+o.length,p="<h"+d+u+">"+l+"</h"+d+">";return a.subParser("hashBlock")(p,t,r)})),e=r.converter._dispatch("headers.after",e,t,r)})),a.subParser("horizontalRule",(function(e,t,r){"use strict";e=r.converter._dispatch("horizontalRule.before",e,t,r);var n=a.subParser("hashBlock")("<hr />",t,r);return e=(e=(e=e.replace(/^ {0,2}( ?-){3,}[ \t]*$/gm,n)).replace(/^ {0,2}( ?\*){3,}[ \t]*$/gm,n)).replace(/^ {0,2}( ?_){3,}[ \t]*$/gm,n),e=r.converter._dispatch("horizontalRule.after",e,t,r)})),a.subParser("images",(function(e,t,r){"use strict";function n(e,t,n,o,i,s,c,l){var u=r.gUrls,d=r.gTitles,p=r.gDimensions;if(n=n.toLowerCase(),l||(l=""),e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)o="";else if(""===o||null===o){if(""!==n&&null!==n||(n=t.toLowerCase().replace(/ ?\n/g," ")),o="#"+n,a.helper.isUndefined(u[n]))return e;o=u[n],a.helper.isUndefined(d[n])||(l=d[n]),a.helper.isUndefined(p[n])||(i=p[n].width,s=p[n].height)}t=t.replace(/"/g,"&quot;").replace(a.helper.regexes.asteriskDashAndColon,a.helper.escapeCharactersCallback);var h='<img src="'+(o=o.replace(a.helper.regexes.asteriskDashAndColon,a.helper.escapeCharactersCallback))+'" alt="'+t+'"';return l&&a.helper.isString(l)&&(h+=' title="'+(l=l.replace(/"/g,"&quot;").replace(a.helper.regexes.asteriskDashAndColon,a.helper.escapeCharactersCallback))+'"'),i&&s&&(h+=' width="'+(i="*"===i?"auto":i)+'"',h+=' height="'+(s="*"===s?"auto":s)+'"'),h+=" />"}return e=(e=(e=(e=(e=(e=r.converter._dispatch("images.before",e,t,r)).replace(/!\[([^\]]*?)] ?(?:\n *)?\[([\s\S]*?)]()()()()()/g,n)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,(function(e,t,r,o,a,i,s,c){return n(e,t,r,o=o.replace(/\s/g,""),a,i,s,c)}))).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<([^>]*)>(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(?:(["'])([^"]*?)\6))?[ \t]?\)/g,n)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,n)).replace(/!\[([^\[\]]+)]()()()()()/g,n),e=r.converter._dispatch("images.after",e,t,r)})),a.subParser("italicsAndBold",(function(e,t,r){"use strict";function n(e,t,r){return t+e+r}return e=r.converter._dispatch("italicsAndBold.before",e,t,r),e=t.literalMidWordUnderscores?(e=(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,(function(e,t){return n(t,"<strong><em>","</em></strong>")}))).replace(/\b__(\S[\s\S]*?)__\b/g,(function(e,t){return n(t,"<strong>","</strong>")}))).replace(/\b_(\S[\s\S]*?)_\b/g,(function(e,t){return n(t,"<em>","</em>")})):(e=(e=e.replace(/___(\S[\s\S]*?)___/g,(function(e,t){return/\S$/.test(t)?n(t,"<strong><em>","</em></strong>"):e}))).replace(/__(\S[\s\S]*?)__/g,(function(e,t){return/\S$/.test(t)?n(t,"<strong>","</strong>"):e}))).replace(/_([^\s_][\s\S]*?)_/g,(function(e,t){return/\S$/.test(t)?n(t,"<em>","</em>"):e})),e=t.literalMidWordAsterisks?(e=(e=e.replace(/([^*]|^)\B\*\*\*(\S[\s\S]*?)\*\*\*\B(?!\*)/g,(function(e,t,r){return n(r,t+"<strong><em>","</em></strong>")}))).replace(/([^*]|^)\B\*\*(\S[\s\S]*?)\*\*\B(?!\*)/g,(function(e,t,r){return n(r,t+"<strong>","</strong>")}))).replace(/([^*]|^)\B\*(\S[\s\S]*?)\*\B(?!\*)/g,(function(e,t,r){return n(r,t+"<em>","</em>")})):(e=(e=e.replace(/\*\*\*(\S[\s\S]*?)\*\*\*/g,(function(e,t){return/\S$/.test(t)?n(t,"<strong><em>","</em></strong>"):e}))).replace(/\*\*(\S[\s\S]*?)\*\*/g,(function(e,t){return/\S$/.test(t)?n(t,"<strong>","</strong>"):e}))).replace(/\*([^\s*][\s\S]*?)\*/g,(function(e,t){return/\S$/.test(t)?n(t,"<em>","</em>"):e})),e=r.converter._dispatch("italicsAndBold.after",e,t,r)})),a.subParser("lists",(function(e,t,r){"use strict";function n(e,n){r.gListLevel++,e=e.replace(/\n{2,}$/,"\n");var o=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0| {0,3}([*+-]|\d+[.])[ \t]+))/gm,i=/\n[ \t]*\n(?!¨0)/.test(e+="¨0");return t.disableForced4SpacesIndentedSublists&&(o=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0|\2([*+-]|\d+[.])[ \t]+))/gm),e=(e=e.replace(o,(function(e,n,o,s,c,l,u){u=u&&""!==u.trim();var d=a.subParser("outdent")(c,t,r),p="";return l&&t.tasklists&&(p=' class="task-list-item" style="list-style-type: none;"',d=d.replace(/^[ \t]*\[(x|X| )?]/m,(function(){var e='<input type="checkbox" disabled style="margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;"';return u&&(e+=" checked"),e+=">"}))),d=d.replace(/^([-*+]|\d\.)[ \t]+[\S\n ]*/g,(function(e){return"¨A"+e})),n||d.search(/\n{2,}/)>-1?(d=a.subParser("githubCodeBlocks")(d,t,r),d=a.subParser("blockGamut")(d,t,r)):(d=(d=a.subParser("lists")(d,t,r)).replace(/\n$/,""),d=(d=a.subParser("hashHTMLBlocks")(d,t,r)).replace(/\n\n+/g,"\n\n"),d=i?a.subParser("paragraphs")(d,t,r):a.subParser("spanGamut")(d,t,r)),d="<li"+p+">"+(d=d.replace("¨A",""))+"</li>\n"}))).replace(/¨0/g,""),r.gListLevel--,n&&(e=e.replace(/\s+$/,"")),e}function o(e,t){if("ol"===t){var r=e.match(/^ *(\d+)\./);if(r&&"1"!==r[1])return' start="'+r[1]+'"'}return""}function i(e,r,a){var i=t.disableForced4SpacesIndentedSublists?/^ ?\d+\.[ \t]/gm:/^ {0,3}\d+\.[ \t]/gm,s=t.disableForced4SpacesIndentedSublists?/^ ?[*+-][ \t]/gm:/^ {0,3}[*+-][ \t]/gm,c="ul"===r?i:s,l="";if(-1!==e.search(c))!function t(u){var d=u.search(c),p=o(e,r);-1!==d?(l+="\n\n<"+r+p+">\n"+n(u.slice(0,d),!!a)+"</"+r+">\n",c="ul"===(r="ul"===r?"ol":"ul")?i:s,t(u.slice(d))):l+="\n\n<"+r+p+">\n"+n(u,!!a)+"</"+r+">\n"}(e);else{var u=o(e,r);l="\n\n<"+r+u+">\n"+n(e,!!a)+"</"+r+">\n"}return l}return e=r.converter._dispatch("lists.before",e,t,r),e+="¨0",e=(e=r.gListLevel?e.replace(/^(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(e,t,r){return i(t,r.search(/[*+-]/g)>-1?"ul":"ol",!0)})):e.replace(/(\n\n|^\n?)(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(e,t,r,n){return i(r,n.search(/[*+-]/g)>-1?"ul":"ol",!1)}))).replace(/¨0/,""),e=r.converter._dispatch("lists.after",e,t,r)})),a.subParser("metadata",(function(e,t,r){"use strict";if(!t.metadata)return e;function n(e){r.metadata.raw=e,(e=(e=e.replace(/&/g,"&amp;").replace(/"/g,"&quot;")).replace(/\n {4}/g," ")).replace(/^([\S ]+): +([\s\S]+?)$/gm,(function(e,t,n){return r.metadata.parsed[t]=n,""}))}return e=(e=(e=(e=r.converter._dispatch("metadata.before",e,t,r)).replace(/^\s*«««+(\S*?)\n([\s\S]+?)\n»»»+\n/,(function(e,t,r){return n(r),"¨M"}))).replace(/^\s*---+(\S*?)\n([\s\S]+?)\n---+\n/,(function(e,t,o){return t&&(r.metadata.format=t),n(o),"¨M"}))).replace(/¨M/g,""),e=r.converter._dispatch("metadata.after",e,t,r)})),a.subParser("outdent",(function(e,t,r){"use strict";return e=(e=(e=r.converter._dispatch("outdent.before",e,t,r)).replace(/^(\t|[ ]{1,4})/gm,"¨0")).replace(/¨0/g,""),e=r.converter._dispatch("outdent.after",e,t,r)})),a.subParser("paragraphs",(function(e,t,r){"use strict";for(var n=(e=(e=(e=r.converter._dispatch("paragraphs.before",e,t,r)).replace(/^\n+/g,"")).replace(/\n+$/g,"")).split(/\n{2,}/g),o=[],i=n.length,s=0;s<i;s++){var c=n[s];c.search(/¨(K|G)(\d+)\1/g)>=0?o.push(c):c.search(/\S/)>=0&&(c=(c=a.subParser("spanGamut")(c,t,r)).replace(/^([ \t]*)/g,"<p>"),c+="</p>",o.push(c))}for(i=o.length,s=0;s<i;s++){for(var l="",u=o[s],d=!1;/¨(K|G)(\d+)\1/.test(u);){var p=RegExp.$1,h=RegExp.$2;l=(l="K"===p?r.gHtmlBlocks[h]:d?a.subParser("encodeCode")(r.ghCodeBlocks[h].text,t,r):r.ghCodeBlocks[h].codeblock).replace(/\$/g,"$$$$"),u=u.replace(/(\n\n)?¨(K|G)\d+\2(\n\n)?/,l),/^<pre\b[^>]*>\s*<code\b[^>]*>/.test(u)&&(d=!0)}o[s]=u}return e=(e=(e=o.join("\n")).replace(/^\n+/g,"")).replace(/\n+$/g,""),r.converter._dispatch("paragraphs.after",e,t,r)})),a.subParser("runExtension",(function(e,t,r,n){"use strict";if(e.filter)t=e.filter(t,n.converter,r);else if(e.regex){var o=e.regex;o instanceof RegExp||(o=new RegExp(o,"g")),t=t.replace(o,e.replace)}return t})),a.subParser("spanGamut",(function(e,t,r){"use strict";return e=r.converter._dispatch("spanGamut.before",e,t,r),e=a.subParser("codeSpans")(e,t,r),e=a.subParser("escapeSpecialCharsWithinTagAttributes")(e,t,r),e=a.subParser("encodeBackslashEscapes")(e,t,r),e=a.subParser("images")(e,t,r),e=a.subParser("anchors")(e,t,r),e=a.subParser("autoLinks")(e,t,r),e=a.subParser("simplifiedAutoLinks")(e,t,r),e=a.subParser("emoji")(e,t,r),e=a.subParser("underline")(e,t,r),e=a.subParser("italicsAndBold")(e,t,r),e=a.subParser("strikethrough")(e,t,r),e=a.subParser("ellipsis")(e,t,r),e=a.subParser("hashHTMLSpans")(e,t,r),e=a.subParser("encodeAmpsAndAngles")(e,t,r),t.simpleLineBreaks?/\n\n¨K/.test(e)||(e=e.replace(/\n+/g,"<br />\n")):e=e.replace(/  +\n/g,"<br />\n"),e=r.converter._dispatch("spanGamut.after",e,t,r)})),a.subParser("strikethrough",(function(e,t,r){"use strict";return t.strikethrough&&(e=(e=r.converter._dispatch("strikethrough.before",e,t,r)).replace(/(?:~){2}([\s\S]+?)(?:~){2}/g,(function(e,n){return function(e){return t.simplifiedAutoLink&&(e=a.subParser("simplifiedAutoLinks")(e,t,r)),"<del>"+e+"</del>"}(n)})),e=r.converter._dispatch("strikethrough.after",e,t,r)),e})),a.subParser("stripLinkDefinitions",(function(e,t,r){"use strict";var n=function(e,n,o,i,s,c,l){return n=n.toLowerCase(),o.match(/^data:.+?\/.+?;base64,/)?r.gUrls[n]=o.replace(/\s/g,""):r.gUrls[n]=a.subParser("encodeAmpsAndAngles")(o,t,r),c?c+l:(l&&(r.gTitles[n]=l.replace(/"|'/g,"&quot;")),t.parseImgDimensions&&i&&s&&(r.gDimensions[n]={width:i,height:s}),"")};return e=(e=(e=(e+="¨0").replace(/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n\n|(?=¨0)|(?=\n\[))/gm,n)).replace(/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?([^>\s]+)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n+|(?=¨0))/gm,n)).replace(/¨0/,"")})),a.subParser("tables",(function(e,t,r){"use strict";if(!t.tables)return e;function n(e,n){return"<td"+n+">"+a.subParser("spanGamut")(e,t,r)+"</td>\n"}function o(e){var o,i=e.split("\n");for(o=0;o<i.length;++o)/^ {0,3}\|/.test(i[o])&&(i[o]=i[o].replace(/^ {0,3}\|/,"")),/\|[ \t]*$/.test(i[o])&&(i[o]=i[o].replace(/\|[ \t]*$/,"")),i[o]=a.subParser("codeSpans")(i[o],t,r);var s,c,l,u,d=i[0].split("|").map((function(e){return e.trim()})),p=i[1].split("|").map((function(e){return e.trim()})),h=[],f=[],g=[],m=[];for(i.shift(),i.shift(),o=0;o<i.length;++o)""!==i[o].trim()&&h.push(i[o].split("|").map((function(e){return e.trim()})));if(d.length<p.length)return e;for(o=0;o<p.length;++o)g.push((s=p[o],/^:[ \t]*--*$/.test(s)?' style="text-align:left;"':/^--*[ \t]*:[ \t]*$/.test(s)?' style="text-align:right;"':/^:[ \t]*--*[ \t]*:$/.test(s)?' style="text-align:center;"':""));for(o=0;o<d.length;++o)a.helper.isUndefined(g[o])&&(g[o]=""),f.push((c=d[o],l=g[o],u=void 0,u="",c=c.trim(),(t.tablesHeaderId||t.tableHeaderId)&&(u=' id="'+c.replace(/ /g,"_").toLowerCase()+'"'),"<th"+u+l+">"+(c=a.subParser("spanGamut")(c,t,r))+"</th>\n"));for(o=0;o<h.length;++o){for(var b=[],_=0;_<f.length;++_)a.helper.isUndefined(h[o][_]),b.push(n(h[o][_],g[_]));m.push(b)}return function(e,t){for(var r="<table>\n<thead>\n<tr>\n",n=e.length,o=0;o<n;++o)r+=e[o];for(r+="</tr>\n</thead>\n<tbody>\n",o=0;o<t.length;++o){r+="<tr>\n";for(var a=0;a<n;++a)r+=t[o][a];r+="</tr>\n"}return r+"</tbody>\n</table>\n"}(f,m)}return e=(e=(e=(e=r.converter._dispatch("tables.before",e,t,r)).replace(/\\(\|)/g,a.helper.escapeCharactersCallback)).replace(/^ {0,3}\|?.+\|.+\n {0,3}\|?[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*:?[ \t]*(?:[-=]){2,}[\s\S]+?(?:\n\n|¨0)/gm,o)).replace(/^ {0,3}\|.+\|[ \t]*\n {0,3}\|[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*\n( {0,3}\|.+\|[ \t]*\n)*(?:\n|¨0)/gm,o),e=r.converter._dispatch("tables.after",e,t,r)})),a.subParser("underline",(function(e,t,r){"use strict";return t.underline?(e=r.converter._dispatch("underline.before",e,t,r),e=(e=t.literalMidWordUnderscores?(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,(function(e,t){return"<u>"+t+"</u>"}))).replace(/\b__(\S[\s\S]*?)__\b/g,(function(e,t){return"<u>"+t+"</u>"})):(e=e.replace(/___(\S[\s\S]*?)___/g,(function(e,t){return/\S$/.test(t)?"<u>"+t+"</u>":e}))).replace(/__(\S[\s\S]*?)__/g,(function(e,t){return/\S$/.test(t)?"<u>"+t+"</u>":e}))).replace(/(_)/g,a.helper.escapeCharactersCallback),e=r.converter._dispatch("underline.after",e,t,r)):e})),a.subParser("unescapeSpecialChars",(function(e,t,r){"use strict";return e=(e=r.converter._dispatch("unescapeSpecialChars.before",e,t,r)).replace(/¨E(\d+)E/g,(function(e,t){var r=parseInt(t);return String.fromCharCode(r)})),e=r.converter._dispatch("unescapeSpecialChars.after",e,t,r)})),a.subParser("makeMarkdown.blockquote",(function(e,t){"use strict";var r="";if(e.hasChildNodes())for(var n=e.childNodes,o=n.length,i=0;i<o;++i){var s=a.subParser("makeMarkdown.node")(n[i],t);""!==s&&(r+=s)}return r="> "+(r=r.trim()).split("\n").join("\n> ")})),a.subParser("makeMarkdown.codeBlock",(function(e,t){"use strict";var r=e.getAttribute("language"),n=e.getAttribute("precodenum");return"```"+r+"\n"+t.preList[n]+"\n```"})),a.subParser("makeMarkdown.codeSpan",(function(e){"use strict";return"`"+e.innerHTML+"`"})),a.subParser("makeMarkdown.emphasis",(function(e,t){"use strict";var r="";if(e.hasChildNodes()){r+="*";for(var n=e.childNodes,o=n.length,i=0;i<o;++i)r+=a.subParser("makeMarkdown.node")(n[i],t);r+="*"}return r})),a.subParser("makeMarkdown.header",(function(e,t,r){"use strict";var n=new Array(r+1).join("#"),o="";if(e.hasChildNodes()){o=n+" ";for(var i=e.childNodes,s=i.length,c=0;c<s;++c)o+=a.subParser("makeMarkdown.node")(i[c],t)}return o})),a.subParser("makeMarkdown.hr",(function(){"use strict";return"---"})),a.subParser("makeMarkdown.image",(function(e){"use strict";var t="";return e.hasAttribute("src")&&(t+="!["+e.getAttribute("alt")+"](",t+="<"+e.getAttribute("src")+">",e.hasAttribute("width")&&e.hasAttribute("height")&&(t+=" ="+e.getAttribute("width")+"x"+e.getAttribute("height")),e.hasAttribute("title")&&(t+=' "'+e.getAttribute("title")+'"'),t+=")"),t})),a.subParser("makeMarkdown.links",(function(e,t){"use strict";var r="";if(e.hasChildNodes()&&e.hasAttribute("href")){var n=e.childNodes,o=n.length;r="[";for(var i=0;i<o;++i)r+=a.subParser("makeMarkdown.node")(n[i],t);r+="](",r+="<"+e.getAttribute("href")+">",e.hasAttribute("title")&&(r+=' "'+e.getAttribute("title")+'"'),r+=")"}return r})),a.subParser("makeMarkdown.list",(function(e,t,r){"use strict";var n="";if(!e.hasChildNodes())return"";for(var o=e.childNodes,i=o.length,s=e.getAttribute("start")||1,c=0;c<i;++c)if(void 0!==o[c].tagName&&"li"===o[c].tagName.toLowerCase()){n+=("ol"===r?s.toString()+". ":"- ")+a.subParser("makeMarkdown.listItem")(o[c],t),++s}return(n+="\n\x3c!-- --\x3e\n").trim()})),a.subParser("makeMarkdown.listItem",(function(e,t){"use strict";for(var r="",n=e.childNodes,o=n.length,i=0;i<o;++i)r+=a.subParser("makeMarkdown.node")(n[i],t);return/\n$/.test(r)?r=r.split("\n").join("\n    ").replace(/^ {4}$/gm,"").replace(/\n\n+/g,"\n\n"):r+="\n",r})),a.subParser("makeMarkdown.node",(function(e,t,r){"use strict";r=r||!1;var n="";if(3===e.nodeType)return a.subParser("makeMarkdown.txt")(e,t);if(8===e.nodeType)return"\x3c!--"+e.data+"--\x3e\n\n";if(1!==e.nodeType)return"";switch(e.tagName.toLowerCase()){case"h1":r||(n=a.subParser("makeMarkdown.header")(e,t,1)+"\n\n");break;case"h2":r||(n=a.subParser("makeMarkdown.header")(e,t,2)+"\n\n");break;case"h3":r||(n=a.subParser("makeMarkdown.header")(e,t,3)+"\n\n");break;case"h4":r||(n=a.subParser("makeMarkdown.header")(e,t,4)+"\n\n");break;case"h5":r||(n=a.subParser("makeMarkdown.header")(e,t,5)+"\n\n");break;case"h6":r||(n=a.subParser("makeMarkdown.header")(e,t,6)+"\n\n");break;case"p":r||(n=a.subParser("makeMarkdown.paragraph")(e,t)+"\n\n");break;case"blockquote":r||(n=a.subParser("makeMarkdown.blockquote")(e,t)+"\n\n");break;case"hr":r||(n=a.subParser("makeMarkdown.hr")(e,t)+"\n\n");break;case"ol":r||(n=a.subParser("makeMarkdown.list")(e,t,"ol")+"\n\n");break;case"ul":r||(n=a.subParser("makeMarkdown.list")(e,t,"ul")+"\n\n");break;case"precode":r||(n=a.subParser("makeMarkdown.codeBlock")(e,t)+"\n\n");break;case"pre":r||(n=a.subParser("makeMarkdown.pre")(e,t)+"\n\n");break;case"table":r||(n=a.subParser("makeMarkdown.table")(e,t)+"\n\n");break;case"code":n=a.subParser("makeMarkdown.codeSpan")(e,t);break;case"em":case"i":n=a.subParser("makeMarkdown.emphasis")(e,t);break;case"strong":case"b":n=a.subParser("makeMarkdown.strong")(e,t);break;case"del":n=a.subParser("makeMarkdown.strikethrough")(e,t);break;case"a":n=a.subParser("makeMarkdown.links")(e,t);break;case"img":n=a.subParser("makeMarkdown.image")(e,t);break;default:n=e.outerHTML+"\n\n"}return n})),a.subParser("makeMarkdown.paragraph",(function(e,t){"use strict";var r="";if(e.hasChildNodes())for(var n=e.childNodes,o=n.length,i=0;i<o;++i)r+=a.subParser("makeMarkdown.node")(n[i],t);return r=r.trim()})),a.subParser("makeMarkdown.pre",(function(e,t){"use strict";var r=e.getAttribute("prenum");return"<pre>"+t.preList[r]+"</pre>"})),a.subParser("makeMarkdown.strikethrough",(function(e,t){"use strict";var r="";if(e.hasChildNodes()){r+="~~";for(var n=e.childNodes,o=n.length,i=0;i<o;++i)r+=a.subParser("makeMarkdown.node")(n[i],t);r+="~~"}return r})),a.subParser("makeMarkdown.strong",(function(e,t){"use strict";var r="";if(e.hasChildNodes()){r+="**";for(var n=e.childNodes,o=n.length,i=0;i<o;++i)r+=a.subParser("makeMarkdown.node")(n[i],t);r+="**"}return r})),a.subParser("makeMarkdown.table",(function(e,t){"use strict";var r,n,o="",i=[[],[]],s=e.querySelectorAll("thead>tr>th"),c=e.querySelectorAll("tbody>tr");for(r=0;r<s.length;++r){var l=a.subParser("makeMarkdown.tableCell")(s[r],t),u="---";if(s[r].hasAttribute("style"))switch(s[r].getAttribute("style").toLowerCase().replace(/\s/g,"")){case"text-align:left;":u=":---";break;case"text-align:right;":u="---:";break;case"text-align:center;":u=":---:"}i[0][r]=l.trim(),i[1][r]=u}for(r=0;r<c.length;++r){var d=i.push([])-1,p=c[r].getElementsByTagName("td");for(n=0;n<s.length;++n){var h=" ";void 0!==p[n]&&(h=a.subParser("makeMarkdown.tableCell")(p[n],t)),i[d].push(h)}}var f=3;for(r=0;r<i.length;++r)for(n=0;n<i[r].length;++n){var g=i[r][n].length;g>f&&(f=g)}for(r=0;r<i.length;++r){for(n=0;n<i[r].length;++n)1===r?":"===i[r][n].slice(-1)?i[r][n]=a.helper.padEnd(i[r][n].slice(-1),f-1,"-")+":":i[r][n]=a.helper.padEnd(i[r][n],f,"-"):i[r][n]=a.helper.padEnd(i[r][n],f);o+="| "+i[r].join(" | ")+" |\n"}return o.trim()})),a.subParser("makeMarkdown.tableCell",(function(e,t){"use strict";var r="";if(!e.hasChildNodes())return"";for(var n=e.childNodes,o=n.length,i=0;i<o;++i)r+=a.subParser("makeMarkdown.node")(n[i],t,!0);return r.trim()})),a.subParser("makeMarkdown.txt",(function(e){"use strict";var t=e.nodeValue;return t=(t=t.replace(/ +/g," ")).replace(/¨NBSP;/g," "),t=(t=(t=(t=(t=(t=(t=(t=(t=a.helper.unescapeHTMLEntities(t)).replace(/([*_~|`])/g,"\\$1")).replace(/^(\s*)>/g,"\\$1>")).replace(/^#/gm,"\\#")).replace(/^(\s*)([-=]{3,})(\s*)$/,"$1\\$2$3")).replace(/^( {0,3}\d+)\./gm,"$1\\.")).replace(/^( {0,3})([+-])/gm,"$1\\$2")).replace(/]([\s]*)\(/g,"\\]$1\\(")).replace(/^ {0,3}\[([\S \t]*?)]:/gm,"\\[$1]:")}));void 0===(n=function(){"use strict";return a}.call(t,r,t,e))||(e.exports=n)}).call(this)}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={exports:{}};return e[n].call(a.exports,a,a.exports,r),a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";r.r(n),r.d(n,{__EXPERIMENTAL_ELEMENTS:()=>X,__EXPERIMENTAL_PATHS_WITH_OVERRIDE:()=>J,__EXPERIMENTAL_STYLE_PROPERTY:()=>Z,__experimentalCloneSanitizedBlock:()=>_r,__experimentalGetAccessibleBlockLabel:()=>He,__experimentalGetBlockAttributesNamesByRole:()=>Ue,__experimentalGetBlockLabel:()=>Re,__experimentalSanitizeBlockAttributes:()=>$e,__unstableGetBlockProps:()=>Vr,__unstableGetInnerBlocksProps:()=>$r,__unstableSerializeAndClean:()=>Wr,children:()=>Gn,cloneBlock:()=>kr,createBlock:()=>mr,createBlocksFromInnerBlocksTemplate:()=>br,doBlocksMatchTemplate:()=>Yo,findTransform:()=>Er,getBlockAttributes:()=>Xn,getBlockContent:()=>qr,getBlockDefaultClassName:()=>zr,getBlockFromExample:()=>Nr,getBlockMenuDefaultClassName:()=>Ir,getBlockSupport:()=>we,getBlockTransforms:()=>Sr,getBlockType:()=>ke,getBlockTypes:()=>ye,getBlockVariations:()=>Ne,getCategories:()=>Go,getChildBlockNames:()=>xe,getDefaultBlockName:()=>_e,getFreeformContentHandlerName:()=>pe,getGroupingBlockName:()=>he,getPhrasingContentSchema:()=>vo,getPossibleBlockTransformations:()=>xr,getSaveContent:()=>Fr,getSaveElement:()=>Ur,getUnregisteredTypeHandlerName:()=>ge,hasBlockSupport:()=>ve,hasChildBlocks:()=>Ee,hasChildBlocksWithInserterSupport:()=>Se,isReusableBlock:()=>Te,isTemplatePart:()=>Ce,isUnmodifiedBlock:()=>Me,isUnmodifiedDefaultBlock:()=>je,isValidBlockContent:()=>Ln,isValidIcon:()=>De,node:()=>$n,normalizeIconObject:()=>ze,parse:()=>oo,parseWithAttributeSchema:()=>Zn,pasteHandler:()=>qo,rawHandler:()=>To,registerBlockCollection:()=>le,registerBlockStyle:()=>Be,registerBlockType:()=>se,registerBlockVariation:()=>Pe,serialize:()=>Yr,serializeRawBlock:()=>Dr,setCategories:()=>Ko,setDefaultBlockName:()=>me,setFreeformContentHandlerName:()=>de,setGroupingBlockName:()=>be,setUnregisteredTypeHandlerName:()=>fe,store:()=>cr,switchToBlockType:()=>Ar,synchronizeBlocksWithTemplate:()=>Qo,unregisterBlockStyle:()=>Ae,unregisterBlockType:()=>ue,unregisterBlockVariation:()=>Oe,unstable__bootstrapServerSideBlockDefinitions:()=>ae,updateCategory:()=>Wo,validateBlock:()=>On,withBlockContentContext:()=>Zo});var e={};r.r(e),r.d(e,{__experimentalHasContentRoleAttribute:()=>St,getActiveBlockVariation:()=>ht,getBlockStyles:()=>dt,getBlockSupport:()=>vt,getBlockType:()=>ut,getBlockTypes:()=>lt,getBlockVariations:()=>pt,getCategories:()=>gt,getChildBlockNames:()=>wt,getCollections:()=>mt,getDefaultBlockName:()=>bt,getDefaultBlockVariation:()=>ft,getFreeformFallbackBlockName:()=>_t,getGroupingBlockName:()=>yt,getUnregisteredFallbackBlockName:()=>kt,hasBlockSupport:()=>Tt,hasChildBlocks:()=>xt,hasChildBlocksWithInserterSupport:()=>Et,isMatchingSearchTerm:()=>Ct});var t={};r.r(t),r.d(t,{getAllBlockBindingsSources:()=>Lt,getBlockBindingsSource:()=>Mt,getBootstrappedBlockType:()=>Pt,getSupportedStyles:()=>Nt,getUnprocessedBlockTypes:()=>Ot});var o={};r.r(o),r.d(o,{__experimentalReapplyBlockFilters:()=>qt,addBlockCollection:()=>nr,addBlockStyles:()=>Kt,addBlockTypes:()=>Ut,addBlockVariations:()=>Yt,reapplyBlockTypeFilters:()=>Ft,removeBlockCollection:()=>or,removeBlockStyles:()=>Wt,removeBlockTypes:()=>Gt,removeBlockVariations:()=>Qt,setCategories:()=>tr,setDefaultBlockName:()=>Zt,setFreeformFallbackBlockName:()=>Xt,setGroupingBlockName:()=>er,setUnregisteredFallbackBlockName:()=>Jt,updateCategory:()=>rr});var a={};r.r(a),r.d(a,{addBootstrappedBlockType:()=>ar,addUnprocessedBlockType:()=>ir,registerBlockBindingsSource:()=>sr});const i=window.wp.data;var s=function(){return s=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},s.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function c(e){return e.toLowerCase()}var l=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],u=/[^A-Z0-9]+/gi;function d(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce((function(e,t){return e.replace(t,r)}),e)}function p(e,t){var r=e.charAt(0),n=e.substr(1).toLowerCase();return t>0&&r>="0"&&r<="9"?"_"+r+n:""+r.toUpperCase()+n}function h(e,t){return void 0===t&&(t={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,n=void 0===r?l:r,o=t.stripRegexp,a=void 0===o?u:o,i=t.transform,s=void 0===i?c:i,p=t.delimiter,h=void 0===p?" ":p,f=d(d(e,n,"$1\0$2"),a,"\0"),g=0,m=f.length;"\0"===f.charAt(g);)g++;for(;"\0"===f.charAt(m-1);)m--;return f.slice(g,m).split("\0").map(s).join(h)}(e,s({delimiter:"",transform:p},t))}function f(e,t){return 0===t?e.toLowerCase():p(e,t)}const g=window.wp.i18n;var m={grad:.9,turn:360,rad:360/(2*Math.PI)},b=function(e){return"string"==typeof e?e.length>0:"number"==typeof e},_=function(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=Math.pow(10,t)),Math.round(r*e)/r+0},k=function(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=1),e>r?r:e>t?e:t},y=function(e){return(e=isFinite(e)?e%360:0)>0?e:e+360},w=function(e){return{r:k(e.r,0,255),g:k(e.g,0,255),b:k(e.b,0,255),a:k(e.a)}},v=function(e){return{r:_(e.r),g:_(e.g),b:_(e.b),a:_(e.a,3)}},T=/^#([0-9a-f]{3,8})$/i,C=function(e){var t=e.toString(16);return t.length<2?"0"+t:t},x=function(e){var t=e.r,r=e.g,n=e.b,o=e.a,a=Math.max(t,r,n),i=a-Math.min(t,r,n),s=i?a===t?(r-n)/i:a===r?2+(n-t)/i:4+(t-r)/i:0;return{h:60*(s<0?s+6:s),s:a?i/a*100:0,v:a/255*100,a:o}},E=function(e){var t=e.h,r=e.s,n=e.v,o=e.a;t=t/360*6,r/=100,n/=100;var a=Math.floor(t),i=n*(1-r),s=n*(1-(t-a)*r),c=n*(1-(1-t+a)*r),l=a%6;return{r:255*[n,s,i,i,c,n][l],g:255*[c,n,n,s,i,i][l],b:255*[i,i,c,n,n,s][l],a:o}},S=function(e){return{h:y(e.h),s:k(e.s,0,100),l:k(e.l,0,100),a:k(e.a)}},B=function(e){return{h:_(e.h),s:_(e.s),l:_(e.l),a:_(e.a,3)}},A=function(e){return E((r=(t=e).s,{h:t.h,s:(r*=((n=t.l)<50?n:100-n)/100)>0?2*r/(n+r)*100:0,v:n+r,a:t.a}));var t,r,n},N=function(e){return{h:(t=x(e)).h,s:(o=(200-(r=t.s))*(n=t.v)/100)>0&&o<200?r*n/100/(o<=100?o:200-o)*100:0,l:o/2,a:t.a};var t,r,n,o},P=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s*,\s*([+-]?\d*\.?\d+)%\s*,\s*([+-]?\d*\.?\d+)%\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,O=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s+([+-]?\d*\.?\d+)%\s+([+-]?\d*\.?\d+)%\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,L=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,M=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,j={string:[[function(e){var t=T.exec(e);return t?(e=t[1]).length<=4?{r:parseInt(e[0]+e[0],16),g:parseInt(e[1]+e[1],16),b:parseInt(e[2]+e[2],16),a:4===e.length?_(parseInt(e[3]+e[3],16)/255,2):1}:6===e.length||8===e.length?{r:parseInt(e.substr(0,2),16),g:parseInt(e.substr(2,2),16),b:parseInt(e.substr(4,2),16),a:8===e.length?_(parseInt(e.substr(6,2),16)/255,2):1}:null:null},"hex"],[function(e){var t=L.exec(e)||M.exec(e);return t?t[2]!==t[4]||t[4]!==t[6]?null:w({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:void 0===t[7]?1:Number(t[7])/(t[8]?100:1)}):null},"rgb"],[function(e){var t=P.exec(e)||O.exec(e);if(!t)return null;var r,n,o=S({h:(r=t[1],n=t[2],void 0===n&&(n="deg"),Number(r)*(m[n]||1)),s:Number(t[3]),l:Number(t[4]),a:void 0===t[5]?1:Number(t[5])/(t[6]?100:1)});return A(o)},"hsl"]],object:[[function(e){var t=e.r,r=e.g,n=e.b,o=e.a,a=void 0===o?1:o;return b(t)&&b(r)&&b(n)?w({r:Number(t),g:Number(r),b:Number(n),a:Number(a)}):null},"rgb"],[function(e){var t=e.h,r=e.s,n=e.l,o=e.a,a=void 0===o?1:o;if(!b(t)||!b(r)||!b(n))return null;var i=S({h:Number(t),s:Number(r),l:Number(n),a:Number(a)});return A(i)},"hsl"],[function(e){var t=e.h,r=e.s,n=e.v,o=e.a,a=void 0===o?1:o;if(!b(t)||!b(r)||!b(n))return null;var i=function(e){return{h:y(e.h),s:k(e.s,0,100),v:k(e.v,0,100),a:k(e.a)}}({h:Number(t),s:Number(r),v:Number(n),a:Number(a)});return E(i)},"hsv"]]},D=function(e,t){for(var r=0;r<t.length;r++){var n=t[r][0](e);if(n)return[n,t[r][1]]}return[null,void 0]},z=function(e){return"string"==typeof e?D(e.trim(),j.string):"object"==typeof e&&null!==e?D(e,j.object):[null,void 0]},I=function(e,t){var r=N(e);return{h:r.h,s:k(r.s+100*t,0,100),l:r.l,a:r.a}},R=function(e){return(299*e.r+587*e.g+114*e.b)/1e3/255},H=function(e,t){var r=N(e);return{h:r.h,s:r.s,l:k(r.l+100*t,0,100),a:r.a}},V=function(){function e(e){this.parsed=z(e)[0],this.rgba=this.parsed||{r:0,g:0,b:0,a:1}}return e.prototype.isValid=function(){return null!==this.parsed},e.prototype.brightness=function(){return _(R(this.rgba),2)},e.prototype.isDark=function(){return R(this.rgba)<.5},e.prototype.isLight=function(){return R(this.rgba)>=.5},e.prototype.toHex=function(){return t=(e=v(this.rgba)).r,r=e.g,n=e.b,a=(o=e.a)<1?C(_(255*o)):"","#"+C(t)+C(r)+C(n)+a;var e,t,r,n,o,a},e.prototype.toRgb=function(){return v(this.rgba)},e.prototype.toRgbString=function(){return t=(e=v(this.rgba)).r,r=e.g,n=e.b,(o=e.a)<1?"rgba("+t+", "+r+", "+n+", "+o+")":"rgb("+t+", "+r+", "+n+")";var e,t,r,n,o},e.prototype.toHsl=function(){return B(N(this.rgba))},e.prototype.toHslString=function(){return t=(e=B(N(this.rgba))).h,r=e.s,n=e.l,(o=e.a)<1?"hsla("+t+", "+r+"%, "+n+"%, "+o+")":"hsl("+t+", "+r+"%, "+n+"%)";var e,t,r,n,o},e.prototype.toHsv=function(){return e=x(this.rgba),{h:_(e.h),s:_(e.s),v:_(e.v),a:_(e.a,3)};var e},e.prototype.invert=function(){return $({r:255-(e=this.rgba).r,g:255-e.g,b:255-e.b,a:e.a});var e},e.prototype.saturate=function(e){return void 0===e&&(e=.1),$(I(this.rgba,e))},e.prototype.desaturate=function(e){return void 0===e&&(e=.1),$(I(this.rgba,-e))},e.prototype.grayscale=function(){return $(I(this.rgba,-1))},e.prototype.lighten=function(e){return void 0===e&&(e=.1),$(H(this.rgba,e))},e.prototype.darken=function(e){return void 0===e&&(e=.1),$(H(this.rgba,-e))},e.prototype.rotate=function(e){return void 0===e&&(e=15),this.hue(this.hue()+e)},e.prototype.alpha=function(e){return"number"==typeof e?$({r:(t=this.rgba).r,g:t.g,b:t.b,a:e}):_(this.rgba.a,3);var t},e.prototype.hue=function(e){var t=N(this.rgba);return"number"==typeof e?$({h:e,s:t.s,l:t.l,a:t.a}):_(t.h)},e.prototype.isEqual=function(e){return this.toHex()===$(e).toHex()},e}(),$=function(e){return e instanceof V?e:new V(e)},U=[];var F=function(e){var t=e/255;return t<.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)},q=function(e){return.2126*F(e.r)+.7152*F(e.g)+.0722*F(e.b)};const G=window.wp.element,K=window.wp.dom,W=window.wp.richText,Y="block-default",Q=["attributes","supports","save","migrate","isEligible","apiVersion"],Z={"--wp--style--color--link":{value:["color","link"],support:["color","link"]},aspectRatio:{value:["dimensions","aspectRatio"],support:["dimensions","aspectRatio"],useEngine:!0},background:{value:["color","gradient"],support:["color","gradients"],useEngine:!0},backgroundColor:{value:["color","background"],support:["color","background"],requiresOptOut:!0,useEngine:!0},backgroundRepeat:{value:["background","backgroundRepeat"],support:["background","backgroundRepeat"],useEngine:!0},backgroundSize:{value:["background","backgroundSize"],support:["background","backgroundSize"],useEngine:!0},borderColor:{value:["border","color"],support:["__experimentalBorder","color"],useEngine:!0},borderRadius:{value:["border","radius"],support:["__experimentalBorder","radius"],properties:{borderTopLeftRadius:"topLeft",borderTopRightRadius:"topRight",borderBottomLeftRadius:"bottomLeft",borderBottomRightRadius:"bottomRight"},useEngine:!0},borderStyle:{value:["border","style"],support:["__experimentalBorder","style"],useEngine:!0},borderWidth:{value:["border","width"],support:["__experimentalBorder","width"],useEngine:!0},borderTopColor:{value:["border","top","color"],support:["__experimentalBorder","color"],useEngine:!0},borderTopStyle:{value:["border","top","style"],support:["__experimentalBorder","style"],useEngine:!0},borderTopWidth:{value:["border","top","width"],support:["__experimentalBorder","width"],useEngine:!0},borderRightColor:{value:["border","right","color"],support:["__experimentalBorder","color"],useEngine:!0},borderRightStyle:{value:["border","right","style"],support:["__experimentalBorder","style"],useEngine:!0},borderRightWidth:{value:["border","right","width"],support:["__experimentalBorder","width"],useEngine:!0},borderBottomColor:{value:["border","bottom","color"],support:["__experimentalBorder","color"],useEngine:!0},borderBottomStyle:{value:["border","bottom","style"],support:["__experimentalBorder","style"],useEngine:!0},borderBottomWidth:{value:["border","bottom","width"],support:["__experimentalBorder","width"],useEngine:!0},borderLeftColor:{value:["border","left","color"],support:["__experimentalBorder","color"],useEngine:!0},borderLeftStyle:{value:["border","left","style"],support:["__experimentalBorder","style"],useEngine:!0},borderLeftWidth:{value:["border","left","width"],support:["__experimentalBorder","width"],useEngine:!0},color:{value:["color","text"],support:["color","text"],requiresOptOut:!0,useEngine:!0},columnCount:{value:["typography","textColumns"],support:["typography","textColumns"],useEngine:!0},filter:{value:["filter","duotone"],support:["filter","duotone"]},linkColor:{value:["elements","link","color","text"],support:["color","link"]},captionColor:{value:["elements","caption","color","text"],support:["color","caption"]},buttonColor:{value:["elements","button","color","text"],support:["color","button"]},buttonBackgroundColor:{value:["elements","button","color","background"],support:["color","button"]},headingColor:{value:["elements","heading","color","text"],support:["color","heading"]},headingBackgroundColor:{value:["elements","heading","color","background"],support:["color","heading"]},fontFamily:{value:["typography","fontFamily"],support:["typography","__experimentalFontFamily"],useEngine:!0},fontSize:{value:["typography","fontSize"],support:["typography","fontSize"],useEngine:!0},fontStyle:{value:["typography","fontStyle"],support:["typography","__experimentalFontStyle"],useEngine:!0},fontWeight:{value:["typography","fontWeight"],support:["typography","__experimentalFontWeight"],useEngine:!0},lineHeight:{value:["typography","lineHeight"],support:["typography","lineHeight"],useEngine:!0},margin:{value:["spacing","margin"],support:["spacing","margin"],properties:{marginTop:"top",marginRight:"right",marginBottom:"bottom",marginLeft:"left"},useEngine:!0},minHeight:{value:["dimensions","minHeight"],support:["dimensions","minHeight"],useEngine:!0},padding:{value:["spacing","padding"],support:["spacing","padding"],properties:{paddingTop:"top",paddingRight:"right",paddingBottom:"bottom",paddingLeft:"left"},useEngine:!0},textDecoration:{value:["typography","textDecoration"],support:["typography","__experimentalTextDecoration"],useEngine:!0},textTransform:{value:["typography","textTransform"],support:["typography","__experimentalTextTransform"],useEngine:!0},letterSpacing:{value:["typography","letterSpacing"],support:["typography","__experimentalLetterSpacing"],useEngine:!0},writingMode:{value:["typography","writingMode"],support:["typography","__experimentalWritingMode"],useEngine:!0},"--wp--style--root--padding":{value:["spacing","padding"],support:["spacing","padding"],properties:{"--wp--style--root--padding-top":"top","--wp--style--root--padding-right":"right","--wp--style--root--padding-bottom":"bottom","--wp--style--root--padding-left":"left"},rootOnly:!0}},X={link:"a",heading:"h1, h2, h3, h4, h5, h6",h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",button:".wp-element-button, .wp-block-button__link",caption:".wp-element-caption, .wp-block-audio figcaption, .wp-block-embed figcaption, .wp-block-gallery figcaption, .wp-block-image figcaption, .wp-block-table figcaption, .wp-block-video figcaption",cite:"cite"},J={"color.duotone":!0,"color.gradients":!0,"color.palette":!0,"typography.fontSizes":!0,"spacing.spacingSizes":!0},ee=window.wp.privateApis,{lock:te,unlock:re}=(0,ee.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I know using unstable features means my theme or plugin will inevitably break in the next version of WordPress.","@wordpress/blocks"),ne={title:"block title",description:"block description",keywords:["block keyword"],styles:[{label:"block style label"}],variations:[{title:"block variation title",description:"block variation description",keywords:["block variation keyword"]}]};function oe(e){return null!==e&&"object"==typeof e}function ae(e){const{addBootstrappedBlockType:t}=re((0,i.dispatch)(cr));for(const[r,n]of Object.entries(e))t(r,n)}function ie({textdomain:e,...t}){const r=["apiVersion","title","category","parent","ancestor","icon","description","keywords","attributes","providesContext","usesContext","selectors","supports","styles","example","variations","blockHooks","allowedBlocks"],n=Object.fromEntries(Object.entries(t).filter((([e])=>r.includes(e))));return e&&Object.keys(ne).forEach((t=>{n[t]&&(n[t]=ce(ne[t],n[t],e))})),n}function se(e,t){const r=oe(e)?e.name:e;if("string"!=typeof r)return void console.error("Block names must be strings.");if(!/^[a-z][a-z0-9-]*\/[a-z][a-z0-9-]*$/.test(r))return void console.error("Block names must contain a namespace prefix, include only lowercase alphanumeric characters or dashes, and start with a letter. Example: my-plugin/my-custom-block");if((0,i.select)(cr).getBlockType(r))return void console.error('Block "'+r+'" is already registered.');const{addBootstrappedBlockType:n,addUnprocessedBlockType:o}=re((0,i.dispatch)(cr));if(oe(e)){n(r,ie(e))}return o(r,t),(0,i.select)(cr).getBlockType(r)}function ce(e,t,r){return"string"==typeof e&&"string"==typeof t?(0,g._x)(t,e,r):Array.isArray(e)&&e.length&&Array.isArray(t)?t.map((t=>ce(e[0],t,r))):oe(e)&&Object.entries(e).length&&oe(t)?Object.keys(t).reduce(((n,o)=>e[o]?(n[o]=ce(e[o],t[o],r),n):(n[o]=t[o],n)),{}):t}function le(e,{title:t,icon:r}){(0,i.dispatch)(cr).addBlockCollection(e,t,r)}function ue(e){const t=(0,i.select)(cr).getBlockType(e);if(t)return(0,i.dispatch)(cr).removeBlockTypes(e),t;console.error('Block "'+e+'" is not registered.')}function de(e){(0,i.dispatch)(cr).setFreeformFallbackBlockName(e)}function pe(){return(0,i.select)(cr).getFreeformFallbackBlockName()}function he(){return(0,i.select)(cr).getGroupingBlockName()}function fe(e){(0,i.dispatch)(cr).setUnregisteredFallbackBlockName(e)}function ge(){return(0,i.select)(cr).getUnregisteredFallbackBlockName()}function me(e){(0,i.dispatch)(cr).setDefaultBlockName(e)}function be(e){(0,i.dispatch)(cr).setGroupingBlockName(e)}function _e(){return(0,i.select)(cr).getDefaultBlockName()}function ke(e){return(0,i.select)(cr)?.getBlockType(e)}function ye(){return(0,i.select)(cr).getBlockTypes()}function we(e,t,r){return(0,i.select)(cr).getBlockSupport(e,t,r)}function ve(e,t,r){return(0,i.select)(cr).hasBlockSupport(e,t,r)}function Te(e){return"core/block"===e?.name}function Ce(e){return"core/template-part"===e?.name}const xe=e=>(0,i.select)(cr).getChildBlockNames(e),Ee=e=>(0,i.select)(cr).hasChildBlocks(e),Se=e=>(0,i.select)(cr).hasChildBlocksWithInserterSupport(e),Be=(e,t)=>{(0,i.dispatch)(cr).addBlockStyles(e,t)},Ae=(e,t)=>{(0,i.dispatch)(cr).removeBlockStyles(e,t)},Ne=(e,t)=>(0,i.select)(cr).getBlockVariations(e,t),Pe=(e,t)=>{(0,i.dispatch)(cr).addBlockVariations(e,t)},Oe=(e,t)=>{(0,i.dispatch)(cr).removeBlockVariations(e,t)};!function(e){e.forEach((function(e){U.indexOf(e)<0&&(e(V,j),U.push(e))}))}([function(e,t){var r={white:"#ffffff",bisque:"#ffe4c4",blue:"#0000ff",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",antiquewhite:"#faebd7",aqua:"#00ffff",azure:"#f0ffff",whitesmoke:"#f5f5f5",papayawhip:"#ffefd5",plum:"#dda0dd",blanchedalmond:"#ffebcd",black:"#000000",gold:"#ffd700",goldenrod:"#daa520",gainsboro:"#dcdcdc",cornsilk:"#fff8dc",cornflowerblue:"#6495ed",burlywood:"#deb887",aquamarine:"#7fffd4",beige:"#f5f5dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkkhaki:"#bdb76b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",peachpuff:"#ffdab9",darkmagenta:"#8b008b",darkred:"#8b0000",darkorchid:"#9932cc",darkorange:"#ff8c00",darkslateblue:"#483d8b",gray:"#808080",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",deeppink:"#ff1493",deepskyblue:"#00bfff",wheat:"#f5deb3",firebrick:"#b22222",floralwhite:"#fffaf0",ghostwhite:"#f8f8ff",darkviolet:"#9400d3",magenta:"#ff00ff",green:"#008000",dodgerblue:"#1e90ff",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",blueviolet:"#8a2be2",forestgreen:"#228b22",lawngreen:"#7cfc00",indianred:"#cd5c5c",indigo:"#4b0082",fuchsia:"#ff00ff",brown:"#a52a2a",maroon:"#800000",mediumblue:"#0000cd",lightcoral:"#f08080",darkturquoise:"#00ced1",lightcyan:"#e0ffff",ivory:"#fffff0",lightyellow:"#ffffe0",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",linen:"#faf0e6",mediumaquamarine:"#66cdaa",lemonchiffon:"#fffacd",lime:"#00ff00",khaki:"#f0e68c",mediumseagreen:"#3cb371",limegreen:"#32cd32",mediumspringgreen:"#00fa9a",lightskyblue:"#87cefa",lightblue:"#add8e6",midnightblue:"#191970",lightpink:"#ffb6c1",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",mintcream:"#f5fffa",lightslategray:"#778899",lightslategrey:"#778899",navajowhite:"#ffdead",navy:"#000080",mediumvioletred:"#c71585",powderblue:"#b0e0e6",palegoldenrod:"#eee8aa",oldlace:"#fdf5e6",paleturquoise:"#afeeee",mediumturquoise:"#48d1cc",mediumorchid:"#ba55d3",rebeccapurple:"#663399",lightsteelblue:"#b0c4de",mediumslateblue:"#7b68ee",thistle:"#d8bfd8",tan:"#d2b48c",orchid:"#da70d6",mediumpurple:"#9370db",purple:"#800080",pink:"#ffc0cb",skyblue:"#87ceeb",springgreen:"#00ff7f",palegreen:"#98fb98",red:"#ff0000",yellow:"#ffff00",slateblue:"#6a5acd",lavenderblush:"#fff0f5",peru:"#cd853f",palevioletred:"#db7093",violet:"#ee82ee",teal:"#008080",slategray:"#708090",slategrey:"#708090",aliceblue:"#f0f8ff",darkseagreen:"#8fbc8f",darkolivegreen:"#556b2f",greenyellow:"#adff2f",seagreen:"#2e8b57",seashell:"#fff5ee",tomato:"#ff6347",silver:"#c0c0c0",sienna:"#a0522d",lavender:"#e6e6fa",lightgreen:"#90ee90",orange:"#ffa500",orangered:"#ff4500",steelblue:"#4682b4",royalblue:"#4169e1",turquoise:"#40e0d0",yellowgreen:"#9acd32",salmon:"#fa8072",saddlebrown:"#8b4513",sandybrown:"#f4a460",rosybrown:"#bc8f8f",darksalmon:"#e9967a",lightgoldenrodyellow:"#fafad2",snow:"#fffafa",lightgrey:"#d3d3d3",lightgray:"#d3d3d3",dimgray:"#696969",dimgrey:"#696969",olivedrab:"#6b8e23",olive:"#808000"},n={};for(var o in r)n[r[o]]=o;var a={};e.prototype.toName=function(t){if(!(this.rgba.a||this.rgba.r||this.rgba.g||this.rgba.b))return"transparent";var o,i,s=n[this.toHex()];if(s)return s;if(null==t?void 0:t.closest){var c=this.toRgb(),l=1/0,u="black";if(!a.length)for(var d in r)a[d]=new e(r[d]).toRgb();for(var p in r){var h=(o=c,i=a[p],Math.pow(o.r-i.r,2)+Math.pow(o.g-i.g,2)+Math.pow(o.b-i.b,2));h<l&&(l=h,u=p)}return u}},t.string.push([function(t){var n=t.toLowerCase(),o="transparent"===n?"#0000":r[n];return o?new e(o).toRgb():null},"name"])},function(e){e.prototype.luminance=function(){return e=q(this.rgba),void 0===(t=2)&&(t=0),void 0===r&&(r=Math.pow(10,t)),Math.round(r*e)/r+0;var e,t,r},e.prototype.contrast=function(t){void 0===t&&(t="#FFF");var r,n,o,a,i,s,c,l=t instanceof e?t:new e(t);return a=this.rgba,i=l.toRgb(),r=(s=q(a))>(c=q(i))?(s+.05)/(c+.05):(c+.05)/(s+.05),void 0===(n=2)&&(n=0),void 0===o&&(o=Math.pow(10,n)),Math.floor(o*r)/o+0},e.prototype.isReadable=function(e,t){return void 0===e&&(e="#FFF"),void 0===t&&(t={}),this.contrast(e)>=(i=void 0===(a=(r=t).size)?"normal":a,"AAA"===(o=void 0===(n=r.level)?"AA":n)&&"normal"===i?7:"AA"===o&&"large"===i?3:4.5);var r,n,o,a,i}}]);const Le=["#191e23","#f8f9f9"];function Me(e){var t;return Object.entries(null!==(t=ke(e.name)?.attributes)&&void 0!==t?t:{}).every((([t,r])=>{const n=e.attributes[t];return r.hasOwnProperty("default")?n===r.default:"rich-text"===r.type?!n?.length:void 0===n}))}function je(e){return e.name===_e()&&Me(e)}function De(e){return!!e&&("string"==typeof e||(0,G.isValidElement)(e)||"function"==typeof e||e instanceof G.Component)}function ze(e){if(De(e=e||Y))return{src:e};if("background"in e){const t=$(e.background),r=e=>t.contrast(e),n=Math.max(...Le.map(r));return{...e,foreground:e.foreground?e.foreground:Le.find((e=>r(e)===n)),shadowColor:t.alpha(.3).toRgbString()}}return e}function Ie(e){return"string"==typeof e?ke(e):e}function Re(e,t,r="visual"){const{__experimentalLabel:n,title:o}=e,a=n&&n(t,{context:r});return a?(0,K.__unstableStripHTML)(a):o}function He(e,t,r,n="vertical"){const o=e?.title,a=e?Re(e,t,"accessibility"):"",i=void 0!==r,s=a&&a!==o;return i&&"vertical"===n?s?(0,g.sprintf)((0,g.__)("%1$s Block. Row %2$d. %3$s"),o,r,a):(0,g.sprintf)((0,g.__)("%1$s Block. Row %2$d"),o,r):i&&"horizontal"===n?s?(0,g.sprintf)((0,g.__)("%1$s Block. Column %2$d. %3$s"),o,r,a):(0,g.sprintf)((0,g.__)("%1$s Block. Column %2$d"),o,r):s?(0,g.sprintf)((0,g.__)("%1$s Block. %2$s"),o,a):(0,g.sprintf)((0,g.__)("%s Block"),o)}function Ve(e){return void 0!==e.default?e.default:"rich-text"===e.type?new W.RichTextData:void 0}function $e(e,t){const r=ke(e);if(void 0===r)throw new Error(`Block type '${e}' is not registered.`);return Object.entries(r.attributes).reduce(((e,[r,n])=>{const o=t[r];if(void 0!==o)"rich-text"===n.type?o instanceof W.RichTextData?e[r]=o:"string"==typeof o&&(e[r]=W.RichTextData.fromHTMLString(o)):"string"===n.type&&o instanceof W.RichTextData?e[r]=o.toHTMLString():e[r]=o;else{const t=Ve(n);void 0!==t&&(e[r]=t)}return-1!==["node","children"].indexOf(n.source)&&("string"==typeof e[r]?e[r]=[e[r]]:Array.isArray(e[r])||(e[r]=[])),e}),{})}function Ue(e,t){const r=ke(e)?.attributes;if(!r)return[];const n=Object.keys(r);return t?n.filter((e=>r[e]?.__experimentalRole===t)):n}function Fe(e,t){return Object.fromEntries(Object.entries(e).filter((([e])=>!t.includes(e))))}const qe=[{slug:"text",title:(0,g.__)("Text")},{slug:"media",title:(0,g.__)("Media")},{slug:"design",title:(0,g.__)("Design")},{slug:"widgets",title:(0,g.__)("Widgets")},{slug:"theme",title:(0,g.__)("Theme")},{slug:"embed",title:(0,g.__)("Embeds")},{slug:"reusable",title:(0,g.__)("Reusable blocks")}];function Ge(e){return e.reduce(((e,t)=>({...e,[t.name]:t})),{})}function Ke(e){return e.reduce(((e,t)=>(e.some((e=>e.name===t.name))||e.push(t),e)),[])}function We(e){return(t=null,r)=>{switch(r.type){case"REMOVE_BLOCK_TYPES":return-1!==r.names.indexOf(t)?null:t;case e:return r.name||null}return t}}const Ye=We("SET_DEFAULT_BLOCK_NAME"),Qe=We("SET_FREEFORM_FALLBACK_BLOCK_NAME"),Ze=We("SET_UNREGISTERED_FALLBACK_BLOCK_NAME"),Xe=We("SET_GROUPING_BLOCK_NAME");const Je=(0,i.combineReducers)({bootstrappedBlockTypes:function(e={},t){switch(t.type){case"ADD_BOOTSTRAPPED_BLOCK_TYPE":const{name:r,blockType:n}=t,o=e[r];let a;return o?(void 0===o.selectors&&n.selectors&&(a={...o,selectors:n.selectors}),void 0===o.blockHooks&&n.blockHooks&&(a={...o,...a,blockHooks:n.blockHooks}),void 0===o.allowedBlocks&&n.allowedBlocks&&(a={...o,...a,allowedBlocks:n.allowedBlocks})):(a=Object.fromEntries(Object.entries(n).filter((([,e])=>null!=e)).map((([e,t])=>{return[(r=e,void 0===n&&(n={}),h(r,s({transform:f},n))),t];var r,n}))),a.name=r),a?{...e,[r]:a}:e;case"REMOVE_BLOCK_TYPES":return Fe(e,t.names)}return e},unprocessedBlockTypes:function(e={},t){switch(t.type){case"ADD_UNPROCESSED_BLOCK_TYPE":return{...e,[t.name]:t.blockType};case"REMOVE_BLOCK_TYPES":return Fe(e,t.names)}return e},blockTypes:function(e={},t){switch(t.type){case"ADD_BLOCK_TYPES":return{...e,...Ge(t.blockTypes)};case"REMOVE_BLOCK_TYPES":return Fe(e,t.names)}return e},blockStyles:function(e={},t){var r,n;switch(t.type){case"ADD_BLOCK_TYPES":return{...e,...Object.fromEntries(Object.entries(Ge(t.blockTypes)).map((([t,r])=>{var n,o;return[t,Ke([...(null!==(n=r.styles)&&void 0!==n?n:[]).map((e=>({...e,source:"block"}))),...(null!==(o=e[r.name])&&void 0!==o?o:[]).filter((({source:e})=>"block"!==e))])]})))};case"ADD_BLOCK_STYLES":return{...e,[t.blockName]:Ke([...null!==(r=e[t.blockName])&&void 0!==r?r:[],...t.styles])};case"REMOVE_BLOCK_STYLES":return{...e,[t.blockName]:(null!==(n=e[t.blockName])&&void 0!==n?n:[]).filter((e=>-1===t.styleNames.indexOf(e.name)))}}return e},blockVariations:function(e={},t){var r,n;switch(t.type){case"ADD_BLOCK_TYPES":return{...e,...Object.fromEntries(Object.entries(Ge(t.blockTypes)).map((([t,r])=>{var n,o;return[t,Ke([...(null!==(n=r.variations)&&void 0!==n?n:[]).map((e=>({...e,source:"block"}))),...(null!==(o=e[r.name])&&void 0!==o?o:[]).filter((({source:e})=>"block"!==e))])]})))};case"ADD_BLOCK_VARIATIONS":return{...e,[t.blockName]:Ke([...null!==(r=e[t.blockName])&&void 0!==r?r:[],...t.variations])};case"REMOVE_BLOCK_VARIATIONS":return{...e,[t.blockName]:(null!==(n=e[t.blockName])&&void 0!==n?n:[]).filter((e=>-1===t.variationNames.indexOf(e.name)))}}return e},defaultBlockName:Ye,freeformFallbackBlockName:Qe,unregisteredFallbackBlockName:Ze,groupingBlockName:Xe,categories:function(e=qe,t){switch(t.type){case"SET_CATEGORIES":return t.categories||[];case"UPDATE_CATEGORY":if(!t.category||!Object.keys(t.category).length)return e;if(e.find((({slug:e})=>e===t.slug)))return e.map((e=>e.slug===t.slug?{...e,...t.category}:e))}return e},collections:function(e={},t){switch(t.type){case"ADD_BLOCK_COLLECTION":return{...e,[t.namespace]:{title:t.title,icon:t.icon}};case"REMOVE_BLOCK_COLLECTION":return Fe(e,t.namespace)}return e},blockBindingsSources:function(e={},t){var r;return"REGISTER_BLOCK_BINDINGS_SOURCE"===t.type?{...e,[t.sourceName]:{label:t.sourceLabel,useSource:t.useSource,lockAttributesEditing:null===(r=t.lockAttributesEditing)||void 0===r||r}}:e}});var et={};function tt(e){return[e]}function rt(e,t,r){var n;if(e.length!==t.length)return!1;for(n=r;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function nt(e,t){var r,n=t||tt;function o(){r=new WeakMap}function a(){var t,o,a,i,s,c=arguments.length;for(i=new Array(c),a=0;a<c;a++)i[a]=arguments[a];for(t=function(e){var t,n,o,a,i,s=r,c=!0;for(t=0;t<e.length;t++){if(!(i=n=e[t])||"object"!=typeof i){c=!1;break}s.has(n)?s=s.get(n):(o=new WeakMap,s.set(n,o),s=o)}return s.has(et)||((a=function(){var e={clear:function(){e.head=null}};return e}()).isUniqueByDependants=c,s.set(et,a)),s.get(et)}(s=n.apply(null,i)),t.isUniqueByDependants||(t.lastDependants&&!rt(s,t.lastDependants,0)&&t.clear(),t.lastDependants=s),o=t.head;o;){if(rt(o.args,i,1))return o!==t.head&&(o.prev.next=o.next,o.next&&(o.next.prev=o.prev),o.next=t.head,o.prev=null,t.head.prev=o,t.head=o),o.val;o=o.next}return o={val:e.apply(null,i)},i[0]=null,o.args=i,t.head&&(t.head.prev=o,o.next=t.head),t.head=o,o.val}return a.getDependants=n,a.clear=o,o(),a}var ot=r(9681),at=r.n(ot);const it=window.wp.compose,st=(e,t,r)=>{var n;const o=Array.isArray(t)?t:t.split(".");let a=e;return o.forEach((e=>{a=a?.[e]})),null!==(n=a)&&void 0!==n?n:r},ct=(e,t)=>"string"==typeof t?ut(e,t):t,lt=nt((e=>Object.values(e.blockTypes)),(e=>[e.blockTypes]));function ut(e,t){return e.blockTypes[t]}function dt(e,t){return e.blockStyles[t]}const pt=nt(((e,t,r)=>{const n=e.blockVariations[t];return n&&r?n.filter((e=>(e.scope||["block","inserter"]).includes(r))):n}),((e,t)=>[e.blockVariations[t]]));function ht(e,t,r,n){const o=pt(e,t,n),a=o?.find((n=>{if(Array.isArray(n.isActive)){const o=ut(e,t),a=Object.keys(o?.attributes||{}),i=n.isActive.filter((e=>a.includes(e)));return 0!==i.length&&i.every((e=>r[e]===n.attributes[e]))}return n.isActive?.(r,n.attributes)}));return a}function ft(e,t,r){const n=pt(e,t,r);return[...n].reverse().find((({isDefault:e})=>!!e))||n[0]}function gt(e){return e.categories}function mt(e){return e.collections}function bt(e){return e.defaultBlockName}function _t(e){return e.freeformFallbackBlockName}function kt(e){return e.unregisteredFallbackBlockName}function yt(e){return e.groupingBlockName}const wt=nt(((e,t)=>lt(e).filter((e=>e.parent?.includes(t))).map((({name:e})=>e))),(e=>[e.blockTypes])),vt=(e,t,r,n)=>{const o=ct(e,t);return o?.supports?st(o.supports,r,n):n};function Tt(e,t,r,n){return!!vt(e,t,r,n)}function Ct(e,t,r){const n=ct(e,t),o=(0,it.pipe)([e=>at()(null!=e?e:""),e=>e.toLowerCase(),e=>e.trim()]),a=o(r),i=(0,it.pipe)([o,e=>e.includes(a)]);return i(n.title)||n.keywords?.some(i)||i(n.category)||"string"==typeof n.description&&i(n.description)}const xt=(e,t)=>wt(e,t).length>0,Et=(e,t)=>wt(e,t).some((t=>Tt(e,t,"inserter",!0))),St=nt(((e,t)=>{const r=ut(e,t);return!!r&&Object.entries(r.attributes).some((([,{__experimentalRole:e}])=>"content"===e))}),((e,t)=>[e.blockTypes[t]?.attributes])),Bt=["background","backgroundColor","color","linkColor","captionColor","buttonColor","headingColor","fontFamily","fontSize","fontStyle","fontWeight","lineHeight","padding","contentSize","wideSize","blockGap","textDecoration","textTransform","letterSpacing"];function At(e,t,r){return e.filter((e=>("fontSize"!==e||"heading"!==r)&&(!("textDecoration"===e&&!t&&"link"!==r)&&(!("textTransform"===e&&!t&&!["heading","h1","h2","h3","h4","h5","h6"].includes(r)&&"button"!==r&&"caption"!==r&&"text"!==r)&&(!("letterSpacing"===e&&!t&&!["heading","h1","h2","h3","h4","h5","h6"].includes(r)&&"button"!==r&&"caption"!==r&&"text"!==r)&&!("textColumns"===e&&!t))))))}const Nt=nt(((e,t,r)=>{if(!t)return At(Bt,t,r);const n=ut(e,t);if(!n)return[];const o=[];return n?.supports?.spacing?.blockGap&&o.push("blockGap"),n?.supports?.shadow&&o.push("shadow"),Object.keys(Z).forEach((e=>{Z[e].support&&(Z[e].requiresOptOut&&Z[e].support[0]in n.supports&&!1!==st(n.supports,Z[e].support)||st(n.supports,Z[e].support,!1))&&o.push(e)})),At(o,t,r)}),((e,t)=>[e.blockTypes[t]]));function Pt(e,t){return e.bootstrappedBlockTypes[t]}function Ot(e){return e.unprocessedBlockTypes}function Lt(e){return e.blockBindingsSources}function Mt(e,t){return e.blockBindingsSources[t]}const jt=window.wp.deprecated;var Dt=r.n(jt);
/*!
 * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */
function zt(e){return"[object Object]"===Object.prototype.toString.call(e)}var It=r(8529);const Rt=window.wp.hooks,Ht=(...e)=>window?.console?.error?.(...e),Vt={common:"text",formatting:"text",layout:"design"},$t=(e,t)=>({select:r})=>{const n={name:e,icon:Y,keywords:[],attributes:{},providesContext:{},usesContext:[],selectors:{},supports:{},styles:[],variations:[],blockHooks:{},save:()=>null,...r.getBootstrappedBlockType(e),...t},o=(0,Rt.applyFilters)("blocks.registerBlockType",n,e,null);if(o.description&&"string"!=typeof o.description&&Dt()("Declaring non-string block descriptions",{since:"6.2"}),o.deprecated&&(o.deprecated=o.deprecated.map((e=>Object.fromEntries(Object.entries((0,Rt.applyFilters)("blocks.registerBlockType",{...Fe(n,Q),...e},n.name,e)).filter((([e])=>Q.includes(e))))))),function(e){var t,r;return!1!==zt(e)&&(void 0===(t=e.constructor)||!1!==zt(r=t.prototype)&&!1!==r.hasOwnProperty("isPrototypeOf"))}(o))if("function"==typeof o.save)if(!("edit"in o)||(0,It.isValidElementType)(o.edit))if(Vt.hasOwnProperty(o.category)&&(o.category=Vt[o.category]),"category"in o&&!r.getCategories().some((({slug:e})=>e===o.category))&&(((...e)=>{window?.console?.warn?.(...e)})('The block "'+e+'" is registered with an invalid category "'+o.category+'".'),delete o.category),"title"in o&&""!==o.title)if("string"==typeof o.title){if(o.icon=ze(o.icon),De(o.icon.src))return o;Ht("The icon passed is invalid. The icon should be a string, an element, a function, or an object following the specifications documented in https://developer.wordpress.org/block-editor/developers/block-api/block-registration/#icon-optional")}else Ht("Block titles must be strings.");else Ht('The block "'+e+'" must have a title.');else Ht('The "edit" property must be a valid component.');else Ht('The "save" property must be a valid function.');else Ht("Block settings must be a valid object.")};function Ut(e){return{type:"ADD_BLOCK_TYPES",blockTypes:Array.isArray(e)?e:[e]}}function Ft(){return({dispatch:e,select:t})=>{const r=[];for(const[n,o]of Object.entries(t.getUnprocessedBlockTypes())){const t=e($t(n,o));t&&r.push(t)}r.length&&e.addBlockTypes(r)}}function qt(){return Dt()('wp.data.dispatch( "core/blocks" ).__experimentalReapplyBlockFilters',{since:"6.4",alternative:"reapplyBlockFilters"}),Ft()}function Gt(e){return{type:"REMOVE_BLOCK_TYPES",names:Array.isArray(e)?e:[e]}}function Kt(e,t){return{type:"ADD_BLOCK_STYLES",styles:Array.isArray(t)?t:[t],blockName:e}}function Wt(e,t){return{type:"REMOVE_BLOCK_STYLES",styleNames:Array.isArray(t)?t:[t],blockName:e}}function Yt(e,t){return{type:"ADD_BLOCK_VARIATIONS",variations:Array.isArray(t)?t:[t],blockName:e}}function Qt(e,t){return{type:"REMOVE_BLOCK_VARIATIONS",variationNames:Array.isArray(t)?t:[t],blockName:e}}function Zt(e){return{type:"SET_DEFAULT_BLOCK_NAME",name:e}}function Xt(e){return{type:"SET_FREEFORM_FALLBACK_BLOCK_NAME",name:e}}function Jt(e){return{type:"SET_UNREGISTERED_FALLBACK_BLOCK_NAME",name:e}}function er(e){return{type:"SET_GROUPING_BLOCK_NAME",name:e}}function tr(e){return{type:"SET_CATEGORIES",categories:e}}function rr(e,t){return{type:"UPDATE_CATEGORY",slug:e,category:t}}function nr(e,t,r){return{type:"ADD_BLOCK_COLLECTION",namespace:e,title:t,icon:r}}function or(e){return{type:"REMOVE_BLOCK_COLLECTION",namespace:e}}function ar(e,t){return{type:"ADD_BOOTSTRAPPED_BLOCK_TYPE",name:e,blockType:t}}function ir(e,t){return({dispatch:r})=>{r({type:"ADD_UNPROCESSED_BLOCK_TYPE",name:e,blockType:t});const n=r($t(e,t));n&&r.addBlockTypes(n)}}function sr(e){return{type:"REGISTER_BLOCK_BINDINGS_SOURCE",sourceName:e.name,sourceLabel:e.label,useSource:e.useSource,lockAttributesEditing:e.lockAttributesEditing}}const cr=(0,i.createReduxStore)("core/blocks",{reducer:Je,selectors:e,actions:o});(0,i.register)(cr),re(cr).registerPrivateSelectors(t),re(cr).registerPrivateActions(a);const lr={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let ur;const dr=new Uint8Array(16);function pr(){if(!ur&&(ur="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!ur))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return ur(dr)}const hr=[];for(let e=0;e<256;++e)hr.push((e+256).toString(16).slice(1));function fr(e,t=0){return hr[e[t+0]]+hr[e[t+1]]+hr[e[t+2]]+hr[e[t+3]]+"-"+hr[e[t+4]]+hr[e[t+5]]+"-"+hr[e[t+6]]+hr[e[t+7]]+"-"+hr[e[t+8]]+hr[e[t+9]]+"-"+hr[e[t+10]]+hr[e[t+11]]+hr[e[t+12]]+hr[e[t+13]]+hr[e[t+14]]+hr[e[t+15]]}const gr=function(e,t,r){if(lr.randomUUID&&!t&&!e)return lr.randomUUID();const n=(e=e||{}).random||(e.rng||pr)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=n[e];return t}return fr(n)};function mr(e,t={},r=[]){const n=$e(e,t);return{clientId:gr(),name:e,isValid:!0,attributes:n,innerBlocks:r}}function br(e=[]){return e.map((e=>{const t=Array.isArray(e)?e:[e.name,e.attributes,e.innerBlocks],[r,n,o=[]]=t;return mr(r,n,br(o))}))}function _r(e,t={},r){const n=gr(),o=$e(e.name,{...e.attributes,...t});return{...e,clientId:n,attributes:o,innerBlocks:r||e.innerBlocks.map((e=>_r(e)))}}function kr(e,t={},r){const n=gr();return{...e,clientId:n,attributes:{...e.attributes,...t},innerBlocks:r||e.innerBlocks.map((e=>kr(e)))}}const yr=(e,t,r)=>{if(!r.length)return!1;const n=r.length>1,o=r[0].name;if(!(Tr(e)||!n||e.isMultiBlock))return!1;if(!Tr(e)&&!r.every((e=>e.name===o)))return!1;if(!("block"===e.type))return!1;const a=r[0];return!("from"===t&&-1===e.blocks.indexOf(a.name)&&!Tr(e))&&(!(!n&&"from"===t&&Cr(a.name)&&Cr(e.blockName))&&!!Br(e,r))},wr=e=>{if(!e.length)return[];return ye().filter((t=>!!Er(Sr("from",t.name),(t=>yr(t,"from",e)))))},vr=e=>{if(!e.length)return[];const t=ke(e[0].name);return(t?Sr("to",t.name):[]).filter((t=>t&&yr(t,"to",e))).map((e=>e.blocks)).flat().map(ke)},Tr=e=>e&&"block"===e.type&&Array.isArray(e.blocks)&&e.blocks.includes("*"),Cr=e=>e===he();function xr(e){if(!e.length)return[];const t=wr(e),r=vr(e);return[...new Set([...t,...r])]}function Er(e,t){const r=(0,Rt.createHooks)();for(let n=0;n<e.length;n++){const o=e[n];t(o)&&r.addFilter("transform","transform/"+n.toString(),(e=>e||o),o.priority)}return r.applyFilters("transform",null)}function Sr(e,t){if(void 0===t)return ye().map((({name:t})=>Sr(e,t))).flat();const r=Ie(t),{name:n,transforms:o}=r||{};if(!o||!Array.isArray(o[e]))return[];const a=o.supportedMobileTransforms&&Array.isArray(o.supportedMobileTransforms),i=a?o[e].filter((e=>"raw"===e.type||!(!e.blocks||!e.blocks.length)&&(!!Tr(e)||e.blocks.every((e=>o.supportedMobileTransforms.includes(e)))))):o[e];return i.map((e=>({...e,blockName:n,usingMobileTransformations:a})))}function Br(e,t){if("function"!=typeof e.isMatch)return!0;const r=t[0],n=e.isMultiBlock?t.map((e=>e.attributes)):r.attributes,o=e.isMultiBlock?t:r;return e.isMatch(n,o)}function Ar(e,t){const r=Array.isArray(e)?e:[e],n=r.length>1,o=r[0],a=o.name,i=Sr("from",t),s=Er(Sr("to",a),(e=>"block"===e.type&&(Tr(e)||-1!==e.blocks.indexOf(t))&&(!n||e.isMultiBlock)&&Br(e,r)))||Er(i,(e=>"block"===e.type&&(Tr(e)||-1!==e.blocks.indexOf(a))&&(!n||e.isMultiBlock)&&Br(e,r)));if(!s)return null;let c;if(c=s.isMultiBlock?"__experimentalConvert"in s?s.__experimentalConvert(r):s.transform(r.map((e=>e.attributes)),r.map((e=>e.innerBlocks))):"__experimentalConvert"in s?s.__experimentalConvert(o):s.transform(o.attributes,o.innerBlocks),null===c||"object"!=typeof c)return null;if(c=Array.isArray(c)?c:[c],c.some((e=>!ke(e.name))))return null;if(!c.some((e=>e.name===t)))return null;return c.map(((t,r,n)=>(0,Rt.applyFilters)("blocks.switchToBlockType.transformedBlock",t,e,r,n)))}const Nr=(e,t)=>{try{var r;return mr(e,t.attributes,(null!==(r=t.innerBlocks)&&void 0!==r?r:[]).map((e=>Nr(e.name,e))))}catch{return mr("core/missing",{originalName:e,originalContent:"",originalUndelimitedContent:""})}},Pr=window.wp.blockSerializationDefaultParser,Or=window.wp.autop,Lr=window.React,Mr=window.wp.isShallowEqual;var jr=r.n(Mr);function Dr(e,t={}){const{isCommentDelimited:r=!0}=t,{blockName:n,attrs:o={},innerBlocks:a=[],innerContent:i=[]}=e;let s=0;const c=i.map((e=>null!==e?e:Dr(a[s++],t))).join("\n").replace(/\n+/g,"\n").trim();return r?Gr(n,o,c):c}function zr(e){const t="wp-block-"+e.replace(/\//,"-").replace(/^core-/,"");return(0,Rt.applyFilters)("blocks.getBlockDefaultClassName",t,e)}function Ir(e){const t="editor-block-list-item-"+e.replace(/\//,"-").replace(/^core-/,"");return(0,Rt.applyFilters)("blocks.getBlockMenuDefaultClassName",t,e)}const Rr={},Hr={};function Vr(e={}){const{blockType:t,attributes:r}=Rr;return Vr.skipFilters?e:(0,Rt.applyFilters)("blocks.getSaveContent.extraProps",{...e},t,r)}function $r(e={}){const{innerBlocks:t}=Hr;if(!Array.isArray(t))return{...e,children:t};const r=Yr(t,{isInnerBlocks:!0}),n=(0,Lr.createElement)(G.RawHTML,null,r);return{...e,children:n}}function Ur(e,t,r=[]){const n=Ie(e);if(!n?.save)return null;let{save:o}=n;if(o.prototype instanceof G.Component){const e=new o({attributes:t});o=e.render.bind(e)}Rr.blockType=n,Rr.attributes=t,Hr.innerBlocks=r;let a=o({attributes:t,innerBlocks:r});if(null!==a&&"object"==typeof a&&(0,Rt.hasFilter)("blocks.getSaveContent.extraProps")&&!(n.apiVersion>1)){const e=(0,Rt.applyFilters)("blocks.getSaveContent.extraProps",{...a.props},n,t);jr()(e,a.props)||(a=(0,G.cloneElement)(a,e))}return(0,Rt.applyFilters)("blocks.getSaveElement",a,n,t)}function Fr(e,t,r){const n=Ie(e);return(0,G.renderToString)(Ur(n,t,r))}function qr(e){let t=e.originalContent;if(e.isValid||e.innerBlocks.length)try{t=Fr(e.name,e.attributes,e.innerBlocks)}catch(e){}return t}function Gr(e,t,r){const n=t&&Object.entries(t).length?function(e){return JSON.stringify(e).replace(/--/g,"\\u002d\\u002d").replace(/</g,"\\u003c").replace(/>/g,"\\u003e").replace(/&/g,"\\u0026").replace(/\\"/g,"\\u0022")}(t)+" ":"",o=e?.startsWith("core/")?e.slice(5):e;return r?`\x3c!-- wp:${o} ${n}--\x3e\n`+r+`\n\x3c!-- /wp:${o} --\x3e`:`\x3c!-- wp:${o} ${n}/--\x3e`}function Kr(e,{isInnerBlocks:t=!1}={}){if(!e.isValid&&e.__unstableBlockSource)return Dr(e.__unstableBlockSource);const r=e.name,n=qr(e);if(r===ge()||!t&&r===pe())return n;const o=ke(r);if(!o)return n;const a=function(e,t){var r;return Object.entries(null!==(r=e.attributes)&&void 0!==r?r:{}).reduce(((e,[r,n])=>{const o=t[r];return void 0===o||void 0!==n.source||"default"in n&&JSON.stringify(n.default)===JSON.stringify(o)||(e[r]=o),e}),{})}(o,e.attributes);return Gr(r,a,n)}function Wr(e){1===e.length&&je(e[0])&&(e=[]);let t=Yr(e);return 1===e.length&&e[0].name===pe()&&"core/freeform"===e[0].name&&(t=(0,Or.removep)(t)),t}function Yr(e,t){return(Array.isArray(e)?e:[e]).map((e=>Kr(e,t))).join("\n\n")}var Qr=/^#[xX]([A-Fa-f0-9]+)$/,Zr=/^#([0-9]+)$/,Xr=/^([A-Za-z0-9]+)$/,Jr=(function(){function e(e){this.named=e}e.prototype.parse=function(e){if(e){var t=e.match(Qr);return t?String.fromCharCode(parseInt(t[1],16)):(t=e.match(Zr))?String.fromCharCode(parseInt(t[1],10)):(t=e.match(Xr))?this.named[t[1]]:void 0}}}(),/[\t\n\f ]/),en=/[A-Za-z]/,tn=/\r\n?/g;function rn(e){return Jr.test(e)}function nn(e){return en.test(e)}var on=function(){function e(e,t,r){void 0===r&&(r="precompile"),this.delegate=e,this.entityParser=t,this.mode=r,this.state="beforeData",this.line=-1,this.column=-1,this.input="",this.index=-1,this.tagNameBuffer="",this.states={beforeData:function(){var e=this.peek();if("<"!==e||this.isIgnoredEndTag()){if("precompile"===this.mode&&"\n"===e){var t=this.tagNameBuffer.toLowerCase();"pre"!==t&&"textarea"!==t||this.consume()}this.transitionTo("data"),this.delegate.beginData()}else this.transitionTo("tagOpen"),this.markTagStart(),this.consume()},data:function(){var e=this.peek(),t=this.tagNameBuffer;"<"!==e||this.isIgnoredEndTag()?"&"===e&&"script"!==t&&"style"!==t?(this.consume(),this.delegate.appendToData(this.consumeCharRef()||"&")):(this.consume(),this.delegate.appendToData(e)):(this.delegate.finishData(),this.transitionTo("tagOpen"),this.markTagStart(),this.consume())},tagOpen:function(){var e=this.consume();"!"===e?this.transitionTo("markupDeclarationOpen"):"/"===e?this.transitionTo("endTagOpen"):("@"===e||":"===e||nn(e))&&(this.transitionTo("tagName"),this.tagNameBuffer="",this.delegate.beginStartTag(),this.appendToTagName(e))},markupDeclarationOpen:function(){var e=this.consume();"-"===e&&"-"===this.peek()?(this.consume(),this.transitionTo("commentStart"),this.delegate.beginComment()):"DOCTYPE"===e.toUpperCase()+this.input.substring(this.index,this.index+6).toUpperCase()&&(this.consume(),this.consume(),this.consume(),this.consume(),this.consume(),this.consume(),this.transitionTo("doctype"),this.delegate.beginDoctype&&this.delegate.beginDoctype())},doctype:function(){rn(this.consume())&&this.transitionTo("beforeDoctypeName")},beforeDoctypeName:function(){var e=this.consume();rn(e)||(this.transitionTo("doctypeName"),this.delegate.appendToDoctypeName&&this.delegate.appendToDoctypeName(e.toLowerCase()))},doctypeName:function(){var e=this.consume();rn(e)?this.transitionTo("afterDoctypeName"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypeName&&this.delegate.appendToDoctypeName(e.toLowerCase())},afterDoctypeName:function(){var e=this.consume();if(!rn(e))if(">"===e)this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData");else{var t=e.toUpperCase()+this.input.substring(this.index,this.index+5).toUpperCase(),r="PUBLIC"===t.toUpperCase(),n="SYSTEM"===t.toUpperCase();(r||n)&&(this.consume(),this.consume(),this.consume(),this.consume(),this.consume(),this.consume()),r?this.transitionTo("afterDoctypePublicKeyword"):n&&this.transitionTo("afterDoctypeSystemKeyword")}},afterDoctypePublicKeyword:function(){var e=this.peek();rn(e)?(this.transitionTo("beforeDoctypePublicIdentifier"),this.consume()):'"'===e?(this.transitionTo("doctypePublicIdentifierDoubleQuoted"),this.consume()):"'"===e?(this.transitionTo("doctypePublicIdentifierSingleQuoted"),this.consume()):">"===e&&(this.consume(),this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData"))},doctypePublicIdentifierDoubleQuoted:function(){var e=this.consume();'"'===e?this.transitionTo("afterDoctypePublicIdentifier"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypePublicIdentifier&&this.delegate.appendToDoctypePublicIdentifier(e)},doctypePublicIdentifierSingleQuoted:function(){var e=this.consume();"'"===e?this.transitionTo("afterDoctypePublicIdentifier"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypePublicIdentifier&&this.delegate.appendToDoctypePublicIdentifier(e)},afterDoctypePublicIdentifier:function(){var e=this.consume();rn(e)?this.transitionTo("betweenDoctypePublicAndSystemIdentifiers"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):'"'===e?this.transitionTo("doctypeSystemIdentifierDoubleQuoted"):"'"===e&&this.transitionTo("doctypeSystemIdentifierSingleQuoted")},betweenDoctypePublicAndSystemIdentifiers:function(){var e=this.consume();rn(e)||(">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):'"'===e?this.transitionTo("doctypeSystemIdentifierDoubleQuoted"):"'"===e&&this.transitionTo("doctypeSystemIdentifierSingleQuoted"))},doctypeSystemIdentifierDoubleQuoted:function(){var e=this.consume();'"'===e?this.transitionTo("afterDoctypeSystemIdentifier"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypeSystemIdentifier&&this.delegate.appendToDoctypeSystemIdentifier(e)},doctypeSystemIdentifierSingleQuoted:function(){var e=this.consume();"'"===e?this.transitionTo("afterDoctypeSystemIdentifier"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypeSystemIdentifier&&this.delegate.appendToDoctypeSystemIdentifier(e)},afterDoctypeSystemIdentifier:function(){var e=this.consume();rn(e)||">"===e&&(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData"))},commentStart:function(){var e=this.consume();"-"===e?this.transitionTo("commentStartDash"):">"===e?(this.delegate.finishComment(),this.transitionTo("beforeData")):(this.delegate.appendToCommentData(e),this.transitionTo("comment"))},commentStartDash:function(){var e=this.consume();"-"===e?this.transitionTo("commentEnd"):">"===e?(this.delegate.finishComment(),this.transitionTo("beforeData")):(this.delegate.appendToCommentData("-"),this.transitionTo("comment"))},comment:function(){var e=this.consume();"-"===e?this.transitionTo("commentEndDash"):this.delegate.appendToCommentData(e)},commentEndDash:function(){var e=this.consume();"-"===e?this.transitionTo("commentEnd"):(this.delegate.appendToCommentData("-"+e),this.transitionTo("comment"))},commentEnd:function(){var e=this.consume();">"===e?(this.delegate.finishComment(),this.transitionTo("beforeData")):(this.delegate.appendToCommentData("--"+e),this.transitionTo("comment"))},tagName:function(){var e=this.consume();rn(e)?this.transitionTo("beforeAttributeName"):"/"===e?this.transitionTo("selfClosingStartTag"):">"===e?(this.delegate.finishTag(),this.transitionTo("beforeData")):this.appendToTagName(e)},endTagName:function(){var e=this.consume();rn(e)?(this.transitionTo("beforeAttributeName"),this.tagNameBuffer=""):"/"===e?(this.transitionTo("selfClosingStartTag"),this.tagNameBuffer=""):">"===e?(this.delegate.finishTag(),this.transitionTo("beforeData"),this.tagNameBuffer=""):this.appendToTagName(e)},beforeAttributeName:function(){var e=this.peek();rn(e)?this.consume():"/"===e?(this.transitionTo("selfClosingStartTag"),this.consume()):">"===e?(this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):"="===e?(this.delegate.reportSyntaxError("attribute name cannot start with equals sign"),this.transitionTo("attributeName"),this.delegate.beginAttribute(),this.consume(),this.delegate.appendToAttributeName(e)):(this.transitionTo("attributeName"),this.delegate.beginAttribute())},attributeName:function(){var e=this.peek();rn(e)?(this.transitionTo("afterAttributeName"),this.consume()):"/"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.transitionTo("selfClosingStartTag")):"="===e?(this.transitionTo("beforeAttributeValue"),this.consume()):">"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):'"'===e||"'"===e||"<"===e?(this.delegate.reportSyntaxError(e+" is not a valid character within attribute names"),this.consume(),this.delegate.appendToAttributeName(e)):(this.consume(),this.delegate.appendToAttributeName(e))},afterAttributeName:function(){var e=this.peek();rn(e)?this.consume():"/"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.transitionTo("selfClosingStartTag")):"="===e?(this.consume(),this.transitionTo("beforeAttributeValue")):">"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.transitionTo("attributeName"),this.delegate.beginAttribute(),this.consume(),this.delegate.appendToAttributeName(e))},beforeAttributeValue:function(){var e=this.peek();rn(e)?this.consume():'"'===e?(this.transitionTo("attributeValueDoubleQuoted"),this.delegate.beginAttributeValue(!0),this.consume()):"'"===e?(this.transitionTo("attributeValueSingleQuoted"),this.delegate.beginAttributeValue(!0),this.consume()):">"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):(this.transitionTo("attributeValueUnquoted"),this.delegate.beginAttributeValue(!1),this.consume(),this.delegate.appendToAttributeValue(e))},attributeValueDoubleQuoted:function(){var e=this.consume();'"'===e?(this.delegate.finishAttributeValue(),this.transitionTo("afterAttributeValueQuoted")):"&"===e?this.delegate.appendToAttributeValue(this.consumeCharRef()||"&"):this.delegate.appendToAttributeValue(e)},attributeValueSingleQuoted:function(){var e=this.consume();"'"===e?(this.delegate.finishAttributeValue(),this.transitionTo("afterAttributeValueQuoted")):"&"===e?this.delegate.appendToAttributeValue(this.consumeCharRef()||"&"):this.delegate.appendToAttributeValue(e)},attributeValueUnquoted:function(){var e=this.peek();rn(e)?(this.delegate.finishAttributeValue(),this.consume(),this.transitionTo("beforeAttributeName")):"/"===e?(this.delegate.finishAttributeValue(),this.consume(),this.transitionTo("selfClosingStartTag")):"&"===e?(this.consume(),this.delegate.appendToAttributeValue(this.consumeCharRef()||"&")):">"===e?(this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):(this.consume(),this.delegate.appendToAttributeValue(e))},afterAttributeValueQuoted:function(){var e=this.peek();rn(e)?(this.consume(),this.transitionTo("beforeAttributeName")):"/"===e?(this.consume(),this.transitionTo("selfClosingStartTag")):">"===e?(this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):this.transitionTo("beforeAttributeName")},selfClosingStartTag:function(){">"===this.peek()?(this.consume(),this.delegate.markTagAsSelfClosing(),this.delegate.finishTag(),this.transitionTo("beforeData")):this.transitionTo("beforeAttributeName")},endTagOpen:function(){var e=this.consume();("@"===e||":"===e||nn(e))&&(this.transitionTo("endTagName"),this.tagNameBuffer="",this.delegate.beginEndTag(),this.appendToTagName(e))}},this.reset()}return e.prototype.reset=function(){this.transitionTo("beforeData"),this.input="",this.tagNameBuffer="",this.index=0,this.line=1,this.column=0,this.delegate.reset()},e.prototype.transitionTo=function(e){this.state=e},e.prototype.tokenize=function(e){this.reset(),this.tokenizePart(e),this.tokenizeEOF()},e.prototype.tokenizePart=function(e){for(this.input+=function(e){return e.replace(tn,"\n")}(e);this.index<this.input.length;){var t=this.states[this.state];if(void 0===t)throw new Error("unhandled state "+this.state);t.call(this)}},e.prototype.tokenizeEOF=function(){this.flushData()},e.prototype.flushData=function(){"data"===this.state&&(this.delegate.finishData(),this.transitionTo("beforeData"))},e.prototype.peek=function(){return this.input.charAt(this.index)},e.prototype.consume=function(){var e=this.peek();return this.index++,"\n"===e?(this.line++,this.column=0):this.column++,e},e.prototype.consumeCharRef=function(){var e=this.input.indexOf(";",this.index);if(-1!==e){var t=this.input.slice(this.index,e),r=this.entityParser.parse(t);if(r){for(var n=t.length;n;)this.consume(),n--;return this.consume(),r}}},e.prototype.markTagStart=function(){this.delegate.tagOpen()},e.prototype.appendToTagName=function(e){this.tagNameBuffer+=e,this.delegate.appendToTagName(e)},e.prototype.isIgnoredEndTag=function(){var e=this.tagNameBuffer;return"title"===e&&"</title>"!==this.input.substring(this.index,this.index+8)||"style"===e&&"</style>"!==this.input.substring(this.index,this.index+8)||"script"===e&&"<\/script>"!==this.input.substring(this.index,this.index+9)},e}(),an=function(){function e(e,t){void 0===t&&(t={}),this.options=t,this.token=null,this.startLine=1,this.startColumn=0,this.tokens=[],this.tokenizer=new on(this,e,t.mode),this._currentAttribute=void 0}return e.prototype.tokenize=function(e){return this.tokens=[],this.tokenizer.tokenize(e),this.tokens},e.prototype.tokenizePart=function(e){return this.tokens=[],this.tokenizer.tokenizePart(e),this.tokens},e.prototype.tokenizeEOF=function(){return this.tokens=[],this.tokenizer.tokenizeEOF(),this.tokens[0]},e.prototype.reset=function(){this.token=null,this.startLine=1,this.startColumn=0},e.prototype.current=function(){var e=this.token;if(null===e)throw new Error("token was unexpectedly null");if(0===arguments.length)return e;for(var t=0;t<arguments.length;t++)if(e.type===arguments[t])return e;throw new Error("token type was unexpectedly "+e.type)},e.prototype.push=function(e){this.token=e,this.tokens.push(e)},e.prototype.currentAttribute=function(){return this._currentAttribute},e.prototype.addLocInfo=function(){this.options.loc&&(this.current().loc={start:{line:this.startLine,column:this.startColumn},end:{line:this.tokenizer.line,column:this.tokenizer.column}}),this.startLine=this.tokenizer.line,this.startColumn=this.tokenizer.column},e.prototype.beginDoctype=function(){this.push({type:"Doctype",name:""})},e.prototype.appendToDoctypeName=function(e){this.current("Doctype").name+=e},e.prototype.appendToDoctypePublicIdentifier=function(e){var t=this.current("Doctype");void 0===t.publicIdentifier?t.publicIdentifier=e:t.publicIdentifier+=e},e.prototype.appendToDoctypeSystemIdentifier=function(e){var t=this.current("Doctype");void 0===t.systemIdentifier?t.systemIdentifier=e:t.systemIdentifier+=e},e.prototype.endDoctype=function(){this.addLocInfo()},e.prototype.beginData=function(){this.push({type:"Chars",chars:""})},e.prototype.appendToData=function(e){this.current("Chars").chars+=e},e.prototype.finishData=function(){this.addLocInfo()},e.prototype.beginComment=function(){this.push({type:"Comment",chars:""})},e.prototype.appendToCommentData=function(e){this.current("Comment").chars+=e},e.prototype.finishComment=function(){this.addLocInfo()},e.prototype.tagOpen=function(){},e.prototype.beginStartTag=function(){this.push({type:"StartTag",tagName:"",attributes:[],selfClosing:!1})},e.prototype.beginEndTag=function(){this.push({type:"EndTag",tagName:""})},e.prototype.finishTag=function(){this.addLocInfo()},e.prototype.markTagAsSelfClosing=function(){this.current("StartTag").selfClosing=!0},e.prototype.appendToTagName=function(e){this.current("StartTag","EndTag").tagName+=e},e.prototype.beginAttribute=function(){this._currentAttribute=["","",!1]},e.prototype.appendToAttributeName=function(e){this.currentAttribute()[0]+=e},e.prototype.beginAttributeValue=function(e){this.currentAttribute()[2]=e},e.prototype.appendToAttributeValue=function(e){this.currentAttribute()[1]+=e},e.prototype.finishAttributeValue=function(){this.current("StartTag").attributes.push(this._currentAttribute)},e.prototype.reportSyntaxError=function(e){this.current().syntaxError=e},e}();var sn=r(7734),cn=r.n(sn);const ln=window.wp.htmlEntities;function un(){function e(e){return(t,...r)=>e("Block validation: "+t,...r)}return{error:e(console.error),warning:e(console.warn),getItems:()=>[]}}const dn=/[\t\n\r\v\f ]+/g,pn=/^[\t\n\r\v\f ]*$/,hn=/^url\s*\(['"\s]*(.*?)['"\s]*\)$/,fn=["allowfullscreen","allowpaymentrequest","allowusermedia","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","typemustmatch"],gn=[...fn,"autocapitalize","autocomplete","charset","contenteditable","crossorigin","decoding","dir","draggable","enctype","formenctype","formmethod","http-equiv","inputmode","kind","method","preload","scope","shape","spellcheck","translate","type","wrap"],mn=[e=>e,function(e){return wn(e).join(" ")}],bn=/^[\da-z]+$/i,_n=/^#\d+$/,kn=/^#x[\da-f]+$/i;class yn{parse(e){if(t=e,bn.test(t)||_n.test(t)||kn.test(t))return(0,ln.decodeEntities)("&"+e+";");var t}}function wn(e){return e.trim().split(dn)}function vn(e){return e.attributes.filter((e=>{const[t,r]=e;return r||0===t.indexOf("data-")||gn.includes(t)}))}function Tn(e,t,r=un()){let n=e.chars,o=t.chars;for(let e=0;e<mn.length;e++){const t=mn[e];if(n=t(n),o=t(o),n===o)return!0}return r.warning("Expected text `%s`, saw `%s`.",t.chars,e.chars),!1}function Cn(e){return 0===parseFloat(e)?"0":0===e.indexOf(".")?"0"+e:e}function xn(e){return wn(e).map(Cn).join(" ").replace(hn,"url($1)")}function En(e){const t=e.replace(/;?\s*$/,"").split(";").map((e=>{const[t,...r]=e.split(":"),n=r.join(":");return[t.trim(),xn(n.trim())]}));return Object.fromEntries(t)}const Sn={class:(e,t)=>{const[r,n]=[e,t].map(wn),o=r.filter((e=>!n.includes(e))),a=n.filter((e=>!r.includes(e)));return 0===o.length&&0===a.length},style:(e,t)=>cn()(...[e,t].map(En)),...Object.fromEntries(fn.map((e=>[e,()=>!0])))};const Bn={StartTag:(e,t,r=un())=>e.tagName!==t.tagName&&e.tagName.toLowerCase()!==t.tagName.toLowerCase()?(r.warning("Expected tag name `%s`, instead saw `%s`.",t.tagName,e.tagName),!1):function(e,t,r=un()){if(e.length!==t.length)return r.warning("Expected attributes %o, instead saw %o.",t,e),!1;const n={};for(let e=0;e<t.length;e++)n[t[e][0].toLowerCase()]=t[e][1];for(let t=0;t<e.length;t++){const[o,a]=e[t],i=o.toLowerCase();if(!n.hasOwnProperty(i))return r.warning("Encountered unexpected attribute `%s`.",o),!1;const s=n[i],c=Sn[i];if(c){if(!c(a,s))return r.warning("Expected attribute `%s` of value `%s`, saw `%s`.",o,s,a),!1}else if(a!==s)return r.warning("Expected attribute `%s` of value `%s`, saw `%s`.",o,s,a),!1}return!0}(...[e,t].map(vn),r),Chars:Tn,Comment:Tn};function An(e){let t;for(;t=e.shift();){if("Chars"!==t.type)return t;if(!pn.test(t.chars))return t}}function Nn(e,t){return!!e.selfClosing&&!(!t||t.tagName!==e.tagName||"EndTag"!==t.type)}function Pn(e,t,r=un()){if(e===t)return!0;const[n,o]=[e,t].map((e=>function(e,t=un()){try{return new an(new yn).tokenize(e)}catch(r){t.warning("Malformed HTML detected: %s",e)}return null}(e,r)));if(!n||!o)return!1;let a,i;for(;a=An(n);){if(i=An(o),!i)return r.warning("Expected end of content, instead saw %o.",a),!1;if(a.type!==i.type)return r.warning("Expected token of type `%s` (%o), instead saw `%s` (%o).",i.type,i,a.type,a),!1;const e=Bn[a.type];if(e&&!e(a,i,r))return!1;Nn(a,o[0])?An(o):Nn(i,n[0])&&An(n)}return!(i=An(o))||(r.warning("Expected %o, instead saw end of content.",i),!1)}function On(e,t=e.name){if(e.name===pe()||e.name===ge())return[!0,[]];const r=function(){const e=[],t=un();return{error(...r){e.push({log:t.error,args:r})},warning(...r){e.push({log:t.warning,args:r})},getItems:()=>e}}(),n=Ie(t);let o;try{o=Fr(n,e.attributes)}catch(e){return r.error("Block validation failed because an error occurred while generating block content:\n\n%s",e.toString()),[!1,r.getItems()]}const a=Pn(e.originalContent,o,r);return a||r.error("Block validation failed for `%s` (%o).\n\nContent generated by `save` function:\n\n%s\n\nContent retrieved from post body:\n\n%s",n.name,n,o,e.originalContent),[a,r.getItems()]}function Ln(e,t,r){Dt()("isValidBlockContent introduces opportunity for data loss",{since:"12.6",plugin:"Gutenberg",alternative:"validateBlock"});const n=Ie(e),o={name:n.name,attributes:t,innerBlocks:[],originalContent:r},[a]=On(o,n);return a}function Mn(e,t){const r={...t};if("core/cover-image"===e&&(e="core/cover"),"core/text"!==e&&"core/cover-text"!==e||(e="core/paragraph"),e&&0===e.indexOf("core/social-link-")&&(r.service=e.substring(17),e="core/social-link"),e&&0===e.indexOf("core-embed/")){const t=e.substring(11),n={speaker:"speaker-deck",polldaddy:"crowdsignal"};r.providerNameSlug=t in n?n[t]:t,["amazon-kindle","wordpress"].includes(t)||(r.responsive=!0),e="core/embed"}if("core/post-comment-author"===e&&(e="core/comment-author-name"),"core/post-comment-content"===e&&(e="core/comment-content"),"core/post-comment-date"===e&&(e="core/comment-date"),"core/comments-query-loop"===e){e="core/comments";const{className:t=""}=r;t.includes("wp-block-comments-query-loop")||(r.className=["wp-block-comments-query-loop",t].join(" "))}return"core/post-comments"===e&&(e="core/comments",r.legacy=!0),[e,r]}var jn,Dn=function(){return jn||(jn=document.implementation.createHTMLDocument("")),jn};function zn(e,t){if(t){if("string"==typeof e){var r=Dn();r.body.innerHTML=e,e=r.body}if("function"==typeof t)return t(e);if(Object===t.constructor)return Object.keys(t).reduce((function(r,n){return r[n]=zn(e,t[n]),r}),{})}}function In(e,t){return 1===arguments.length&&(t=e,e=void 0),function(r){var n=r;if(e&&(n=r.querySelector(e)),n)return function(e,t){for(var r,n=t.split(".");r=n.shift();){if(!(r in e))return;e=e[r]}return e}(n,t)}}function Rn(e){const t={};for(let r=0;r<e.length;r++){const{name:n,value:o}=e[r];t[n]=o}return t}function Hn(e){if(Dt()("wp.blocks.node.fromDOM",{since:"6.1",version:"6.3",alternative:"wp.richText.create",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"}),e.nodeType===e.TEXT_NODE)return e.nodeValue;if(e.nodeType!==e.ELEMENT_NODE)throw new TypeError("A block node can only be created from a node of type text or element.");return{type:e.nodeName.toLowerCase(),props:{...Rn(e.attributes),children:Un(e.childNodes)}}}function Vn(e){return Dt()("wp.blocks.node.matcher",{since:"6.1",version:"6.3",alternative:"html source",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"}),t=>{let r=t;e&&(r=t.querySelector(e));try{return Hn(r)}catch(e){return null}}}const $n={isNodeOfType:function(e,t){return Dt()("wp.blocks.node.isNodeOfType",{since:"6.1",version:"6.3",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"}),e&&e.type===t},fromDOM:Hn,toHTML:function(e){return Dt()("wp.blocks.node.toHTML",{since:"6.1",version:"6.3",alternative:"wp.richText.toHTMLString",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"}),Fn([e])},matcher:Vn};function Un(e){Dt()("wp.blocks.children.fromDOM",{since:"6.1",version:"6.3",alternative:"wp.richText.create",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"});const t=[];for(let r=0;r<e.length;r++)try{t.push(Hn(e[r]))}catch(e){}return t}function Fn(e){Dt()("wp.blocks.children.toHTML",{since:"6.1",version:"6.3",alternative:"wp.richText.toHTMLString",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"});const t=e;return(0,G.renderToString)(t)}function qn(e){return Dt()("wp.blocks.children.matcher",{since:"6.1",version:"6.3",alternative:"html source",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"}),t=>{let r=t;return e&&(r=t.querySelector(e)),r?Un(r.childNodes):[]}}const Gn={concat:function(...e){Dt()("wp.blocks.children.concat",{since:"6.1",version:"6.3",alternative:"wp.richText.concat",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"});const t=[];for(let r=0;r<e.length;r++){const n=Array.isArray(e[r])?e[r]:[e[r]];for(let e=0;e<n.length;e++){const r=n[e];"string"==typeof r&&"string"==typeof t[t.length-1]?t[t.length-1]+=r:t.push(r)}}return t},getChildrenArray:function(e){return Dt()("wp.blocks.children.getChildrenArray",{since:"6.1",version:"6.3",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"}),e},fromDOM:Un,toHTML:Fn,matcher:qn};function Kn(e,t){return t.some((t=>function(e,t){switch(t){case"rich-text":return e instanceof W.RichTextData;case"string":return"string"==typeof e;case"boolean":return"boolean"==typeof e;case"object":return!!e&&e.constructor===Object;case"null":return null===e;case"array":return Array.isArray(e);case"integer":case"number":return"number"==typeof e}return!0}(e,t)))}function Wn(e,t,r,n,o){let a;switch(t.source){case void 0:a=n?n[e]:void 0;break;case"raw":a=o;break;case"attribute":case"property":case"html":case"text":case"rich-text":case"children":case"node":case"query":case"tag":a=Zn(r,t)}return function(e,t){return void 0===t||Kn(e,Array.isArray(t)?t:[t])}(a,t.type)&&function(e,t){return!Array.isArray(t)||t.includes(e)}(a,t.enum)||(a=void 0),void 0===a&&(a=Ve(t)),a}const Yn=function(e,t){var r,n,o=0;function a(){var a,i,s=r,c=arguments.length;e:for(;s;){if(s.args.length===arguments.length){for(i=0;i<c;i++)if(s.args[i]!==arguments[i]){s=s.next;continue e}return s!==r&&(s===n&&(n=s.prev),s.prev.next=s.next,s.next&&(s.next.prev=s.prev),s.next=r,s.prev=null,r.prev=s,r=s),s.val}s=s.next}for(a=new Array(c),i=0;i<c;i++)a[i]=arguments[i];return s={args:a,val:e.apply(null,a)},r?(r.prev=s,s.next=r):n=s,o===t.maxSize?(n=n.prev).next=null:o++,r=s,s.val}return t=t||{},a.clear=function(){r=null,n=null,o=0},a}((e=>{switch(e.source){case"attribute":let n=function(e,t){return 1===arguments.length&&(t=e,e=void 0),function(r){var n=In(e,"attributes")(r);if(n&&n.hasOwnProperty(t))return n[t].value}}(e.selector,e.attribute);return"boolean"===e.type&&(n=(e=>(0,it.pipe)([e,e=>void 0!==e]))(n)),n;case"html":return t=e.selector,r=e.multiline,e=>{let n=e;if(t&&(n=e.querySelector(t)),!n)return"";if(r){let e="";const t=n.children.length;for(let o=0;o<t;o++){const t=n.children[o];t.nodeName.toLowerCase()===r&&(e+=t.outerHTML)}return e}return n.innerHTML};case"text":return function(e){return In(e,"textContent")}(e.selector);case"rich-text":return((e,t)=>r=>{const n=e?r.querySelector(e):r;return n?W.RichTextData.fromHTMLElement(n,{preserveWhiteSpace:t}):W.RichTextData.empty()})(e.selector,e.__unstablePreserveWhiteSpace);case"children":return qn(e.selector);case"node":return Vn(e.selector);case"query":const o=Object.fromEntries(Object.entries(e.query).map((([e,t])=>[e,Yn(t)])));return function(e,t){return function(r){var n=r.querySelectorAll(e);return[].map.call(n,(function(e){return zn(e,t)}))}}(e.selector,o);case"tag":return(0,it.pipe)([In(e.selector,"nodeName"),e=>e?e.toLowerCase():void 0]);default:console.error(`Unknown source type "${e.source}"`)}var t,r}));function Qn(e){return zn(e,(e=>e))}function Zn(e,t){return Yn(t)(Qn(e))}function Xn(e,t,r={}){var n;const o=Qn(t),a=Ie(e),i=Object.fromEntries(Object.entries(null!==(n=a.attributes)&&void 0!==n?n:{}).map((([e,n])=>[e,Wn(e,n,o,r,t)])));return(0,Rt.applyFilters)("blocks.getBlockAttributes",i,a,t,r)}const Jn={type:"string",source:"attribute",selector:"[data-custom-class-name] > *",attribute:"class"};function eo(e){const t=Zn(`<div data-custom-class-name>${e}</div>`,Jn);return t?t.trim().split(/\s+/):[]}function to(e,t){const r=function(e,t,r){if(ve(t,"customClassName",!0)){const{className:n,...o}=e,a=Fr(t,o),i=eo(a),s=eo(r).filter((e=>!i.includes(e)));s.length?e.className=s.join(" "):a&&delete e.className}return e}(e.attributes,t,e.originalContent);return{...e,attributes:r}}function ro(){return!1}function no(e,t){let r=function(e,t){const r=pe(),n=e.blockName||pe(),o=e.attrs||{},a=e.innerBlocks||[];let i=e.innerHTML.trim();return n!==r||"core/freeform"!==n||t?.__unstableSkipAutop||(i=(0,Or.autop)(i).trim()),{...e,blockName:n,attrs:o,innerHTML:i,innerBlocks:a}}(e,t);r=function(e){const[t,r]=Mn(e.blockName,e.attrs);return{...e,blockName:t,attrs:r}}(r);let n=ke(r.blockName);n||(r=function(e){const t=ge()||pe(),r=Dr(e,{isCommentDelimited:!1}),n=Dr(e,{isCommentDelimited:!0});return{blockName:t,attrs:{originalName:e.blockName,originalContent:n,originalUndelimitedContent:r},innerHTML:e.blockName?n:e.innerHTML,innerBlocks:e.innerBlocks,innerContent:e.innerContent}}(r),n=ke(r.blockName));const o=r.blockName===pe()||r.blockName===ge();if(!n||!r.innerHTML&&o)return;const a=r.innerBlocks.map((e=>no(e,t))).filter((e=>!!e)),i=mr(r.blockName,Xn(n,r.innerHTML,r.attrs),a);i.originalContent=r.innerHTML;const s=function(e,t){const[r]=On(e,t);if(r)return{...e,isValid:r,validationIssues:[]};const n=to(e,t),[o,a]=On(e,t);return{...n,isValid:o,validationIssues:a}}(i,n),{validationIssues:c}=s,l=function(e,t,r){const n=t.attrs,{deprecated:o}=r;if(!o||!o.length)return e;for(let a=0;a<o.length;a++){const{isEligible:i=ro}=o[a];if(e.isValid&&!i(n,e.innerBlocks,{blockNode:t,block:e}))continue;const s=Object.assign(Fe(r,Q),o[a]);let c={...e,attributes:Xn(s,e.originalContent,n)},[l]=On(c,s);if(l||(c=to(c,s),[l]=On(c,s)),!l)continue;let u=c.innerBlocks,d=c.attributes;const{migrate:p}=s;if(p){let t=p(d,e.innerBlocks);Array.isArray(t)||(t=[t]),[d=n,u=e.innerBlocks]=t}e={...e,attributes:d,innerBlocks:u,isValid:!0,validationIssues:[]}}return e}(s,r,n);return l.isValid||(l.__unstableBlockSource=e),s.isValid||!l.isValid||t?.__unstableSkipMigrationLogs?s.isValid||l.isValid||c.forEach((({log:e,args:t})=>e(...t))):(console.groupCollapsed("Updated Block: %s",n.name),console.info("Block successfully updated for `%s` (%o).\n\nNew content generated by `save` function:\n\n%s\n\nContent retrieved from post body:\n\n%s",n.name,n,Fr(n,l.attributes),l.originalContent),console.groupEnd()),l}function oo(e,t){return(0,Pr.parse)(e).reduce(((e,r)=>{const n=no(r,t);return n&&e.push(n),e}),[])}function ao(){return Sr("from").filter((({type:e})=>"raw"===e)).map((e=>e.isMatch?e:{...e,isMatch:t=>e.selector&&t.matches(e.selector)}))}function io(e,t){const r=document.implementation.createHTMLDocument("");return r.body.innerHTML=e,Array.from(r.body.children).flatMap((e=>{const r=Er(ao(),(({isMatch:t})=>t(e)));if(!r)return G.Platform.isNative?oo(`\x3c!-- wp:html --\x3e${e.outerHTML}\x3c!-- /wp:html --\x3e`):mr("core/html",Xn("core/html",e.outerHTML));const{transform:n,blockName:o}=r;return n?n(e,t):mr(o,Xn(o,e.outerHTML))}))}function so(e){const t=document.implementation.createHTMLDocument(""),r=document.implementation.createHTMLDocument(""),n=t.body,o=r.body;for(n.innerHTML=e;n.firstChild;){const e=n.firstChild;e.nodeType===e.TEXT_NODE?(0,K.isEmpty)(e)?n.removeChild(e):(o.lastChild&&"P"===o.lastChild.nodeName||o.appendChild(r.createElement("P")),o.lastChild.appendChild(e)):e.nodeType===e.ELEMENT_NODE?"BR"===e.nodeName?(e.nextSibling&&"BR"===e.nextSibling.nodeName&&(o.appendChild(r.createElement("P")),n.removeChild(e.nextSibling)),o.lastChild&&"P"===o.lastChild.nodeName&&o.lastChild.hasChildNodes()?o.lastChild.appendChild(e):n.removeChild(e)):"P"===e.nodeName?(0,K.isEmpty)(e)?n.removeChild(e):o.appendChild(e):(0,K.isPhrasingContent)(e)?(o.lastChild&&"P"===o.lastChild.nodeName||o.appendChild(r.createElement("P")),o.lastChild.appendChild(e)):o.appendChild(e):n.removeChild(e)}return o.innerHTML}function co(e,t){e.nodeType===e.COMMENT_NODE&&("nextpage"!==e.nodeValue?0===e.nodeValue.indexOf("more")&&function(e,t){const r=e.nodeValue.slice(4).trim();let n=e,o=!1;for(;n=n.nextSibling;)if(n.nodeType===n.COMMENT_NODE&&"noteaser"===n.nodeValue){o=!0,(0,K.remove)(n);break}const a=function(e,t,r){const n=r.createElement("wp-block");n.dataset.block="core/more",e&&(n.dataset.customText=e);t&&(n.dataset.noTeaser="");return n}(r,o,t);if(e.parentNode&&"P"===e.parentNode.nodeName&&1!==e.parentNode.childNodes.length){const r=Array.from(e.parentNode.childNodes),n=r.indexOf(e),o=e.parentNode.parentNode||t.body,i=(e,r)=>(e||(e=t.createElement("p")),e.appendChild(r),e);[r.slice(0,n).reduce(i,null),a,r.slice(n+1).reduce(i,null)].forEach((t=>t&&o.insertBefore(t,e.parentNode))),(0,K.remove)(e.parentNode)}else(0,K.replace)(e,a)}(e,t):(0,K.replace)(e,function(e){const t=e.createElement("wp-block");return t.dataset.block="core/nextpage",t}(t)))}function lo(e){return"OL"===e.nodeName||"UL"===e.nodeName}function uo(e){if(!lo(e))return;const t=e,r=e.previousElementSibling;if(r&&r.nodeName===e.nodeName&&1===t.children.length){for(;t.firstChild;)r.appendChild(t.firstChild);t.parentNode.removeChild(t)}const n=e.parentNode;if(n&&"LI"===n.nodeName&&1===n.children.length&&!/\S/.test((o=n,Array.from(o.childNodes).map((({nodeValue:e=""})=>e)).join("")))){const e=n,r=e.previousElementSibling,o=e.parentNode;r?(r.appendChild(t),o.removeChild(e)):(o.parentNode.insertBefore(t,o),o.parentNode.removeChild(o))}var o;if(n&&lo(n)){const t=e.previousElementSibling;t?t.appendChild(e):(0,K.unwrap)(e)}}function po(e){"BLOCKQUOTE"===e.nodeName&&(e.innerHTML=so(e.innerHTML))}function ho(e,t=e){const r=e.ownerDocument.createElement("figure");t.parentNode.insertBefore(r,t),r.appendChild(e)}function fo(e,t,r){if(!function(e,t){var r;const n=e.nodeName.toLowerCase();return"figcaption"!==n&&!(0,K.isTextContent)(e)&&n in(null!==(r=t?.figure?.children)&&void 0!==r?r:{})}(e,r))return;let n=e;const o=e.parentNode;(function(e,t){var r;return e.nodeName.toLowerCase()in(null!==(r=t?.figure?.children?.a?.children)&&void 0!==r?r:{})})(e,r)&&"A"===o.nodeName&&1===o.childNodes.length&&(n=e.parentNode);const a=n.closest("p,div");a?e.classList?(e.classList.contains("alignright")||e.classList.contains("alignleft")||e.classList.contains("aligncenter")||!a.textContent.trim())&&ho(n,a):ho(n,a):"BODY"===n.parentNode.nodeName&&ho(n)}const go=window.wp.shortcode,mo=e=>Array.isArray(e)?e:[e];const bo=function e(t,r=0,n=[]){const o=Er(Sr("from"),(e=>-1===n.indexOf(e.blockName)&&"shortcode"===e.type&&mo(e.tag).some((e=>(0,go.regexp)(e).test(t)))));if(!o)return[t];const a=mo(o.tag).find((e=>(0,go.regexp)(e).test(t)));let i;const s=r;if(i=(0,go.next)(a,t,r)){r=i.index+i.content.length;const a=t.substr(0,i.index),c=t.substr(r);if(!(i.shortcode.content?.includes("<")||/(\n|<p>)\s*$/.test(a)&&/^\s*(\n|<\/p>)/.test(c)))return e(t,r);if(o.isMatch&&!o.isMatch(i.shortcode.attrs))return e(t,s,[...n,o.blockName]);let l=[];if("function"==typeof o.transform)l=[].concat(o.transform(i.shortcode.attrs,i)),l=l.map((e=>(e.originalContent=i.shortcode.content,to(e,ke(e.name)))));else{const e=Object.fromEntries(Object.entries(o.attributes).filter((([,e])=>e.shortcode)).map((([e,t])=>[e,t.shortcode(i.shortcode.attrs,i)]))),r=ke(o.blockName);if(!r)return[t];const n={...r,attributes:o.attributes};let a=mr(o.blockName,Xn(n,i.shortcode.content,e));a.originalContent=i.shortcode.content,a=to(a,n),l=[a]}return[...e(a),...l,...e(c)]}return[t]};function _o(e){return function(e,t){const r={phrasingContentSchema:(0,K.getPhrasingContentSchema)(t),isPaste:"paste"===t};function n(e,t,r){switch(r){case"children":return"*"===e||"*"===t?"*":{...e,...t};case"attributes":case"require":return[...e||[],...t||[]];case"isMatch":if(!e||!t)return;return(...r)=>e(...r)||t(...r)}}function o(e,t){for(const r in t)e[r]=e[r]?n(e[r],t[r],r):{...t[r]};return e}return e.map((({isMatch:e,blockName:t,schema:n})=>{const o=ve(t,"anchor");return n="function"==typeof n?n(r):n,o||e?n?Object.fromEntries(Object.entries(n).map((([t,r])=>{let n=r.attributes||[];return o&&(n=[...n,"id"]),[t,{...r,attributes:n,isMatch:e||void 0}]}))):{}:n})).reduce((function(e,t){for(const r in t)e[r]=e[r]?o(e[r],t[r]):{...t[r]};return e}),{})}(ao(),e)}function ko(e,t,r,n){Array.from(e).forEach((e=>{ko(e.childNodes,t,r,n),t.forEach((t=>{r.contains(e)&&t(e,r,n)}))}))}function yo(e,t=[],r){const n=document.implementation.createHTMLDocument("");return n.body.innerHTML=e,ko(n.body.childNodes,t,n,r),n.body.innerHTML}function wo(e,t){const r=e[`${t}Sibling`];if(r&&(0,K.isPhrasingContent)(r))return r;const{parentNode:n}=e;return n&&(0,K.isPhrasingContent)(n)?wo(n,t):void 0}function vo(e){return Dt()("wp.blocks.getPhrasingContentSchema",{since:"5.6",alternative:"wp.dom.getPhrasingContentSchema"}),(0,K.getPhrasingContentSchema)(e)}function To({HTML:e=""}){if(-1!==e.indexOf("\x3c!-- wp:"))return oo(e);const t=bo(e),r=_o();return t.map((e=>{if("string"!=typeof e)return e;return io(e=so(e=yo(e,[uo,co,fo,po],r)),To)})).flat().filter(Boolean)}function Co(e){e.nodeType===e.COMMENT_NODE&&(0,K.remove)(e)}function xo(e,t){return e.every((e=>function(e,t){if((0,K.isTextContent)(e))return!0;if(!t)return!1;const r=e.nodeName.toLowerCase();return[["ul","li","ol"],["h1","h2","h3","h4","h5","h6"]].some((e=>0===[r,t].filter((t=>!e.includes(t))).length))}(e,t)&&xo(Array.from(e.children),t)))}function Eo(e){return"BR"===e.nodeName&&e.previousSibling&&"BR"===e.previousSibling.nodeName}function So(e,t){if("SPAN"===e.nodeName&&e.style){const{fontWeight:r,fontStyle:n,textDecorationLine:o,textDecoration:a,verticalAlign:i}=e.style;"bold"!==r&&"700"!==r||(0,K.wrap)(t.createElement("strong"),e),"italic"===n&&(0,K.wrap)(t.createElement("em"),e),("line-through"===o||a.includes("line-through"))&&(0,K.wrap)(t.createElement("s"),e),"super"===i?(0,K.wrap)(t.createElement("sup"),e):"sub"===i&&(0,K.wrap)(t.createElement("sub"),e)}else"B"===e.nodeName?e=(0,K.replaceTag)(e,"strong"):"I"===e.nodeName?e=(0,K.replaceTag)(e,"em"):"A"===e.nodeName&&(e.target&&"_blank"===e.target.toLowerCase()?e.rel="noreferrer noopener":(e.removeAttribute("target"),e.removeAttribute("rel")),e.name&&!e.id&&(e.id=e.name),e.id&&!e.ownerDocument.querySelector(`[href="#${e.id}"]`)&&e.removeAttribute("id"))}function Bo(e){"SCRIPT"!==e.nodeName&&"NOSCRIPT"!==e.nodeName&&"TEMPLATE"!==e.nodeName&&"STYLE"!==e.nodeName||e.parentNode.removeChild(e)}function Ao(e){if(e.nodeType!==e.ELEMENT_NODE)return;const t=e.getAttribute("style");if(!t||!t.includes("mso-list"))return;"ignore"===t.split(";").reduce(((e,t)=>{const[r,n]=t.split(":");return e[r.trim().toLowerCase()]=n.trim().toLowerCase(),e}),{})["mso-list"]&&e.remove()}function No(e){return"OL"===e.nodeName||"UL"===e.nodeName}function Po(e,t){if("P"!==e.nodeName)return;const r=e.getAttribute("style");if(!r||!r.includes("mso-list"))return;const n=e.previousElementSibling;if(!n||!No(n)){const r=e.textContent.trim().slice(0,1),n=/[1iIaA]/.test(r),o=t.createElement(n?"ol":"ul");n&&o.setAttribute("type",r),e.parentNode.insertBefore(o,e)}const o=e.previousElementSibling,a=o.nodeName,i=t.createElement("li");let s=o;i.innerHTML=yo(e.innerHTML,[Ao]);const c=/mso-list\s*:[^;]+level([0-9]+)/i.exec(r);let l=c&&parseInt(c[1],10)-1||0;for(;l--;)s=s.lastChild||s,No(s)&&(s=s.lastChild||s);No(s)||(s=s.appendChild(t.createElement(a))),s.appendChild(i),e.parentNode.removeChild(e)}const Oo=window.wp.blob;function Lo(e){if("IMG"===e.nodeName){if(0===e.src.indexOf("file:")&&(e.src=""),0===e.src.indexOf("data:")){const[t,r]=e.src.split(","),[n]=t.slice(5).split(";");if(!r||!n)return void(e.src="");let o;try{o=atob(r)}catch(t){return void(e.src="")}const a=new Uint8Array(o.length);for(let e=0;e<a.length;e++)a[e]=o.charCodeAt(e);const i=n.replace("/","."),s=new window.File([a],i,{type:n});e.src=(0,Oo.createBlobURL)(s)}1!==e.height&&1!==e.width||e.parentNode.removeChild(e)}}function Mo(e){"DIV"===e.nodeName&&(e.innerHTML=so(e.innerHTML))}var jo=r(1030);const Do=new(r.n(jo)().Converter)({noHeaderId:!0,tables:!0,literalMidWordUnderscores:!0,omitExtraWLInCodeBlocks:!0,simpleLineBreaks:!0,strikethrough:!0});function zo(e){if("IFRAME"===e.nodeName){const t=e.ownerDocument.createTextNode(e.src);e.parentNode.replaceChild(t,e)}}function Io(e){e.id&&0===e.id.indexOf("docs-internal-guid-")&&("B"===e.tagName?(0,K.unwrap)(e):e.removeAttribute("id"))}function Ro(e){if(e.nodeType!==e.TEXT_NODE)return;let t=e;for(;t=t.parentNode;)if(t.nodeType===t.ELEMENT_NODE&&"PRE"===t.nodeName)return;let r=e.data.replace(/[ \r\n\t]+/g," ");if(" "===r[0]){const t=wo(e,"previous");t&&"BR"!==t.nodeName&&" "!==t.textContent.slice(-1)||(r=r.slice(1))}if(" "===r[r.length-1]){const t=wo(e,"next");(!t||"BR"===t.nodeName||t.nodeType===t.TEXT_NODE&&(" "===(n=t.textContent[0])||"\r"===n||"\n"===n||"\t"===n))&&(r=r.slice(0,-1))}var n;r?e.data=r:e.parentNode.removeChild(e)}function Ho(e){"BR"===e.nodeName&&(wo(e,"next")||e.parentNode.removeChild(e))}function Vo(e){"P"===e.nodeName&&(e.hasChildNodes()||e.parentNode.removeChild(e))}function $o(e){if("SPAN"!==e.nodeName)return;if("paragraph-break"!==e.getAttribute("data-stringify-type"))return;const{parentNode:t}=e;t.insertBefore(e.ownerDocument.createElement("br"),e),t.insertBefore(e.ownerDocument.createElement("br"),e),t.removeChild(e)}const Uo=(...e)=>window?.console?.log?.(...e);function Fo(e){return e=yo(e,[Bo,Io,Ao,So,Co]),e=yo(e=(0,K.removeInvalidHTML)(e,(0,K.getPhrasingContentSchema)("paste"),{inline:!0}),[Ro,Ho]),Uo("Processed inline HTML:\n\n",e),e}function qo({HTML:e="",plainText:t="",mode:r="AUTO",tagName:n}){if(e=(e=(e=e.replace(/<meta[^>]+>/g,"")).replace(/^\s*<html[^>]*>\s*<body[^>]*>(?:\s*<!--\s*StartFragment\s*-->)?/i,"")).replace(/(?:<!--\s*EndFragment\s*-->\s*)?<\/body>\s*<\/html>\s*$/i,""),"INLINE"!==r){const r=e||t;if(-1!==r.indexOf("\x3c!-- wp:"))return oo(r)}String.prototype.normalize&&(e=e.normalize()),e=yo(e,[$o]);const o=t&&(!e||function(e){return!/<(?!br[ />])/i.test(e)}(e));var a;o&&(e=t,/^\s+$/.test(t)||(a=e,e=Do.makeHtml(function(e){return e.replace(/((?:^|\n)```)([^\n`]+)(```(?:$|\n))/,((e,t,r,n)=>`${t}\n${r}\n${n}`))}(function(e){return e.replace(/(^|\n)•( +)/g,"$1*$2")}(a)))));const i=bo(e),s=i.length>1;if(o&&!s&&"AUTO"===r&&-1===t.indexOf("\n")&&0!==t.indexOf("<p>")&&0===e.indexOf("<p>")&&(r="INLINE"),"INLINE"===r)return Fo(e);if("AUTO"===r&&!s&&function(e,t){const r=document.implementation.createHTMLDocument("");r.body.innerHTML=e;const n=Array.from(r.body.children);return!n.some(Eo)&&xo(n,t)}(e,n))return Fo(e);const c=(0,K.getPhrasingContentSchema)("paste"),l=_o("paste"),u=i.map((e=>{if("string"!=typeof e)return e;const t=[Io,Po,Bo,uo,Lo,So,co,Co,zo,fo,po,Mo],r={...l,...c};return e=yo(e,t,l),e=yo(e=so(e=(0,K.removeInvalidHTML)(e,r)),[Ro,Ho,Vo],l),Uo("Processed HTML piece:\n\n",e),io(e,qo)})).flat().filter(Boolean);if("AUTO"===r&&1===u.length&&ve(u[0].name,"__unstablePasteTextInline",!1)){const e=/^[\n]+|[\n]+$/g,r=t.replace(e,"");if(""!==r&&-1===r.indexOf("\n"))return(0,K.removeInvalidHTML)(qr(u[0]),c).replace(e,"")}return u}function Go(){return(0,i.select)(cr).getCategories()}function Ko(e){(0,i.dispatch)(cr).setCategories(e)}function Wo(e,t){(0,i.dispatch)(cr).updateCategory(e,t)}function Yo(e=[],t=[]){return e.length===t.length&&t.every((([t,,r],n)=>{const o=e[n];return t===o.name&&Yo(o.innerBlocks,r)}))}function Qo(e=[],t){return t?t.map((([t,r,n],o)=>{var a;const i=e[o];if(i&&i.name===t){const e=Qo(i.innerBlocks,n);return{...i,innerBlocks:e}}const s=ke(t),c=(e,t)=>t?Object.fromEntries(Object.entries(t).map((([t,r])=>[t,l(e[t],r)]))):{},l=(e,t)=>{return r=e,"html"===r?.source&&Array.isArray(t)?(0,G.renderToString)(t):(e=>"query"===e?.source)(e)&&t?t.map((t=>c(e.query,t))):t;var r},u=c(null!==(a=s?.attributes)&&void 0!==a?a:{},r);let[d,p]=Mn(t,u);return void 0===ke(d)&&(p={originalName:t,originalContent:"",originalUndelimitedContent:""},d="core/missing"),mr(d,p,Qo([],n))})):e}function Zo(e){return Dt()("wp.blocks.withBlockContentContext",{since:"6.1"}),e}})(),(window.wp=window.wp||{}).blocks=n})();