/*! This file is auto-generated */
(()=>{"use strict";var e={d:(t,n)=>{for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{store:()=>G});var n={};e.r(n),e.d(n,{__experimentalGetAllAnnotationsForBlock:()=>N,__experimentalGetAnnotations:()=>O,__experimentalGetAnnotationsForBlock:()=>T,__experimentalGetAnnotationsForRichText:()=>b});var r={};e.r(r),e.d(r,{__experimentalAddAnnotation:()=>S,__experimentalRemoveAnnotation:()=>C,__experimentalRemoveAnnotationsBySource:()=>F,__experimentalUpdateAnnotationRange:()=>P});const o=window.wp.richText,a=window.wp.i18n,i="core/annotations",l="core/annotation",c="annotation-text-";const s={name:l,title:(0,a.__)("Annotation"),tagName:"mark",className:"annotation-text",attributes:{className:"class",id:"id"},edit:()=>null,__experimentalGetPropsForEditableTreePreparation:(e,{richTextIdentifier:t,blockClientId:n})=>({annotations:e(i).__experimentalGetAnnotationsForRichText(n,t)}),__experimentalCreatePrepareEditableTree:({annotations:e})=>(t,n)=>{if(0===e.length)return t;let r={formats:t,text:n};return r=function(e,t=[]){return t.forEach((t=>{let{start:n,end:r}=t;n>e.text.length&&(n=e.text.length),r>e.text.length&&(r=e.text.length);const a=c+t.source,i=c+t.id;e=(0,o.applyFormat)(e,{type:l,attributes:{className:a,id:i}},n,r)})),e}(r,e),r.formats},__experimentalGetPropsForEditableTreeChangeHandler:e=>({removeAnnotation:e(i).__experimentalRemoveAnnotation,updateAnnotationRange:e(i).__experimentalUpdateAnnotationRange}),__experimentalCreateOnChangeEditableValue:e=>t=>{const n=function(e){const t={};return e.forEach(((e,n)=>{(e=(e=e||[]).filter((e=>e.type===l))).forEach((e=>{let{id:r}=e.attributes;r=r.replace(c,""),t.hasOwnProperty(r)||(t[r]={start:n}),t[r].end=n+1}))})),t}(t),{removeAnnotation:r,updateAnnotationRange:o,annotations:a}=e;!function(e,t,{removeAnnotation:n,updateAnnotationRange:r}){e.forEach((e=>{const o=t[e.id];if(!o)return void n(e.id);const{start:a,end:i}=e;a===o.start&&i===o.end||r(e.id,o.start,o.end)}))}(a,n,{removeAnnotation:r,updateAnnotationRange:o})}},{name:u,...d}=s;(0,o.registerFormatType)(u,d);const p=window.wp.hooks,f=window.wp.data;function m(e,t){const n=e.filter(t);return e.length===n.length?e:n}(0,p.addFilter)("editor.BlockListBlock","core/annotations",(e=>(0,f.withSelect)(((e,{clientId:t,className:n})=>({className:e(i).__experimentalGetAnnotationsForBlock(t).map((e=>"is-annotated-by-"+e.source)).concat(n).filter(Boolean).join(" ")})))(e)));const g=(e,t)=>Object.entries(e).reduce(((e,[n,r])=>({...e,[n]:t(r)})),{});const h=function(e={},t){var n;switch(t.type){case"ANNOTATION_ADD":const r=t.blockClientId,o={id:t.id,blockClientId:r,richTextIdentifier:t.richTextIdentifier,source:t.source,selector:t.selector,range:t.range};if("range"===o.selector&&!function(e){return"number"==typeof e.start&&"number"==typeof e.end&&e.start<=e.end}(o.range))return e;const a=null!==(n=e?.[r])&&void 0!==n?n:[];return{...e,[r]:[...a,o]};case"ANNOTATION_REMOVE":return g(e,(e=>m(e,(e=>e.id!==t.annotationId))));case"ANNOTATION_UPDATE_RANGE":return g(e,(e=>{let n=!1;const r=e.map((e=>e.id===t.annotationId?(n=!0,{...e,range:{start:t.start,end:t.end}}):e));return n?r:e}));case"ANNOTATION_REMOVE_SOURCE":return g(e,(e=>m(e,(e=>e.source!==t.source))))}return e};var _={};function A(e){return[e]}function v(e,t,n){var r;if(e.length!==t.length)return!1;for(r=n;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}function y(e,t){var n,r=t||A;function o(){n=new WeakMap}function a(){var t,o,a,i,l,c=arguments.length;for(i=new Array(c),a=0;a<c;a++)i[a]=arguments[a];for(t=function(e){var t,r,o,a,i,l=n,c=!0;for(t=0;t<e.length;t++){if(!(i=r=e[t])||"object"!=typeof i){c=!1;break}l.has(r)?l=l.get(r):(o=new WeakMap,l.set(r,o),l=o)}return l.has(_)||((a=function(){var e={clear:function(){e.head=null}};return e}()).isUniqueByDependants=c,l.set(_,a)),l.get(_)}(l=r.apply(null,i)),t.isUniqueByDependants||(t.lastDependants&&!v(l,t.lastDependants,0)&&t.clear(),t.lastDependants=l),o=t.head;o;){if(v(o.args,i,1))return o!==t.head&&(o.prev.next=o.next,o.next&&(o.next.prev=o.prev),o.next=t.head,o.prev=null,t.head.prev=o,t.head=o),o.val;o=o.next}return o={val:e.apply(null,i)},i[0]=null,o.args=i,t.head&&(t.head.prev=o,o.next=t.head),t.head=o,o.val}return a.getDependants=r,a.clear=o,o(),a}const x=[],T=y(((e,t)=>{var n;return(null!==(n=e?.[t])&&void 0!==n?n:[]).filter((e=>"block"===e.selector))}),((e,t)=>{var n;return[null!==(n=e?.[t])&&void 0!==n?n:x]}));function N(e,t){var n;return null!==(n=e?.[t])&&void 0!==n?n:x}const b=y(((e,t,n)=>{var r;return(null!==(r=e?.[t])&&void 0!==r?r:[]).filter((e=>"range"===e.selector&&n===e.richTextIdentifier)).map((e=>{const{range:t,...n}=e;return{...t,...n}}))}),((e,t)=>{var n;return[null!==(n=e?.[t])&&void 0!==n?n:x]}));function O(e){return Object.values(e).flat()}const I={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let w;const E=new Uint8Array(16);function R(){if(!w&&(w="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!w))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return w(E)}const U=[];for(let e=0;e<256;++e)U.push((e+256).toString(16).slice(1));function D(e,t=0){return U[e[t+0]]+U[e[t+1]]+U[e[t+2]]+U[e[t+3]]+"-"+U[e[t+4]]+U[e[t+5]]+"-"+U[e[t+6]]+U[e[t+7]]+"-"+U[e[t+8]]+U[e[t+9]]+"-"+U[e[t+10]]+U[e[t+11]]+U[e[t+12]]+U[e[t+13]]+U[e[t+14]]+U[e[t+15]]}const k=function(e,t,n){if(I.randomUUID&&!t&&!e)return I.randomUUID();const r=(e=e||{}).random||(e.rng||R)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(let e=0;e<16;++e)t[n+e]=r[e];return t}return D(r)};function S({blockClientId:e,richTextIdentifier:t=null,range:n=null,selector:r="range",source:o="default",id:a=k()}){const i={type:"ANNOTATION_ADD",id:a,blockClientId:e,richTextIdentifier:t,source:o,selector:r};return"range"===r&&(i.range=n),i}function C(e){return{type:"ANNOTATION_REMOVE",annotationId:e}}function P(e,t,n){return{type:"ANNOTATION_UPDATE_RANGE",annotationId:e,start:t,end:n}}function F(e){return{type:"ANNOTATION_REMOVE_SOURCE",source:e}}const G=(0,f.createReduxStore)(i,{reducer:h,selectors:n,actions:r});(0,f.register)(G),(window.wp=window.wp||{}).annotations=t})();