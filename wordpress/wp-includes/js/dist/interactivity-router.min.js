/*! This file is auto-generated */
import*as t from"@wordpress/interactivity";var e={d:(t,i)=>{for(var o in i)e.o(i,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:i[o]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)},i={};e.d(i,{o:()=>y,w:()=>f});const o=(t=>{var i={};return e.d(i,t),i})({getConfig:()=>t.getConfig,privateApis:()=>t.privateApis,store:()=>t.store}),{directivePrefix:a,getRegionRootFragment:n,initialVdom:r,toVdom:s,render:c,parseInitialData:l,populateInitialData:d,batch:g}=(0,o.privateApis)("I acknowledge that using private APIs means my theme or plugin will inevitably break in the next version of WordPress."),w=new Map,u=t=>{const e=new URL(t,window.location);return e.pathname+e.search},h=(t,{vdom:e}={})=>{const i={},o=`data-${a}-router-region`;t.querySelectorAll(`[${o}]`).forEach((t=>{const a=t.getAttribute(o);i[a]=e?.has(t)?e.get(t):s(t)}));const n=t.querySelector("title")?.innerText,r=l(t);return{regions:i,title:n,initialData:r}},m=t=>{g((()=>{d(t.initialData);const e=`data-${a}-router-region`;document.querySelectorAll(`[${e}]`).forEach((i=>{const o=i.getAttribute(e),a=n(i);c(t.regions[o],a)})),t.title&&(document.title=t.title)}))},p=t=>(window.location.assign(t),new Promise((()=>{})));window.addEventListener("popstate",(async()=>{const t=u(window.location),e=w.has(t)&&await w.get(t);e?(m(e),f.url=window.location.href):window.location.reload()})),w.set(u(window.location),Promise.resolve(h(document,{vdom:r})));let v="";const{state:f,actions:y}=(0,o.store)("core/router",{state:{url:window.location.href,navigation:{hasStarted:!1,hasFinished:!1,texts:{}}},actions:{*navigate(t,e={}){const{clientNavigationDisabled:i}=(0,o.getConfig)();i&&(yield p(t));const a=u(t),{navigation:n}=f,{loadingAnimation:r=!0,screenReaderAnnouncement:s=!0,timeout:c=1e4}=e;v=t,y.prefetch(a,e);const l=new Promise((t=>setTimeout(t,c))),d=setTimeout((()=>{v===t&&(r&&(n.hasStarted=!0,n.hasFinished=!1),s&&(n.message=n.texts.loading))}),400),g=yield Promise.race([w.get(a),l]);clearTimeout(d),v===t&&(g&&!g.initialData?.config?.["core/router"]?.clientNavigationDisabled?(m(g),window.history[e.replace?"replaceState":"pushState"]({},"",t),f.url=t,r&&(n.hasStarted=!1,n.hasFinished=!0),s&&(n.message=n.texts.loaded+(n.message===n.texts.loaded?" ":""))):yield p(t))},prefetch(t,e={}){const{clientNavigationDisabled:i}=(0,o.getConfig)();if(i)return;const a=u(t);!e.force&&w.has(a)||w.set(a,(async(t,{html:e})=>{try{if(!e){const i=await window.fetch(t);if(200!==i.status)return!1;e=await i.text()}const i=(new window.DOMParser).parseFromString(e,"text/html");return h(i)}catch(t){return!1}})(a,e))}}});var b=i.o,x=i.w;export{b as actions,x as state};