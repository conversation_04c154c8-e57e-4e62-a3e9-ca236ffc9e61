/*! This file is auto-generated */
(()=>{"use strict";var e={6689:(e,t,n)=>{n.d(t,{createUndoManager:()=>a});var r=n(923),s=n.n(r);function i(e,t){const n={...e};return Object.entries(t).forEach((([e,t])=>{n[e]?n[e]={...n[e],to:t.to}:n[e]=t})),n}const o=(e,t)=>{const n=e?.findIndex((({id:e})=>"string"==typeof e?e===t.id:s()(e,t.id))),r=[...e];return-1!==n?r[n]={id:t.id,changes:i(r[n].changes,t.changes)}:r.push(t),r};function a(){let e=[],t=[],n=0;const r=()=>{e=e.slice(0,n||void 0),n=0},i=()=>{var n;const r=0===e.length?0:e.length-1;let s=null!==(n=e[r])&&void 0!==n?n:[];t.forEach((e=>{s=o(s,e)})),t=[],e[r]=s};return{addRecord(n,a=!1){const c=!n||(e=>!e.filter((({changes:e})=>Object.values(e).some((({from:e,to:t})=>"function"!=typeof e&&"function"!=typeof t&&!s()(e,t))))).length)(n);if(a){if(c)return;n.forEach((e=>{t=o(t,e)}))}else{if(r(),t.length&&i(),c)return;e.push(n)}},undo(){t.length&&(r(),i());const s=e[e.length-1+n];if(s)return n-=1,s},redo(){const t=e[e.length+n];if(t)return n+=1,t},hasUndo:()=>!!e[e.length-1+n],hasRedo:()=>!!e[e.length+n]}}},3249:e=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function n(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function r(e,t){var n=e._map,r=e._arrayTreeMap,s=e._objectTreeMap;if(n.has(t))return n.get(t);for(var i=Object.keys(t).sort(),o=Array.isArray(t)?r:s,a=0;a<i.length;a++){var c=i[a];if(void 0===(o=o.get(c)))return;var l=t[c];if(void 0===(o=o.get(l)))return}var u=o.get("_ekm_value");return u?(n.delete(u[0]),u[0]=t,o.set("_ekm_value",u),n.set(t,u),u):void 0}var s=function(){function e(t){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.clear(),t instanceof e){var n=[];t.forEach((function(e,t){n.push([t,e])})),t=n}if(null!=t)for(var r=0;r<t.length;r++)this.set(t[r][0],t[r][1])}var s,i,o;return s=e,i=[{key:"set",value:function(n,r){if(null===n||"object"!==t(n))return this._map.set(n,r),this;for(var s=Object.keys(n).sort(),i=[n,r],o=Array.isArray(n)?this._arrayTreeMap:this._objectTreeMap,a=0;a<s.length;a++){var c=s[a];o.has(c)||o.set(c,new e),o=o.get(c);var l=n[c];o.has(l)||o.set(l,new e),o=o.get(l)}var u=o.get("_ekm_value");return u&&this._map.delete(u[0]),o.set("_ekm_value",i),this._map.set(n,i),this}},{key:"get",value:function(e){if(null===e||"object"!==t(e))return this._map.get(e);var n=r(this,e);return n?n[1]:void 0}},{key:"has",value:function(e){return null===e||"object"!==t(e)?this._map.has(e):void 0!==r(this,e)}},{key:"delete",value:function(e){return!!this.has(e)&&(this.set(e,void 0),!0)}},{key:"forEach",value:function(e){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this;this._map.forEach((function(s,i){null!==i&&"object"===t(i)&&(s=s[1]),e.call(r,s,i,n)}))}},{key:"clear",value:function(){this._map=new Map,this._arrayTreeMap=new Map,this._objectTreeMap=new Map}},{key:"size",get:function(){return this._map.size}}],i&&n(s.prototype,i),o&&n(s,o),e}();e.exports=s},7734:e=>{e.exports=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var r,s,i;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(s=r;0!=s--;)if(!e(t[s],n[s]))return!1;return!0}if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(s of t.entries())if(!n.has(s[0]))return!1;for(s of t.entries())if(!e(s[1],n.get(s[0])))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(s of t.entries())if(!n.has(s[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(n)){if((r=t.length)!=n.length)return!1;for(s=r;0!=s--;)if(t[s]!==n[s])return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(i=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(s=r;0!=s--;)if(!Object.prototype.hasOwnProperty.call(n,i[s]))return!1;for(s=r;0!=s--;){var o=i[s];if(!e(t[o],n[o]))return!1}return!0}return t!=t&&n!=n}},923:e=>{e.exports=window.wp.isShallowEqual}},t={};function n(r){var s=t[r];if(void 0!==s)return s.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{n.r(r),n.d(r,{EntityProvider:()=>Zn,__experimentalFetchLinkSuggestions:()=>Zt,__experimentalFetchUrlData:()=>en,__experimentalUseEntityRecord:()=>ur,__experimentalUseEntityRecords:()=>fr,__experimentalUseResourcePermissions:()=>Er,fetchBlockPatterns:()=>tn,store:()=>vr,useEntityBlockEditor:()=>nr,useEntityId:()=>Xn,useEntityProp:()=>er,useEntityRecord:()=>lr,useEntityRecords:()=>pr,useResourcePermissions:()=>yr});var e={};n.r(e),n.d(e,{__experimentalBatch:()=>ce,__experimentalReceiveCurrentGlobalStylesId:()=>W,__experimentalReceiveThemeBaseGlobalStyles:()=>J,__experimentalReceiveThemeGlobalStyleVariations:()=>Z,__experimentalSaveSpecifiedEntityEdits:()=>ue,__unstableCreateUndoLevel:()=>oe,addEntities:()=>Y,deleteEntityRecord:()=>ne,editEntityRecord:()=>re,receiveAutosaves:()=>fe,receiveCurrentTheme:()=>z,receiveCurrentUser:()=>Q,receiveDefaultTemplateId:()=>Ee,receiveEmbedPreview:()=>te,receiveEntityRecords:()=>H,receiveNavigationFallbackId:()=>ye,receiveRevisions:()=>me,receiveThemeGlobalStyleRevisions:()=>ee,receiveThemeSupports:()=>X,receiveUploadPermissions:()=>de,receiveUserPermission:()=>pe,receiveUserQuery:()=>K,redo:()=>ie,saveEditedEntityRecord:()=>le,saveEntityRecord:()=>ae,undo:()=>se});var t={};n.r(t),n.d(t,{__experimentalGetCurrentGlobalStylesId:()=>It,__experimentalGetCurrentThemeBaseGlobalStyles:()=>Dt,__experimentalGetCurrentThemeGlobalStylesVariations:()=>Nt,__experimentalGetDirtyEntityRecords:()=>lt,__experimentalGetEntitiesBeingSaved:()=>ut,__experimentalGetEntityRecordNoResolver:()=>rt,__experimentalGetTemplateForLink:()=>jt,canUser:()=>At,canUserEditEntityRecord:()=>Ct,getAuthors: <AUTHORS>