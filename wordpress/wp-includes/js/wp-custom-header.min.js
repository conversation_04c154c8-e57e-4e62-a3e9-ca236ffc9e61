/*! This file is auto-generated */
!function(i,t){var e,n;function a(e,t){var n;"function"==typeof i.Event?n=new Event(t):(n=document.createEvent("Event")).initEvent(t,!0,!0),e.dispatchEvent(n)}function o(){this.handlers={nativeVideo:new e,youtube:new n}}function s(){}i.wp=i.wp||{},"addEventListener"in i&&(o.prototype={initialize:function(){if(this.supportsVideo())for(var e in this.handlers){e=this.handlers[e];if("test"in e&&e.test(t)){this.activeHandler=e.initialize.call(e,t),a(document,"wp-custom-header-video-loaded");break}}},supportsVideo:function(){return!(i.innerWidth<t.minWidth||i.innerHeight<t.minHeight)},BaseVideoHandler:s},s.prototype={initialize:function(e){var t=this,n=document.createElement("button");this.settings=e,this.container=document.getElementById("wp-custom-header"),(this.button=n).setAttribute("type","button"),n.setAttribute("id","wp-custom-header-video-button"),n.setAttribute("class","wp-custom-header-video-button wp-custom-header-video-play"),n.innerHTML=e.l10n.play,n.addEventListener("click",function(){t.isPaused()?t.play():t.pause()}),this.container.addEventListener("play",function(){n.className="wp-custom-header-video-button wp-custom-header-video-play",n.innerHTML=e.l10n.pause,"a11y"in i.wp&&i.wp.a11y.speak(e.l10n.playSpeak)}),this.container.addEventListener("pause",function(){n.className="wp-custom-header-video-button wp-custom-header-video-pause",n.innerHTML=e.l10n.play,"a11y"in i.wp&&i.wp.a11y.speak(e.l10n.pauseSpeak)}),this.ready()},ready:function(){},isPaused:function(){},pause:function(){},play:function(){},setVideo:function(e){var t,n=this.container.getElementsByClassName("customize-partial-edit-shortcut");n.length&&(t=this.container.removeChild(n[0])),this.container.innerHTML="",this.container.appendChild(e),t&&this.container.appendChild(t)},showControls:function(){this.container.contains(this.button)||this.container.appendChild(this.button)},test:function(){return!1},trigger:function(e){a(this.container,e)}},e=(s.extend=function(e){function t(){return s.apply(this,arguments)}for(var n in(t.prototype=Object.create(s.prototype)).constructor=t,e)t.prototype[n]=e[n];return t})({test:function(e){return document.createElement("video").canPlayType(e.mimeType)},ready:function(){var e=this,t=document.createElement("video");t.id="wp-custom-header-video",t.autoplay=!0,t.loop=!0,t.muted=!0,t.playsInline=!0,t.width=this.settings.width,t.height=this.settings.height,t.addEventListener("play",function(){e.trigger("play")}),t.addEventListener("pause",function(){e.trigger("pause")}),t.addEventListener("canplay",function(){e.showControls()}),this.video=t,e.setVideo(t),t.src=this.settings.videoUrl},isPaused:function(){return this.video.paused},pause:function(){this.video.pause()},play:function(){this.video.play()}}),n=s.extend({test:function(e){return"video/x-youtube"===e.mimeType},ready:function(){var e,t=this;"YT"in i?YT.ready(t.loadVideo.bind(t)):((e=document.createElement("script")).src="https://www.youtube.com/iframe_api",e.onload=function(){YT.ready(t.loadVideo.bind(t))},document.getElementsByTagName("head")[0].appendChild(e))},loadVideo:function(){var t=this,e=document.createElement("div");e.id="wp-custom-header-video",t.setVideo(e),t.player=new YT.Player(e,{height:this.settings.height,width:this.settings.width,videoId:this.settings.videoUrl.match(/^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/)[1],events:{onReady:function(e){e.target.mute(),t.showControls()},onStateChange:function(e){YT.PlayerState.PLAYING===e.data?t.trigger("play"):YT.PlayerState.PAUSED===e.data?t.trigger("pause"):YT.PlayerState.ENDED===e.data&&e.target.playVideo()}},playerVars:{autoplay:1,controls:0,disablekb:1,fs:0,iv_load_policy:3,loop:1,modestbranding:1,playsinline:1,rel:0,showinfo:0}})},isPaused:function(){return YT.PlayerState.PAUSED===this.player.getPlayerState()},pause:function(){this.player.pauseVideo()},play:function(){this.player.playVideo()}}),i.wp.customHeader=new o,document.addEventListener("DOMContentLoaded",i.wp.customHeader.initialize.bind(i.wp.customHeader),!1),"customize"in i.wp)&&(i.wp.customize.selectiveRefresh.bind("render-partials-response",function(e){"custom_header_settings"in e&&(t=e.custom_header_settings)}),i.wp.customize.selectiveRefresh.bind("partial-content-rendered",function(e){"custom_header"===e.partial.id&&i.wp.customHeader.initialize()}))}(window,window._wpCustomHeaderSettings||{});