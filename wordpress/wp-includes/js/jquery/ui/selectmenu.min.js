/*!
 * jQuery UI Selectmenu 1.13.2
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 */
!function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery","./menu","./core"],e):e(jQuery)}(function(u){"use strict";return u.widget("ui.selectmenu",[u.ui.formResetMixin,{version:"1.13.2",defaultElement:"<select>",options:{appendTo:null,classes:{"ui-selectmenu-button-open":"ui-corner-top","ui-selectmenu-button-closed":"ui-corner-all"},disabled:null,icons:{button:"ui-icon-triangle-1-s"},position:{my:"left top",at:"left bottom",collision:"none"},width:!1,change:null,close:null,focus:null,open:null,select:null},_create:function(){var e=this.element.uniqueId().attr("id");this.ids={element:e,button:e+"-button",menu:e+"-menu"},this._drawButton(),this._drawMenu(),this._bindFormResetHandler(),this._rendered=!1,this.menuItems=u()},_drawButton:function(){var e,t=this,i=this._parseOption(this.element.find("option:selected"),this.element[0].selectedIndex);this.labels=this.element.labels().attr("for",this.ids.button),this._on(this.labels,{click:function(e){this.button.trigger("focus"),e.preventDefault()}}),this.element.hide(),this.button=u("<span>",{tabindex:this.options.disabled?-1:0,id:this.ids.button,role:"combobox","aria-expanded":"false","aria-autocomplete":"list","aria-owns":this.ids.menu,"aria-haspopup":"true",title:this.element.attr("title")}).insertAfter(this.element),this._addClass(this.button,"ui-selectmenu-button ui-selectmenu-button-closed","ui-button ui-widget"),e=u("<span>").appendTo(this.button),this._addClass(e,"ui-selectmenu-icon","ui-icon "+this.options.icons.button),this.buttonItem=this._renderButtonItem(i).appendTo(this.button),!1!==this.options.width&&this._resizeButton(),this._on(this.button,this._buttonEvents),this.button.one("focusin",function(){t._rendered||t._refreshMenu()})},_drawMenu:function(){var i=this;this.menu=u("<ul>",{"aria-hidden":"true","aria-labelledby":this.ids.button,id:this.ids.menu}),this.menuWrap=u("<div>").append(this.menu),this._addClass(this.menuWrap,"ui-selectmenu-menu","ui-front"),this.menuWrap.appendTo(this._appendTo()),this.menuInstance=this.menu.menu({classes:{"ui-menu":"ui-corner-bottom"},role:"listbox",select:function(e,t){e.preventDefault(),i._setSelection(),i._select(t.item.data("ui-selectmenu-item"),e)},focus:function(e,t){t=t.item.data("ui-selectmenu-item");null!=i.focusIndex&&t.index!==i.focusIndex&&(i._trigger("focus",e,{item:t}),i.isOpen||i._select(t,e)),i.focusIndex=t.index,i.button.attr("aria-activedescendant",i.menuItems.eq(t.index).attr("id"))}}).menu("instance"),this.menuInstance._off(this.menu,"mouseleave"),this.menuInstance._closeOnDocumentClick=function(){return!1},this.menuInstance._isDivider=function(){return!1}},refresh:function(){this._refreshMenu(),this.buttonItem.replaceWith(this.buttonItem=this._renderButtonItem(this._getSelectedItem().data("ui-selectmenu-item")||{})),null===this.options.width&&this._resizeButton()},_refreshMenu:function(){var e=this.element.find("option");this.menu.empty(),this._parseOptions(e),this._renderMenu(this.menu,this.items),this.menuInstance.refresh(),this.menuItems=this.menu.find("li").not(".ui-selectmenu-optgroup").find(".ui-menu-item-wrapper"),this._rendered=!0,e.length&&(e=this._getSelectedItem(),this.menuInstance.focus(null,e),this._setAria(e.data("ui-selectmenu-item")),this._setOption("disabled",this.element.prop("disabled")))},open:function(e){this.options.disabled||(this._rendered?(this._removeClass(this.menu.find(".ui-state-active"),null,"ui-state-active"),this.menuInstance.focus(null,this._getSelectedItem())):this._refreshMenu(),this.menuItems.length&&(this.isOpen=!0,this._toggleAttr(),this._resizeMenu(),this._position(),this._on(this.document,this._documentClick),this._trigger("open",e)))},_position:function(){this.menuWrap.position(u.extend({of:this.button},this.options.position))},close:function(e){this.isOpen&&(this.isOpen=!1,this._toggleAttr(),this.range=null,this._off(this.document),this._trigger("close",e))},widget:function(){return this.button},menuWidget:function(){return this.menu},_renderButtonItem:function(e){var t=u("<span>");return this._setText(t,e.label),this._addClass(t,"ui-selectmenu-text"),t},_renderMenu:function(n,e){var s=this,o="";u.each(e,function(e,t){var i;t.optgroup!==o&&(i=u("<li>",{text:t.optgroup}),s._addClass(i,"ui-selectmenu-optgroup","ui-menu-divider"+(t.element.parent("optgroup").prop("disabled")?" ui-state-disabled":"")),i.appendTo(n),o=t.optgroup),s._renderItemData(n,t)})},_renderItemData:function(e,t){return this._renderItem(e,t).data("ui-selectmenu-item",t)},_renderItem:function(e,t){var i=u("<li>"),n=u("<div>",{title:t.element.attr("title")});return t.disabled&&this._addClass(i,null,"ui-state-disabled"),this._setText(n,t.label),i.append(n).appendTo(e)},_setText:function(e,t){t?e.text(t):e.html("&#160;")},_move:function(e,t){var i,n=".ui-menu-item";this.isOpen?i=this.menuItems.eq(this.focusIndex).parent("li"):(i=this.menuItems.eq(this.element[0].selectedIndex).parent("li"),n+=":not(.ui-state-disabled)"),(i="first"===e||"last"===e?i["first"===e?"prevAll":"nextAll"](n).eq(-1):i[e+"All"](n).eq(0)).length&&this.menuInstance.focus(t,i)},_getSelectedItem:function(){return this.menuItems.eq(this.element[0].selectedIndex).parent("li")},_toggle:function(e){this[this.isOpen?"close":"open"](e)},_setSelection:function(){var e;this.range&&(window.getSelection?((e=window.getSelection()).removeAllRanges(),e.addRange(this.range)):this.range.select(),this.button.trigger("focus"))},_documentClick:{mousedown:function(e){!this.isOpen||u(e.target).closest(".ui-selectmenu-menu, #"+u.escapeSelector(this.ids.button)).length||this.close(e)}},_buttonEvents:{mousedown:function(){var e;window.getSelection?(e=window.getSelection()).rangeCount&&(this.range=e.getRangeAt(0)):this.range=document.selection.createRange()},click:function(e){this._setSelection(),this._toggle(e)},keydown:function(e){var t=!0;switch(e.keyCode){case u.ui.keyCode.TAB:case u.ui.keyCode.ESCAPE:this.close(e),t=!1;break;case u.ui.keyCode.ENTER:this.isOpen&&this._selectFocusedItem(e);break;case u.ui.keyCode.UP:e.altKey?this._toggle(e):this._move("prev",e);break;case u.ui.keyCode.DOWN:e.altKey?this._toggle(e):this._move("next",e);break;case u.ui.keyCode.SPACE:this.isOpen?this._selectFocusedItem(e):this._toggle(e);break;case u.ui.keyCode.LEFT:this._move("prev",e);break;case u.ui.keyCode.RIGHT:this._move("next",e);break;case u.ui.keyCode.HOME:case u.ui.keyCode.PAGE_UP:this._move("first",e);break;case u.ui.keyCode.END:case u.ui.keyCode.PAGE_DOWN:this._move("last",e);break;default:this.menu.trigger(e),t=!1}t&&e.preventDefault()}},_selectFocusedItem:function(e){var t=this.menuItems.eq(this.focusIndex).parent("li");t.hasClass("ui-state-disabled")||this._select(t.data("ui-selectmenu-item"),e)},_select:function(e,t){var i=this.element[0].selectedIndex;this.element[0].selectedIndex=e.index,this.buttonItem.replaceWith(this.buttonItem=this._renderButtonItem(e)),this._setAria(e),this._trigger("select",t,{item:e}),e.index!==i&&this._trigger("change",t,{item:e}),this.close(t)},_setAria:function(e){e=this.menuItems.eq(e.index).attr("id");this.button.attr({"aria-labelledby":e,"aria-activedescendant":e}),this.menu.attr("aria-activedescendant",e)},_setOption:function(e,t){var i;"icons"===e&&(i=this.button.find("span.ui-icon"),this._removeClass(i,null,this.options.icons.button)._addClass(i,null,t.button)),this._super(e,t),"appendTo"===e&&this.menuWrap.appendTo(this._appendTo()),"width"===e&&this._resizeButton()},_setOptionDisabled:function(e){this._super(e),this.menuInstance.option("disabled",e),this.button.attr("aria-disabled",e),this._toggleClass(this.button,null,"ui-state-disabled",e),this.element.prop("disabled",e),e?(this.button.attr("tabindex",-1),this.close()):this.button.attr("tabindex",0)},_appendTo:function(){var e=this.options.appendTo;return e=(e=(e=e&&(e.jquery||e.nodeType?u(e):this.document.find(e).eq(0)))&&e[0]?e:this.element.closest(".ui-front, dialog")).length?e:this.document[0].body},_toggleAttr:function(){this.button.attr("aria-expanded",this.isOpen),this._removeClass(this.button,"ui-selectmenu-button-"+(this.isOpen?"closed":"open"))._addClass(this.button,"ui-selectmenu-button-"+(this.isOpen?"open":"closed"))._toggleClass(this.menuWrap,"ui-selectmenu-open",null,this.isOpen),this.menu.attr("aria-hidden",!this.isOpen)},_resizeButton:function(){var e=this.options.width;!1===e?this.button.css("width",""):(null===e&&(e=this.element.show().outerWidth(),this.element.hide()),this.button.outerWidth(e))},_resizeMenu:function(){this.menu.outerWidth(Math.max(this.button.outerWidth(),this.menu.width("").outerWidth()+1))},_getCreateOptions:function(){var e=this._super();return e.disabled=this.element.prop("disabled"),e},_parseOptions:function(e){var i=this,n=[];e.each(function(e,t){t.hidden||n.push(i._parseOption(u(t),e))}),this.items=n},_parseOption:function(e,t){var i=e.parent("optgroup");return{element:e,index:t,value:e.val(),label:e.text(),optgroup:i.attr("label")||"",disabled:i.prop("disabled")||e.prop("disabled")}},_destroy:function(){this._unbindFormResetHandler(),this.menuWrap.remove(),this.button.remove(),this.element.show(),this.element.removeUniqueId(),this.labels.attr("for",this.ids.element)}}])});