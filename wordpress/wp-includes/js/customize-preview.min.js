/*! This file is auto-generated */
!function(r){var s,i,a,o,c,n,u,l,d,h=wp.customize,p={};(i=history).replaceState&&(c=function(e){var t,n=document.createElement("a");return n.href=e,e=h.utils.parseQueryString(location.search.substr(1)),(t=h.utils.parseQueryString(n.search.substr(1))).customize_changeset_uuid=e.customize_changeset_uuid,e.customize_autosaved&&(t.customize_autosaved="on"),e.customize_theme&&(t.customize_theme=e.customize_theme),e.customize_messenger_channel&&(t.customize_messenger_channel=e.customize_messenger_channel),n.search=r.param(t),n.href},i.replaceState=(a=i.replaceState,function(e,t,n){return p=e,a.call(i,e,t,"string"==typeof n&&0<n.length?c(n):n)}),i.pushState=(o=i.pushState,function(e,t,n){return p=e,o.call(i,e,t,"string"==typeof n&&0<n.length?c(n):n)}),window.addEventListener("popstate",function(e){p=e.state})),s=function(t,n,i){var s;return function(){var e=arguments;i=i||this,clearTimeout(s),s=setTimeout(function(){s=null,t.apply(i,e)},n)}},h.Preview=h.Messenger.extend({initialize:function(e,t){var n=this,i=document.createElement("a");h.Messenger.prototype.initialize.call(n,e,t),i.href=n.origin(),n.add("scheme",i.protocol.replace(/:$/,"")),n.body=r(document.body),n.window=r(window),h.settings.channel&&(n.body.on("click.preview","a",function(e){n.handleLinkClick(e)}),n.body.on("submit.preview","form",function(e){n.handleFormSubmit(e)}),n.window.on("scroll.preview",s(function(){n.send("scroll",n.window.scrollTop())},200)),n.bind("scroll",function(e){n.window.scrollTop(e)}))},handleLinkClick:function(e){var t=r(e.target).closest("a");_.isUndefined(t.attr("href"))||"#"!==t.attr("href").substr(0,1)&&/^https?:$/.test(t.prop("protocol"))&&(h.isLinkPreviewable(t[0])?(e.preventDefault(),e.shiftKey||this.send("url",t.prop("href"))):(wp.a11y.speak(h.settings.l10n.linkUnpreviewable),e.preventDefault()))},handleFormSubmit:function(e){var t=document.createElement("a"),n=r(e.target);t.href=n.prop("action"),"GET"===n.prop("method").toUpperCase()&&h.isLinkPreviewable(t)?e.isDefaultPrevented()||(1<t.search.length&&(t.search+="&"),t.search+=n.serialize(),this.send("url",t.href)):wp.a11y.speak(h.settings.l10n.formUnpreviewable),e.preventDefault()}}),h.addLinkPreviewing=function(){var t="a[href], area[href]";r(document.body).find(t).each(function(){h.prepareLinkPreview(this)}),"undefined"!=typeof MutationObserver?(h.mutationObserver=new MutationObserver(function(e){_.each(e,function(e){r(e.target).find(t).each(function(){h.prepareLinkPreview(this)})})}),h.mutationObserver.observe(document.documentElement,{childList:!0,subtree:!0})):r(document.documentElement).on("click focus mouseover",t,function(){h.prepareLinkPreview(this)})},h.isLinkPreviewable=function(t,e){var n,i,e=_.extend({},{allowAdminAjax:!1},e||{});return"javascript:"===t.protocol||("https:"===t.protocol||"http:"===t.protocol)&&(i=t.host.replace(/:(80|443)$/,""),n=document.createElement("a"),!_.isUndefined(_.find(h.settings.url.allowed,function(e){return n.href=e,n.protocol===t.protocol&&n.host.replace(/:(80|443)$/,"")===i&&0===t.pathname.indexOf(n.pathname.replace(/\/$/,""))})))&&!/\/wp-(login|signup)\.php$/.test(t.pathname)&&(/\/wp-admin\/admin-ajax\.php$/.test(t.pathname)?e.allowAdminAjax:!/\/wp-(admin|includes|content)(\/|$)/.test(t.pathname))},h.prepareLinkPreview=function(e){var t,n=r(e);e.hasAttribute("href")&&!n.closest("#wpadminbar").length&&"#"!==n.attr("href").substr(0,1)&&/^https?:$/.test(e.protocol)&&(h.settings.channel&&"https"===h.preview.scheme.get()&&"http:"===e.protocol&&-1!==h.settings.url.allowedHosts.indexOf(e.host)&&(e.protocol="https:"),n.hasClass("wp-playlist-caption")||(h.isLinkPreviewable(e)?(n.removeClass("customize-unpreviewable"),(t=h.utils.parseQueryString(e.search.substring(1))).customize_changeset_uuid=h.settings.changeset.uuid,h.settings.changeset.autosaved&&(t.customize_autosaved="on"),h.settings.theme.active||(t.customize_theme=h.settings.theme.stylesheet),h.settings.channel&&(t.customize_messenger_channel=h.settings.channel),e.search=r.param(t)):h.settings.channel&&n.addClass("customize-unpreviewable")))},h.addRequestPreviewing=function(){r.ajaxPrefilter(function(e,t,n){var i,s,a={},o=document.createElement("a");o.href=e.url,h.isLinkPreviewable(o,{allowAdminAjax:!0})&&(i=h.utils.parseQueryString(o.search.substring(1)),h.each(function(e){e._dirty&&(a[e.id]=e.get())}),_.isEmpty(a)||("POST"!==(s=e.type.toUpperCase())&&(n.setRequestHeader("X-HTTP-Method-Override",s),i._method=s,e.type="POST"),e.data?e.data+="&":e.data="",e.data+=r.param({customized:JSON.stringify(a)})),i.customize_changeset_uuid=h.settings.changeset.uuid,h.settings.changeset.autosaved&&(i.customize_autosaved="on"),h.settings.theme.active||(i.customize_theme=h.settings.theme.stylesheet),i.customize_preview_nonce=h.settings.nonce.preview,o.search=r.param(i),e.url=o.href)})},h.addFormPreviewing=function(){r(document.body).find("form").each(function(){h.prepareFormPreview(this)}),"undefined"!=typeof MutationObserver&&(h.mutationObserver=new MutationObserver(function(e){_.each(e,function(e){r(e.target).find("form").each(function(){h.prepareFormPreview(this)})})}),h.mutationObserver.observe(document.documentElement,{childList:!0,subtree:!0}))},h.prepareFormPreview=function(i){var e,t={};i.action||(i.action=location.href),(e=document.createElement("a")).href=i.action,h.settings.channel&&"https"===h.preview.scheme.get()&&"http:"===e.protocol&&-1!==h.settings.url.allowedHosts.indexOf(e.host)&&(e.protocol="https:",i.action=e.href),"GET"===i.method.toUpperCase()&&h.isLinkPreviewable(e)?(r(i).removeClass("customize-unpreviewable"),t.customize_changeset_uuid=h.settings.changeset.uuid,h.settings.changeset.autosaved&&(t.customize_autosaved="on"),h.settings.theme.active||(t.customize_theme=h.settings.theme.stylesheet),h.settings.channel&&(t.customize_messenger_channel=h.settings.channel),_.each(t,function(e,t){var n=r(i).find('input[name="'+t+'"]');n.length?n.val(e):r(i).prepend(r("<input>",{type:"hidden",name:t,value:e}))}),h.settings.channel&&(i.target="_self")):h.settings.channel&&r(i).addClass("customize-unpreviewable")},h.keepAliveCurrentUrl=(n=location.pathname,u=location.search.substr(1),l=null,d=["customize_theme","customize_changeset_uuid","customize_messenger_channel","customize_autosaved"],function(){var e,t;u===location.search.substr(1)&&n===location.pathname?h.preview.send("keep-alive"):(e=document.createElement("a"),null===l&&(e.search=u,l=h.utils.parseQueryString(u),_.each(d,function(e){delete l[e]})),e.href=location.href,t=h.utils.parseQueryString(e.search.substr(1)),_.each(d,function(e){delete t[e]}),n===location.pathname&&_.isEqual(l,t)?h.preview.send("keep-alive"):(e.search=r.param(t),e.hash="",h.settings.url.self=e.href,h.preview.send("ready",{currentUrl:h.settings.url.self,activePanels:h.settings.activePanels,activeSections:h.settings.activeSections,activeControls:h.settings.activeControls,settingValidities:h.settings.settingValidities})),l=t,u=location.search.substr(1),n=location.pathname)}),h.settingPreviewHandlers={custom_logo:function(e){r("body").toggleClass("wp-custom-logo",!!e)},custom_css:function(e){r("#wp-custom-css").text(e)},background:function(){var e="",t={};_.each(["color","image","preset","position_x","position_y","size","repeat","attachment"],function(e){t[e]=h("background_"+e)}),r(document.body).toggleClass("custom-background",!(!t.color()&&!t.image())),t.color()&&(e+="background-color: "+t.color()+";"),t.image()&&(e=(e=(e=(e=(e+='background-image: url("'+t.image()+'");')+"background-size: "+t.size()+";")+"background-position: "+t.position_x()+" "+t.position_y()+";")+"background-repeat: "+t.repeat()+";")+"background-attachment: "+t.attachment()+";"),r("#custom-background-css").text("body.custom-background { "+e+" }")}},r(function(){var e,t,n;h.settings=window._wpCustomizeSettings,h.settings&&(h.preview=new h.Preview({url:window.location.href,channel:h.settings.channel}),h.addLinkPreviewing(),h.addRequestPreviewing(),h.addFormPreviewing(),t=function(e,t,n){var i=h(e);i?i.set(t):(n=n||!1,i=h.create(e,t,{id:e}),n&&(i._dirty=!0))},h.preview.bind("settings",function(e){r.each(e,t)}),h.preview.trigger("settings",h.settings.values),r.each(h.settings._dirty,function(e,t){t=h(t);t&&(t._dirty=!0)}),h.preview.bind("setting",function(e){t.apply(null,e.concat(!0))}),h.preview.bind("sync",function(t){t.settings&&t["settings-modified-while-loading"]&&_.each(_.keys(t.settings),function(e){h.has(e)&&!t["settings-modified-while-loading"][e]&&delete t.settings[e]}),r.each(t,function(e,t){h.preview.trigger(e,t)}),h.preview.send("synced")}),h.preview.bind("active",function(){h.preview.send("nonce",h.settings.nonce),h.preview.send("documentTitle",document.title),h.preview.send("scroll",r(window).scrollTop())}),h.preview.bind("changeset-uuid",n=function(e){h.settings.changeset.uuid=e,r(document.body).find("a[href], area[href]").each(function(){h.prepareLinkPreview(this)}),r(document.body).find("form").each(function(){h.prepareFormPreview(this)}),history.replaceState&&history.replaceState(p,"",location.href)}),h.preview.bind("saved",function(e){e.next_changeset_uuid&&n(e.next_changeset_uuid),h.trigger("saved",e)}),h.preview.bind("autosaving",function(){h.settings.changeset.autosaved||(h.settings.changeset.autosaved=!0,r(document.body).find("a[href], area[href]").each(function(){h.prepareLinkPreview(this)}),r(document.body).find("form").each(function(){h.prepareFormPreview(this)}),history.replaceState&&history.replaceState(p,"",location.href))}),h.preview.bind("changeset-saved",function(e){_.each(e.saved_changeset_values,function(e,t){t=h(t);t&&_.isEqual(t.get(),e)&&(t._dirty=!1)})}),h.preview.bind("nonce-refresh",function(e){r.extend(h.settings.nonce,e)}),h.preview.send("ready",{currentUrl:h.settings.url.self,activePanels:h.settings.activePanels,activeSections:h.settings.activeSections,activeControls:h.settings.activeControls,settingValidities:h.settings.settingValidities}),setInterval(h.keepAliveCurrentUrl,h.settings.timeouts.keepAliveSend),h.preview.bind("loading-initiated",function(){r("body").addClass("wp-customizer-unloading")}),h.preview.bind("loading-failed",function(){r("body").removeClass("wp-customizer-unloading")}),e=r.map(["color","image","preset","position_x","position_y","size","repeat","attachment"],function(e){return"background_"+e}),h.when.apply(h,e).done(function(){r.each(arguments,function(){this.bind(h.settingPreviewHandlers.background)})}),h("custom_logo",function(e){h.settingPreviewHandlers.custom_logo.call(e,e.get()),e.bind(h.settingPreviewHandlers.custom_logo)}),h("custom_css["+h.settings.theme.stylesheet+"]",function(e){e.bind(h.settingPreviewHandlers.custom_css)}),h.trigger("preview-ready"))})}((wp,jQuery));