<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Illuminate\Support\Facades\Auth;

// only allow in local environment
Route::group(['middleware' => ['local']], function () {
	Route::any('/debug', 'DebugController@index');
});

// impersonate user, generate signed URL via tinker - URL::temporarySignedRoute('impersonate', now()->addMinutes(30), ['id' => $id])
Route::get('/impersonate', function() {
	Auth::logout();
	Auth::loginUsingId(request()->get('id'));
	return redirect(route('home'));
})->name('impersonate')->middleware('signed');

// invalidate browser "back" cache
Route::group(['middleware' => ['cache.headers:no_cache;no_store;must_revalidate;max_age=0']], function () {

	// home route
	Route::get('/', function () {

		// check if WordPress draft preview
		if (request('preview')) {

			$controller = app()->make('App\Http\Controllers\WordpressController');

			// post
			if (request('p')) {
				return $controller->callAction('postDraft', [
					'id' => request('p')
				]);
				// page
			} elseif (request('page_id')) {
				return $controller->callAction('pageDraft', [
					'id' => request('page_id')
				]);
			}
		}

		$controller = app()->make('App\Http\Controllers\HomeController');

		return $controller->callAction('index', []);

	})->name('home');

    Route::get('/redirect/to', 'RedirectController@to')->name('redirect.to');

	Route::any('/document/show', 'DocumentController@show')->name('document.show');
	Route::any('/document/create', 'DocumentController@create')->name('document.create');
	Route::any('/document/create/{post}', 'DocumentController@createThroughPost')->name('document.createThroughPost');

	// signed routes
	Route::group(['middleware' => ['signed']], function () {

		// documents
		Route::get('/dokument/{document}/stream', 'DocumentController@stream')->name('document.stream');
		Route::post('/document/preview/html', 'DocumentController@previewHtml')->name('document.preview.html');
		Route::get('/dokument/{document}/pregled', 'DocumentController@previewPdf')->name('document.preview.image');
		Route::get('/dokument/{document}/preuzmi', 'DocumentController@download')->name('document.download');

		// drafts
		Route::get('/uredivac/{draft}/preuzmi', 'EditorController@download')->name('editor.draft.download');

        // examples
        Route::get('/primjeri/{example}/unprotected', 'WordpressController@exampleUnprotected')->name('wordpress.example.unprotected');

		// translations
		Route::get('/prijevod/{translation}/preuzmi', 'TranslationDocumentController@downloadOutput')->name('translation.download');

        // signatures
		Route::get('/stranka/{party}/potpis', 'SignatureController@show')->name('signature.show');
		Route::get('/stranka/{party}/potpis/image', 'SignatureController@image')->name('signature.image');
		Route::post('/party/{party}/signature/save', 'SignatureController@save')->name('signature.save');

		// email subscription
		Route::get('/postavke/obavijesti', 'EmailPreferencesController@show')->name('email.preferences.show');
		Route::post('/email/preferences/store', 'EmailPreferencesController@store')->name('email.preferences.store');

		// email address change
		Route::get('/email/update', 'EmailChangeController@update')->name('email.update');

		// contact files
		Route::get('/contact/{id}/file/{filename}', 'ContactController@getFile')->name('contact.file');

		// order confirmation (can be accessed publicly via email notification)
		Route::get('/prijevod/{translation}/potvrda', 'TranslationController@downloadOrderConfirmation')->name('translation.order.confirmation.download');

		// translation shipment tracking
		Route::get('/prijevod/{translation}/posiljka', 'TranslationController@trackShipment')->name('translation.shipment.track');
	});

	// document routes - restrict to document owners
    Route::group(['middleware' => ['auth', 'verified', 'can:update,document']], function () {
        Route::get('/dokument/{document}/izbrisi', 'DocumentController@delete')->name('document.delete');
        Route::get('/dokument/{document}/potpisi', 'SignatureController@index')->name('signature.index');
        Route::get('/dokument/{document}/posalji', 'EmailController@document')->name('email.document');
        Route::get('/dokument/{document}/dupliciraj', 'DocumentController@duplicate')->name('document.duplicate');
        Route::get('/dokument/{document}/izvezi', 'DocumentController@exportToEditor')->name('document.exportToEditor');
        Route::get('/dokument/{document}/prevedi', 'DocumentController@translate')->name('document.translate');
        Route::get('/dokument/{document}/fork', 'DocumentController@fork')->name('document.fork');
        Route::post('/dokument/{document}/send', 'EmailController@sendDocument')->name('email.sendDocument');
        Route::get('/dokument/{document}/stranka/{party}/zatrazi-potpis', 'SignatureController@request')->name('signature.request');
        Route::post('/dokument/{document}/party/{party}/signature/request', 'SignatureController@requestSend')->name('signature.request.send');
        Route::post('/dokument/{document}/signatureRequest/{request}/revoke', 'SignatureController@requestRevoke')->name('signature.request.revoke');
	    Route::any('/document/{document}/comment', 'DocumentController@comment')->name('document.comment');
    });

	// document section (wizard) routes
	Route::group(['middleware' => ['can:update,document']], function () {
		Route::get('/dokument/{document}/{section}', 'SectionController@show')->name('section.show');
		Route::post('/dokument/{document}/{section}', 'SectionController@store')->name('section.store');
	});

	// logged-in user routes
	Route::group(['middleware' => ['auth', 'verified']], function () {
		Route::get('/moji-dokumenti', 'DocumentController@index')->name('documents');
		Route::get('/profil', 'UserController@dashboard')->name('user.dashboard');
		Route::get('/user/documents', 'DocumentController@dataTables')->name('user.documents');
		Route::get('/user/drafts', 'EditorController@draftsDataTables')->name('user.drafts');
		Route::get('/user/translations', 'TranslationController@dataTables')->name('user.translations');

		// save tutorial shown flags
		Route::post('/user/documents/tutorial', 'UserController@setDocumentTutorialShown')->name('documents.tutorial');
		Route::post('/user/editor/draft/tutorial', 'UserController@setDraftTutorialShown')->name('editor.draft.tutorial');
		Route::post('/user/editor/tutorial', 'UserController@setEditorTutorialShown')->name('editor.tutorial');
		Route::post('/user/translation/tutorial', 'UserController@setTranslationTutorialShown')->name('translation.tutorial');

		Route::get('uredi/podatke', 'PersonalDataController@show')->name('account.personal.data');
		Route::post('uredi/podatke', 'PersonalDataController@update')->name('account.personal.data.update');
		Route::post('account/delete', 'UserController@deleteAccount')->name('account.delete');

		// editor routes
		Route::group(['middleware' => ['can:update,draft']], function () {
			Route::get('/uredivac/{draft}', 'EditorController@edit')->name('editor.draft.edit');
			Route::post('/editor/{draft}/save', 'EditorController@save')->name('editor.draft.save');
			Route::get('/uredivac/{draft}/izbrisi', 'EditorController@delete')->name('editor.draft.delete');
			Route::any('/editor/{draft}/comment', 'EditorController@comment')->name('editor.draft.comment');
			Route::any('/editor/{draft}/title', 'EditorController@title')->name('editor.draft.title');
			Route::get('/editor/{draft}/stream', 'EditorController@stream')->name('editor.draft.stream');
			Route::get('/uredivac/{draft}/dupliciraj', 'EditorController@duplicate')->name('editor.draft.duplicate');
			Route::get('/uredivac/dokument/{draft}/posalji', 'EmailController@draft')->name('editor.draft.email');
			Route::get('/uredivac/dokument/{draft}/prevedi', 'EditorController@translate')->name('editor.draft.translate');
			Route::post('/editor/{draft}/send', 'EmailController@sendDraft')->name('editor.draft.send');
		});

		Route::get('/editor/{post}/articles/search', 'EditorController@articlesDataTables')->name('editor.articles');
		Route::post('/editor/spellcheck', 'EditorController@spellCheck')->name('editor.spellcheck')->middleware('throttle:30,1');

		// translation routes
		Route::group(['middleware' => ['can:update,translation']], function () {
            Route::get('/prijevod/{translation}/dokumenti/preuzmi', 'TranslationDocumentController@downloadInput')->name('translation.download.input');
            Route::get('/prijevod/{translation}/posalji', 'EmailController@translation')->name('email.translation');
            Route::post('/prijevod/{translation}/send', 'EmailController@sendTranslation')->name('email.sendTranslation');
			Route::get('/prijevod/{translation}', 'TranslationController@show')->name('translation.show');
			Route::get('/prijevod/{translation}/uredi', 'TranslationController@edit')->name('translation.edit');
			Route::get('/translation/{translation}/reinvoice', 'TranslationController@requestReinvoice')->name('translation.request.reinvoice');
			Route::post('/translation/{translation}/update', 'TranslationController@update')->name('translation.update');
			Route::get('/prijevod/naruci/{translation}', 'TranslationController@checkout')->name('translation.checkout');
			Route::get('/prijevod/{translation}/otkazi', 'TranslationController@cancel')->name('translation.cancel');
			Route::get('/prijevod/{translation}/racun', 'TranslationController@downloadReceipt')->name('translation.receipt.download');
			Route::get('/prijevod/{translation}/storno/racun', 'TranslationController@downloadStornoReceipt')->name('translation.storno.receipt.download');
		});

        // translation document routes
        Route::group(['middleware' => ['can:update,document']], function () {
            Route::get('/prijevod/dokument/{document}/preuzmi', 'TranslationDocumentController@download')->name('translation.download.document');
            Route::get('/prijevod/dokument/{document}/preuzmi/prijevod', 'TranslationDocumentController@downloadDocumentOutput')->name('translation.download.document.output');
        });

		// translation invoice routes
		Route::group(['middleware' => ['can:update,invoice']], function () {
			Route::post('/translation/invoice/{invoice}/pay', 'TranslationInvoiceController@pay')->name('translation.invoice.pay');
			Route::get('/prijevod/ponuda/{invoice}/preuzmi', 'TranslationInvoiceController@download')->name('translation.invoice.download');
		});

        Route::post('/translation/process', 'TranslationController@process')->name('translation.process');

        // admin routes
		Route::group(['middleware' => ['admin']], function () {
			// Admin Dashboard
			Route::get('/admin', 'Admin\AdminController@dashboard')->name('admin.dashboard');

			if (app()->environment(['local', 'staging'])) {
				Route::get('/admin/document-ai', 'Admin\DocumentAIController@index')->name('admin.document-ai.index');
				Route::post('/admin/document-ai/process', 'Admin\DocumentAIController@process')->name('admin.document-ai.process');
				Route::get('/admin/document-ai/success', 'Admin\DocumentAIController@success')->name('admin.document-ai.success');
				Route::post('/admin/document-ai/restore-backups', 'Admin\DocumentAIController@restoreBackups')->name('admin.document-ai.restore-backups');
				Route::post('/admin/document-ai/check-backups', 'Admin\DocumentAIController@checkBackups')->name('admin.document-ai.check-backups');
			}

			Route::get('/uredivac/{draft}/dupliciraj/admin', 'EditorController@duplicateAdmin')->name('editor.draft.duplicate.admin');
			Route::get('/dokument/{document}/dupliciraj/admin', 'DocumentController@duplicateAdmin')->name('document.duplicate.admin');
			Route::get('/uredivac/{draft}/preuzmi/admin', 'EditorController@downloadAdmin')->name('editor.draft.download.admin');
			Route::get('/prijevod/{translation}/preuzmi/dokumenti/admin', 'TranslationDocumentController@downloadInputAdmin')->name('translation.download.input.admin');
			Route::post('/translation/{translation}/upload', 'TranslationDocumentController@uploadOutput')->name('translation.upload');
			Route::post('/translation/{translation}/upload/edit', 'TranslationDocumentController@editOutput')->name('translation.upload.edit');
			Route::get('/prijevod/{translation}/admin', 'TranslationController@show')->name('translation.show.admin');
			Route::get('/prijevod/{translation}/autoriziraj', 'TranslationController@authorizePayment')->name('translation.payment.authorize');
			Route::post('/translation/{translation}/reject', 'TranslationController@reject')->name('translation.reject');
			Route::post('/prijevod/{translation}/void', 'TranslationController@void')->name('translation.void');
            Route::post('/translation/{translation}/ship', 'TranslationController@ship')->name('translation.ship');
            Route::post('/translation/{translation}/shipment/edit', 'TranslationController@editShipment')->name('translation.shipment.edit');
            Route::get('/prijevod/narudzba/{translation}/dostavljeno', 'TranslationController@deliver')->name('translation.delivered');
            Route::post('/translation/{translation}/invoice', 'TranslationInvoiceController@create')->name('translation.invoice.create');
            Route::post('/translation/{translation}/receipt/recreate', 'TranslationController@recreateReceipt')->name('translation.receipt.recreate');
			Route::get('/prijevod/{translation}/izbrisi', 'TranslationController@delete')->name('translation.delete');
		});

	});

	// custom translation
	Route::get('/prijevod', 'TranslationController@customCheckout')->name('translation.custom.checkout');
    Route::post('/translation/process/custom', 'TranslationController@processCustomCheckout')->name('translation.process.custom');

	// unauthenticated user tutorial routes
	Route::post('/user/wizard/tutorial', 'SectionController@setWizardTutorialShown')->name('wizard.tutorial');

	// WordPress documents routes
	Route::get('/obrasci', 'WordpressController@documents')->name('wordpress.documents')->middleware('cacheResponse:wordpress-document');

	// WordPress examples routes
	Route::get('/primjeri', 'WordpressController@examples')->name('wordpress.examples')->middleware('cacheResponse:wordpress-example');
	Route::get('/primjeri/{slug}', 'WordpressController@example')->name('wordpress.example')->middleware('cacheResponse:wordpress-example');

	Route::get('/primjeri/{id}/export', 'WordpressController@exportExample')->name('wordpress.example.export')->middleware(
		'authWithMessage:Prijavite se ili <a href="/register">besplatno napravite račun.</a>,1'
	);

	// WordPress articles routes
	Route::get('/strucni-clanci', 'WordpressController@posts')->name('wordpress.posts')->middleware('cacheResponse:wordpress-post');
	Route::get('/strucni-clanci/{slug}', 'WordpressController@post')->name('wordpress.post')->middleware('cacheResponse:wordpress-post');

	// draft preview route
	Route::get('/page/draft', 'WordpressController@pageDraft')->name('wordpress.page.draft');
	Route::get('/post/draft', 'WordpressController@postDraft')->name('wordpress.post.draft');

	// template download
	Route::get('/predlozak/{template}', function ($template = null) {

		$path = storage_path() . '/templates/' . $template;
		if (file_exists($path)) {
			return Response::download($path);
		}

		abort(404);
	})->name('template.download');

	// search routes
	Route::post('/template/search', 'SearchController@templates')->name('search.templates');
	Route::post('/backend/search', 'SearchController@wordpress')->name('wordpress.search');

	// API routes
	Route::get('/template/tags/sync', 'ApiController@syncTemplateTags')->name('api.template.tags.sync');
	Route::get('/example/images/delete', 'ApiController@exampleImagesDelete')->name('api.example.images.delete');
	Route::get('/es/sync', 'ApiController@syncElasticSearch')->name('api.es.sync');
	Route::get('/cache/clear', 'ApiController@clearCache')->name('api.cache.clear');

	// throttled routes
	Route::group(['middleware' => ['throttle']], function () {

		// login/registration routes
		Auth::routes(['verify' => true]);

		// SocialAuthController routes
		Route::get('login/{provider}', 'Auth\SocialAuthController@redirectToProvider')->name('auth.social.login');
		Route::get('callback/{provider}', 'Auth\SocialAuthController@handleProviderCallback');

		// contact routes
		Route::get('/kontakt', 'ContactController@index')->name('contact');
		Route::post('/kontakt', 'ContactController@store')->name('contact.store')->middleware(\Spatie\Honeypot\ProtectAgainstSpam::class);

	});

	// feedback
	Route::get('/feedback', 'FeedbackController@index')->name('feedback');
	Route::post('/feedback', 'FeedbackController@store')->name('feedback.post');
	Route::get('/feedback/facebook', 'FeedbackController@facebook')->name('feedback.facebook');

	// javascript error reporting
	Route::post('/jserror', function () {

		return true; // disable error reporting

		$errorData = request()->all();
		$error = json_encode($errorData);

		// List of predefined errors to ignore
		$ignored_errors = [
			"ReferenceError: Can't find variable: gmo",
		];

		// Check if errorMsg matches any ignored error
		if (!empty($errorData['errorMsg'])) {
			foreach ($ignored_errors as $_ignored_error) {
				if (str_contains($errorData['errorMsg'], $_ignored_error)) {
					return false;
				}
			}
		}

		// Check if the error originated from an external script
		$script_url = request()->get('scriptURL');
		$is_external_script = $script_url && !str($script_url)->contains(request()->getHost());
		$is_generic_error = !$script_url;

		// In production, ignore errors from crawlers and external scripts
		if (app()->environment('production')) {
			$cd = new \Jaybizzle\CrawlerDetect\CrawlerDetect();

			if ($cd->isCrawler() || $is_external_script || $is_generic_error) {
				return false;
			}
		}

		// Log or throw exception for other cases
		throw new \Exception("Error in JavaScript: $error");
	});



	// save cookie preferences
	Route::post('/cookiePreferences', 'UserController@setCookiePreferences')->name('cookie.preferences');
	Route::post('/documentPreviewSetting', 'UserController@documentPreviewSetting')->name('document.preview.setting');

	// old route redirects
	Route::redirect('/moji-ugovori', '/', 301);
	Route::redirect('/blog/{slug}', '/strucni-clanci/{slug}', 301);

	// other slugs (must be placed at the end of this file)
	Route::get('{slug}', 'WordpressController@page')->name('wordpress.page')->middleware('cacheResponse:wordpress-page,wordpress-document');
});
