<?php

namespace Tests\Browser;

use App\Helpers\Post;
use Illuminate\Support\Facades\App;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

/**
 * Class CanRenderWordpressPagesTest
 *
 * Test rendering of WordPress pages (documents, examples, posts)
 *
 * @package Tests\Browser
 */
class WordpressPagesTest extends DuskTestCase {

	private function testType(Browser $browser, string $type, string $prefix = "") {
		$posts = Post::type($type)->status('publish')->get();

		foreach ($posts as $_post) {
			$url = '/' . (!empty($prefix) ? $prefix . '/' : '') . $_post->slug;
			$browser->visit($url)->assertSee($_post->title);
		}
	}

	public function testDocuments() {
		$this->browse(function (Browser $browser) {
			$this->testType($browser, 'document');
		});
	}

	public function testExamples() {
		$this->browse(function (Browser $browser) {
			$this->testType($browser, 'example', 'primjeri');
		});
	}

	public function testPosts() {
		$this->browse(function (Browser $browser) {
			$this->testType($browser, 'post', 'strucni-clanci');
		});
	}
}
