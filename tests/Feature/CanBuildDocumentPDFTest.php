<?php

namespace Tests\Feature;

use App\Helpers\DocumentBuilder;
use App\Models\Document;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

/**
 *
 * All existing documents must be able to render PDFs without error
 *
 * @package Tests\Feature
 */
class CanBuildDocumentPDFTest extends TestCase
{
    public function test()
    {
	    $document_model = new Document();
	    $document_model->setConnection('mysql_production');

		$_doc_ref = null;

	    try{

			// check documents updated within given scope of days
			$scope_days = 30;

            // could be lots of models, so grab ids instead and fetch models one by one
		    $documents = DB::connection('mysql_production')
		                   ->table('documents')
		                   ->select('id')
		                   ->whereNotNull('user_id')
		                   ->whereNull('deleted_at')
		                   ->where('is_visible', '=', 1)
		                   ->where('updated_at', '>=', \Carbon\Carbon::now()->subDays($scope_days)->toDateTimeString())
		                   ->get();


		    foreach($documents as $_document){
				$_doc_ref = $_document->id;
			    $_document_to_test = $document_model->find($_document->id);
			    $builder = new DocumentBuilder($_document_to_test);
			    $builder->loadTemplate();
		    }

	    }
	    catch(\Exception $e)
	    {
		    $this->fail("ID $_doc_ref: " .$e->getMessage());
	    }

        $this->assertTrue(true);
    }
}
