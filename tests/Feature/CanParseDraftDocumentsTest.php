<?php

namespace Tests\Feature;

use App\Helpers\DocumentParser;
use App\Models\DocumentDraft;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

/**
 *
 * All existing draft documents must be able to be parsed for editor
 *
 * @package Tests\Feature
 */
class CanParseDraftDocumentsTest extends TestCase
{
    public function test()
    {
	    $document_model = new DocumentDraft();
	    $document_model->setConnection('mysql_production');

	    try{

			// check documents updated within given scope of days
			$scope_days = 30;

            // could be lots of models, so grab ids instead and fetch models one by one
		    $documents = DB::connection('mysql_production')
		                   ->table('document_drafts')
		                   ->select('id')
		                   ->whereNull('deleted_at')
		                   ->where('is_visible', '=', 1)
		                   ->where('updated_at', '>=', \Carbon\Carbon::now()->subDays($scope_days)->toDateTimeString())
		                   ->get();


		    foreach($documents as $_document){
			    $_document_to_test = $document_model->find($_document->id);

                $parser = new DocumentParser(
                    $_document_to_test,
                    $_document_to_test->type_id
                );
		    }

	    }
	    catch(\Exception $e)
	    {
		    $this->fail($e->getMessage());
	    }

        $this->assertTrue(true);
    }
}
